import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
	DataService,
	ModalService,
	NotificationService,
	SharedModule,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { BehaviorSubject, Observable } from 'rxjs';
import { finalize, map, switchMap } from 'rxjs/operators';

export const getDecalDetailDocument = gql`
	query GetDecalDetail($id: ID!) {
		decal(id: $id) {
			id
			createdAt
			updatedAt
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
				source
			}
		}
	}
`;

const CREATE_DECAL = gql`
	mutation CreateDecal($input: CreateDecalInput!) {
		createDecal(input: $input) {
			id
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
			}
		}
	}
`;

const UPDATE_DECAL = gql`
	mutation UpdateDecal($input: UpdateDecalInput!) {
		updateDecal(input: $input) {
			id
			name
			description
			category
			isActive
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				name
				preview
			}
		}
	}
`;

const DELETE_DECAL = gql`
	mutation DeleteDecal($id: ID!) {
		deleteDecal(id: $id) {
			result
			message
		}
	}
`;

const GET_ASSET = gql`
	query GetAsset($id: ID!) {
		asset(id: $id) {
			id
			name
			source
			preview
		}
	}
`;

@Component({
	selector: 'decal-detail',
	templateUrl: './decal-detail.component.html',
	styleUrls: ['./decal-detail.component.scss'],
	changeDetection: ChangeDetectionStrategy.Default,
	standalone: true,
	imports: [SharedModule],
})
export class DecalDetailComponent implements OnInit {
	detailForm: any;
	entity$: Observable<any>;
	isNew$: Observable<boolean>;
	loading$ = new BehaviorSubject<boolean>(false);
	saving$ = new BehaviorSubject<boolean>(false);
	imageError = false;
	currentAsset: any = null;

	constructor(
		private formBuilder: FormBuilder,
		private notificationService: NotificationService,
		private dataService: DataService,
		private route: ActivatedRoute,
		private router: Router,
		private modalService: ModalService
	) {
		this.detailForm = this.formBuilder.group({
			name: ['', Validators.required],
			description: [''],
			category: ['', Validators.required],
			assetId: ['', Validators.required],
			isActive: [true],
			maxWidth: [100, [Validators.required, Validators.min(1)]],
			maxHeight: [100, [Validators.required, Validators.min(1)]],
			minScale: [0.5, [Validators.required, Validators.min(0.1)]],
			maxScale: [2.0, [Validators.required, Validators.min(0.1)]],
		});
	}

	ngOnInit() {
		this.isNew$ = this.route.paramMap.pipe(map((params) => params.get('id') === 'create'));

		this.entity$ = this.route.paramMap.pipe(
			switchMap((params) => {
				const id = params.get('id');
				if (id && id !== 'create') {
					return this.dataService.query(getDecalDetailDocument, { id }).stream$;
				}
				return new Observable((observer) => observer.next(null));
			})
		);

		// Subscribe to entity changes to populate form
		this.entity$.subscribe((result) => {
			if (result?.decal) {
				this.setFormValues(result.decal);
			}
		});
	}

	create() {
		console.log('=== CREATE METHOD CALLED ===');

		// Check if form exists
		if (!this.detailForm) {
			console.error('Form is not initialized!');
			this.notificationService.error('Form is not initialized');
			return;
		}

		const formValue = this.detailForm.value;
		console.log('Create form value:', formValue);
		console.log('Form valid:', this.detailForm.valid);
		console.log('Form errors:', this.getFormErrors());
		console.log('Form status:', this.detailForm.status);
		console.log('Form disabled:', this.detailForm.disabled);

		// Check each required field individually
		const nameControl = this.detailForm.get('name');
		const categoryControl = this.detailForm.get('category');
		const assetIdControl = this.detailForm.get('assetId');

		console.log('Name control:', {
			value: nameControl?.value,
			valid: nameControl?.valid,
			errors: nameControl?.errors,
			touched: nameControl?.touched,
			dirty: nameControl?.dirty,
		});
		console.log('Category control:', {
			value: categoryControl?.value,
			valid: categoryControl?.valid,
			errors: categoryControl?.errors,
			touched: categoryControl?.touched,
			dirty: categoryControl?.dirty,
		});
		console.log('AssetId control:', {
			value: assetIdControl?.value,
			valid: assetIdControl?.valid,
			errors: assetIdControl?.errors,
			touched: assetIdControl?.touched,
			dirty: assetIdControl?.dirty,
		});

		// Validate required fields
		if (!formValue.name || !formValue.category || !formValue.assetId) {
			console.log('Missing required fields:', {
				name: formValue.name,
				category: formValue.category,
				assetId: formValue.assetId,
			});
			this.notificationService.error(
				'Please fill in all required fields: Name, Category, and Asset ID'
			);
			return;
		}

		// Check if already saving
		if (this.saving$.value) {
			console.log('Already saving, ignoring duplicate request');
			return;
		}

		console.log('Starting mutation...');
		this.saving$.next(true);

		const mutationInput = {
			name: formValue.name,
			description: formValue.description || '',
			category: formValue.category,
			assetId: formValue.assetId,
			maxWidth: formValue.maxWidth || 100,
			maxHeight: formValue.maxHeight || 100,
			minScale: formValue.minScale || 0.5,
			maxScale: formValue.maxScale || 2.0,
		};

		console.log('Mutation input:', mutationInput);

		this.dataService
			.mutate(CREATE_DECAL, { input: mutationInput })
			.pipe(
				finalize(() => {
					console.log('Mutation completed, setting saving to false');
					this.saving$.next(false);
				})
			)
			.subscribe({
				next: (result: any) => {
					console.log('Mutation success result:', result);
					if (result.createDecal?.id) {
						this.notificationService.success('Decal created successfully');
						console.log('Navigating to:', ['extensions', 'decal', result.createDecal.id]);
						this.router.navigate(['extensions', 'decal', result.createDecal.id]);
					} else {
						console.error('No ID returned from mutation:', result);
						this.notificationService.error('Decal created but no ID returned');
					}
				},
				error: (error: any) => {
					console.error('Mutation error:', error);
					console.error('Error details:', {
						message: error.message,
						graphQLErrors: error.graphQLErrors,
						networkError: error.networkError,
						extraInfo: error.extraInfo,
					});
					this.notificationService.error(
						'Error creating decal: ' + (error.message || 'Unknown error')
					);
				},
			});
	}

	update() {
		const formValue = this.detailForm.value;

		// Get current ID from route
		const currentId = this.route.snapshot.paramMap.get('id');
		if (!currentId || currentId === 'create') {
			return;
		}

		this.saving$.next(true);
		this.dataService
			.mutate(UPDATE_DECAL, {
				input: {
					id: currentId,
					name: formValue.name,
					description: formValue.description,
					category: formValue.category,
					assetId: formValue.assetId,
					isActive: formValue.isActive,
					maxWidth: formValue.maxWidth,
					maxHeight: formValue.maxHeight,
					minScale: formValue.minScale,
					maxScale: formValue.maxScale,
				},
			})
			.pipe(finalize(() => this.saving$.next(false)))
			.subscribe({
				next: () => {
					this.notificationService.success('Decal updated successfully');
					this.detailForm.markAsPristine();
				},
				error: (error: any) => {
					this.notificationService.error(
						'Error updating decal: ' + (error.message || 'Unknown error')
					);
				},
			});
	}

	async delete() {
		const currentId = this.route.snapshot.paramMap.get('id');
		if (!currentId || currentId === 'create') {
			return;
		}

		const entity = await this.entity$.pipe(map((result) => result?.decal)).toPromise();
		if (!entity) {
			return;
		}

		const result = await this.modalService
			.dialog({
				title: 'Delete Decal',
				body: `Are you sure you want to delete "${entity.name}"? This action cannot be undone.`,
				buttons: [
					{ type: 'secondary', label: 'Cancel' },
					{ type: 'danger', label: 'Delete', returnValue: true },
				],
			})
			.pipe(map((res) => res))
			.toPromise();

		if (result) {
			this.saving$.next(true);
			this.dataService
				.mutate(DELETE_DECAL, { id: currentId })
				.pipe(finalize(() => this.saving$.next(false)))
				.subscribe({
					next: (deleteResult: any) => {
						if (deleteResult.deleteDecal?.result === 'DELETED') {
							this.notificationService.success('Decal deleted successfully');
							this.router.navigate(['extensions', 'decal']);
						} else {
							this.notificationService.error('Failed to delete decal');
						}
					},
					error: (error: any) => {
						this.notificationService.error(
							'Error deleting decal: ' + (error.message || 'Unknown error')
						);
					},
				});
		}
	}

	getAssetUrl(assetId: string): string {
		if (!assetId) {
			return '';
		}

		// If we have the current asset data, use its preview URL
		if (this.currentAsset && this.currentAsset.id === assetId) {
			return this.currentAsset.preview || this.currentAsset.source || '';
		}

		// Fallback to constructed URL (though this might not work)
		return `/admin-api/assets/${assetId}?preset=medium`;
	}

	fetchAssetData(assetId: string): void {
		if (!assetId) {
			this.currentAsset = null;
			return;
		}

		this.dataService.query(GET_ASSET, { id: assetId }).stream$.subscribe({
			next: (result: any) => {
				this.currentAsset = result.asset;
				this.imageError = false;
			},
			error: (error: any) => {
				console.error('Failed to fetch asset:', error);
				this.currentAsset = null;
				this.imageError = true;
			},
		});
	}

	onImageError(event: any): void {
		console.error('Failed to load asset image:', event);
		this.imageError = true;
		event.target.style.display = 'none';

		// Try alternative URL format
		const assetId = this.detailForm.get('assetId')?.value;
		if (assetId && event.target.src.includes('/admin-api/assets/')) {
			// Try without admin-api prefix
			event.target.src = `/assets/${assetId}?preset=medium`;
		}
	}

	onImageLoad(event: any): void {
		this.imageError = false;
		event.target.style.display = 'block';
	}

	onAssetIdChange(event: any): void {
		const value = event.target.value;
		this.detailForm.get('assetId')?.setValue(value);
		this.detailForm.get('assetId')?.markAsDirty();
		this.imageError = false; // Reset error state when changing asset ID

		// Fetch asset data when ID changes
		if (value) {
			this.fetchAssetData(value);
		} else {
			this.currentAsset = null;
		}
	}

	openAssetManager(): void {
		// Open asset manager in a new tab
		window.open('/admin/catalog/assets', '_blank');
	}

	getFormErrors(): any {
		const errors: any = {};
		Object.keys(this.detailForm.controls).forEach((key) => {
			const control = this.detailForm.get(key);
			if (control && control.errors) {
				errors[key] = control.errors;
			}
		});
		return errors;
	}

	// Test method to verify button clicks are working
	testButtonClick(): void {
		console.log('TEST: Button click detected!');
		alert('Button click is working!');
	}

	private setFormValues(entity: any): void {
		this.detailForm.patchValue({
			name: entity.name,
			description: entity.description,
			category: entity.category,
			assetId: entity.asset?.id,
			isActive: entity.isActive,
			maxWidth: entity.maxWidth,
			maxHeight: entity.maxHeight,
			minScale: entity.minScale,
			maxScale: entity.maxScale,
		});

		// Reset image error state when loading new entity
		this.imageError = false;

		// Fetch asset data if available
		if (entity.asset?.id) {
			this.fetchAssetData(entity.asset.id);
		} else {
			this.currentAsset = null;
		}
	}
}
