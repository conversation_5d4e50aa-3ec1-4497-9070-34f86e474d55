<!-- Action bar -->
<vdr-page-block>
	<vdr-action-bar>
		<vdr-ab-left>
			<div *ngIf="entity$ | async as result">
				<vdr-entity-info *ngIf="result?.decal" [entity]="result.decal"></vdr-entity-info>
			</div>
		</vdr-ab-left>
		<vdr-ab-right>
			<div class="action-buttons">
				<!-- Test button to verify click events work -->
				<button
					class="btn btn-secondary"
					(click)="testButtonClick()"
					type="button"
					style="margin-right: 8px;"
				>
					Test Click
				</button>

				<button
					class="btn btn-danger"
					*ngIf="!(isNew$ | async) && (entity$ | async)?.decal"
					(click)="delete()"
					[disabled]="saving$ | async"
				>
					<clr-icon shape="trash"></clr-icon>
					Delete
				</button>
				<button
					class="btn btn-primary"
					*ngIf="isNew$ | async; else updateButton"
					(click)="create()"
					[disabled]="detailForm.invalid || (saving$ | async)"
					type="button"
				>
					<clr-icon *ngIf="saving$ | async" shape="spinner" class="spinning"></clr-icon>
					<clr-icon *ngIf="!(saving$ | async)" shape="plus"></clr-icon>
					{{ (saving$ | async) ? 'Creating...' : 'Create' }}
				</button>
				<ng-template #updateButton>
					<button
						class="btn btn-primary"
						(click)="update()"
						[disabled]="detailForm.invalid || (saving$ | async)"
					>
						<clr-icon *ngIf="saving$ | async" shape="spinner" class="spinning"></clr-icon>
						<clr-icon *ngIf="!(saving$ | async)" shape="check"></clr-icon>
						{{ (saving$ | async) ? 'Saving...' : 'Save' }}
					</button>
				</ng-template>
			</div>
		</vdr-ab-right>
	</vdr-action-bar>
</vdr-page-block>

<!-- Loading overlay -->
<div *ngIf="saving$ | async" class="loading-overlay">
	<div class="loading-content">
		<clr-icon shape="spinner" class="spinning"></clr-icon>
		<span>{{ (isNew$ | async) ? 'Creating decal...' : 'Saving changes...' }}</span>
	</div>
</div>

<!-- Form content -->
<form class="form" [formGroup]="detailForm" [class.loading-disabled]="saving$ | async">
	<vdr-page-detail-layout>
		<!-- Sidebar -->
		<vdr-page-detail-sidebar>
			<div *ngIf="entity$ | async as result">
				<vdr-card *ngIf="result?.decal">
					<vdr-page-entity-info [entity]="result.decal" />
				</vdr-card>
			</div>

			<!-- Asset preview -->
			<vdr-card title="Image Preview">
				<div class="asset-preview-large">
					<div *ngIf="detailForm.get('assetId')?.value; else noImagePlaceholder">
						<div class="asset-preview-container">
							<div *ngIf="currentAsset && !imageError; else errorOrLoading">
								<img
									[src]="currentAsset.preview || currentAsset.source"
									[alt]="currentAsset.name || 'Decal preview'"
									class="asset-preview-img"
									(error)="onImageError($event)"
									(load)="onImageLoad($event)"
								/>
							</div>
							<ng-template #errorOrLoading>
								<div class="image-placeholder">
									<div *ngIf="imageError" class="image-error">
										<clr-icon shape="exclamation-triangle"></clr-icon>
										<p>Failed to load asset</p>
										<small>Asset ID: {{ detailForm.get('assetId')?.value }}</small>
									</div>
									<div *ngIf="!imageError && !currentAsset" class="loading-asset">
										<clr-icon shape="spinner" class="spinning"></clr-icon>
										<p>Loading asset...</p>
									</div>
								</div>
							</ng-template>
						</div>
						<div class="preview-info">
							<p class="preview-label">{{ currentAsset?.name || 'Asset Preview' }}</p>
							<small class="preview-id">ID: {{ detailForm.get('assetId')?.value }}</small>
						</div>
					</div>
					<ng-template #noImagePlaceholder>
						<div class="no-image-placeholder">
							<clr-icon shape="image" size="48"></clr-icon>
							<p>No image selected</p>
							<small>Upload an image using the asset picker below</small>
						</div>
					</ng-template>
				</div>
			</vdr-card>
		</vdr-page-detail-sidebar>

		<!-- Main content -->
		<vdr-page-block>
			<!-- Basic Information -->
			<vdr-card title="Basic Information">
				<div class="form-grid">
					<vdr-form-field label="Name" for="name">
						<input
							id="name"
							type="text"
							formControlName="name"
							[class.is-invalid]="detailForm.get('name')?.invalid && detailForm.get('name')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('name')?.hasError('required')"
						>
							Name is required
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Category" for="category">
						<input
							id="category"
							type="text"
							formControlName="category"
							[class.is-invalid]="detailForm.get('category')?.invalid && detailForm.get('category')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('category')?.hasError('required')"
						>
							Category is required
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Description" for="description" class="full-width">
						<textarea
							id="description"
							formControlName="description"
							rows="3"
						></textarea>
					</vdr-form-field>

					<vdr-form-field label="Active" for="isActive">
						<clr-toggle-wrapper>
							<input
								type="checkbox"
								clrToggle
								id="isActive"
								formControlName="isActive"
							/>
							<label for="isActive">Active</label>
						</clr-toggle-wrapper>
					</vdr-form-field>
				</div>
			</vdr-card>

			<!-- Asset Selection -->
			<vdr-card title="Decal Image">
				<div class="form-grid">
					<vdr-form-field label="Image" for="assetId" class="full-width">
						<div class="asset-upload-section">
							<input
								type="hidden"
								formControlName="assetId"
								id="assetId"
							/>
							<div class="asset-picker-placeholder">
								<p><strong>Step 1:</strong> Upload your image to Vendure Assets</p>
								<p><strong>Step 2:</strong> Copy the Asset ID and paste it below</p>
								<p>Current Asset ID: <code>{{ detailForm.get('assetId')?.value || 'None selected' }}</code></p>
								<input
									type="text"
									placeholder="Enter Asset ID (e.g., 123 or filename.jpg)"
									[value]="detailForm.get('assetId')?.value"
									(input)="onAssetIdChange($event)"
									class="asset-id-input"
								/>
								<button
									type="button"
									class="btn btn-sm btn-secondary asset-manager-btn"
									(click)="openAssetManager()"
								>
									<clr-icon shape="folder-open"></clr-icon>
									Open Asset Manager
								</button>
							</div>
							<div class="upload-help-text">
								<p><strong>How to get Asset ID:</strong></p>
								<ol>
									<li>Go to <strong>Catalog → Assets</strong> in a new tab</li>
									<li>Upload your decal image (PNG, JPG, SVG)</li>
									<li>Click on the uploaded asset to view details</li>
									<li>Copy the Asset ID or filename</li>
									<li>Paste it in the field above</li>
								</ol>
								<p><em>Tip: High-resolution images with transparent backgrounds work best for decals.</em></p>
							</div>
						</div>
						<vdr-form-field-error
							*ngIf="detailForm.get('assetId')?.hasError('required')"
						>
							Image is required
						</vdr-form-field-error>
					</vdr-form-field>
				</div>
			</vdr-card>

			<!-- Dimensions & Scaling -->
			<vdr-card title="Dimensions & Scaling">
				<div class="form-grid">
					<vdr-form-field label="Max Width" for="maxWidth">
						<input
							id="maxWidth"
							type="number"
							formControlName="maxWidth"
							min="1"
							step="0.1"
							[class.is-invalid]="detailForm.get('maxWidth')?.invalid && detailForm.get('maxWidth')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxWidth')?.hasError('required')"
						>
							Max width is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxWidth')?.hasError('min')"
						>
							Max width must be at least 1
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Max Height" for="maxHeight">
						<input
							id="maxHeight"
							type="number"
							formControlName="maxHeight"
							min="1"
							step="0.1"
							[class.is-invalid]="detailForm.get('maxHeight')?.invalid && detailForm.get('maxHeight')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxHeight')?.hasError('required')"
						>
							Max height is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxHeight')?.hasError('min')"
						>
							Max height must be at least 1
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Min Scale" for="minScale">
						<input
							id="minScale"
							type="number"
							formControlName="minScale"
							min="0.1"
							step="0.1"
							[class.is-invalid]="detailForm.get('minScale')?.invalid && detailForm.get('minScale')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('minScale')?.hasError('required')"
						>
							Min scale is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('minScale')?.hasError('min')"
						>
							Min scale must be at least 0.1
						</vdr-form-field-error>
					</vdr-form-field>

					<vdr-form-field label="Max Scale" for="maxScale">
						<input
							id="maxScale"
							type="number"
							formControlName="maxScale"
							min="0.1"
							step="0.1"
							[class.is-invalid]="detailForm.get('maxScale')?.invalid && detailForm.get('maxScale')?.touched"
						/>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxScale')?.hasError('required')"
						>
							Max scale is required
						</vdr-form-field-error>
						<vdr-form-field-error
							*ngIf="detailForm.get('maxScale')?.hasError('min')"
						>
							Max scale must be at least 0.1
						</vdr-form-field-error>
					</vdr-form-field>
				</div>
			</vdr-card>
		</vdr-page-block>
	</vdr-page-detail-layout>
</form>
	<!-- Debug Information (remove in production) -->
	<div class="debug-info" style="margin-top: 20px; padding: 16px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;">
		<h4>Debug Information:</h4>
		<p><strong>Form Valid:</strong> {{ detailForm.valid }}</p>
		<p><strong>Form Dirty:</strong> {{ detailForm.dirty }}</p>
		<p><strong>Form Pristine:</strong> {{ detailForm.pristine }}</p>
		<p><strong>Current Asset:</strong> {{ currentAsset?.name || 'None' }}</p>
		<p><strong>Image Error:</strong> {{ imageError }}</p>
		<p><strong>Form Values:</strong></p>
		<pre>{{ detailForm.value | json }}</pre>
		<p><strong>Form Errors:</strong></p>
		<pre>{{ getFormErrors() | json }}</pre>
	</div>
</div>
