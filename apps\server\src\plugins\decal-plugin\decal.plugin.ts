import { PluginCommonModule, VendurePlugin } from '@vendure/core';
import { AdminUiExtension } from '@vendure/ui-devkit/compiler';
import gql from 'graphql-tag';
import * as path from 'path';
import { Decal } from './decal.entity';
import { DecalAdminResolver, DecalShopResolver } from './decal.resolver';
import { DecalService } from './decal.service';
const adminApiExtensions = {
	resolvers: [DecalAdminResolver],
	schema: gql`
		type Decal implements Node {
			id: ID!
			createdAt: DateTime!
			updatedAt: DateTime!
			name: String!
			description: String!
			asset: Asset!
			category: String!
			isActive: Boolean!
			maxWidth: Float!
			maxHeight: Float!
			minScale: Float!
			maxScale: Float!
		}

		type DecalList implements PaginatedList {
			items: [Decal!]!
			totalItems: Int!
		}

		input CreateDecalInput {
			name: String!
			description: String!
			assetId: ID!
			category: String!
			maxWidth: Float
			maxHeight: Float
			minScale: Float
			maxScale: Float
		}

		input UpdateDecalInput {
			id: ID!
			name: String
			description: String
			assetId: ID
			category: String
			isActive: Boolean
			maxWidth: Float
			maxHeight: Float
			minScale: Float
			maxScale: Float
		}

		extend type Query {
			decals: DecalList!
			decal(id: ID!): Decal
		}

		extend type Mutation {
			createDecal(input: CreateDecalInput!): Decal!
			updateDecal(input: UpdateDecalInput!): Decal!
			deleteDecal(id: ID!): DeletionResponse!
		}
	`,
};

const shopApiExtensions = {
	resolvers: [DecalShopResolver],
	schema: gql`
		type Decal implements Node {
			id: ID!
			createdAt: DateTime!
			updatedAt: DateTime!
			name: String!
			description: String!
			asset: Asset!
			category: String!
			maxWidth: Float!
			maxHeight: Float!
			minScale: Float!
			maxScale: Float!
		}

		type DecalList implements PaginatedList {
			items: [Decal!]!
			totalItems: Int!
		}

		extend type Query {
			decals: DecalList!
			decalsByCategory(category: String!): [Decal!]!
		}
	`,
};

@VendurePlugin({
	imports: [PluginCommonModule],
	entities: [Decal],
	providers: [DecalService],
	adminApiExtensions,
	shopApiExtensions,
})
export class DecalPlugin {
	static ui: AdminUiExtension = {
		id: 'decal-ui',
		extensionPath: path.join(__dirname, 'ui'),
		routes: [{ route: 'extensions/decal', filePath: 'routes.ts' }],
		providers: ['providers.ts'],
	};
}
