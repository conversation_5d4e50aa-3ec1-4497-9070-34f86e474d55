{"version": 3, "sources": ["../../../../../../../node_modules/@vendure/admin-ui/fesm2022/vendure-admin-ui-customer.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Input, ChangeDetectionStrategy, Component, EventEmitter, Output, ViewContainerRef, ViewChild, NgModule } from '@angular/core';\nimport { BehaviorSubject, Subject, combineLatest, forkJoin, from, EMPTY, of } from 'rxjs';\nimport { map, startWith, distinctUntilChanged, debounceTime, tap, takeUntil, switchMap, filter, take, shareReplay, merge, mergeMap, concatMap, mapTo } from 'rxjs/operators';\nimport * as i1 from '@vendure/admin-ui/core';\nimport { SelectionManager, HistoryEntryType, CUSTOMER_FRAGMENT, TypedBaseDetailComponent, getCustomFieldsDefaults, SortOrder, EditNoteDialogComponent, CustomerDetailQueryDocument, createBulkDeleteAction, Permission, CUSTOMER_GROUP_FRAGMENT, TypedBaseListComponent, GetCustomerGroupListDocument, ModalService, DataService, NotificationService, LogicalOperator, CustomerListQueryDocument, PageComponent, detailBreadcrumb, detailComponentWithResolver, GetCustomerGroupDetailDocument, SharedModule, PageService } from '@vendure/admin-ui/core';\nimport * as i1$1 from '@angular/forms';\nimport { FormControl, UntypedFormArray, Validators } from '@angular/forms';\nimport * as i2 from '@angular/router';\nimport { RouterModule, ROUTES } from '@angular/router';\nimport * as i3 from '@clr/angular';\nimport * as i3$1 from '@angular/common';\nimport * as i4 from '@ngx-translate/core';\nimport { marker } from '@biesbjerg/ngx-translate-extract-marker';\nimport { notNullOrUndefined } from '@vendure/common/lib/shared-utils';\nimport { gql } from 'apollo-angular';\nimport * as i3$2 from '@ng-select/ng-select';\nfunction CustomerStatusLabelComponent_vdr_chip_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"clr-icon\", 1);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"customer.verified\"), \" \");\n  }\n}\nfunction CustomerStatusLabelComponent_vdr_chip_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"clr-icon\", 2);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"customer.registered\"), \" \");\n  }\n}\nfunction CustomerStatusLabelComponent_vdr_chip_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\");\n    i0.ɵɵtemplate(1, CustomerStatusLabelComponent_vdr_chip_0_ng_container_1_Template, 4, 3, \"ng-container\", 0)(2, CustomerStatusLabelComponent_vdr_chip_0_ng_container_2_Template, 4, 3, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.customer.user == null ? null : ctx_r0.customer.user.verified);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r0.customer.user == null ? null : ctx_r0.customer.user.verified));\n  }\n}\nfunction CustomerStatusLabelComponent_vdr_chip_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"customer.guest\"));\n  }\n}\nconst _c0 = a0 => [\"/customer/customers\", a0];\nfunction CustomerGroupMemberListComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const customerGroup_r1 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", customerGroup_r1.id, \" \");\n  }\n}\nfunction CustomerGroupMemberListComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const customer_r2 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, customer_r2.createdAt, \"short\"), \" \");\n  }\n}\nfunction CustomerGroupMemberListComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const customer_r3 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, customer_r3.createdAt, \"short\"), \" \");\n  }\n}\nfunction CustomerGroupMemberListComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 9)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"clr-icon\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r4 = ctx.item;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, customer_r4.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", customer_r4.title, \" \", customer_r4.firstName, \" \", customer_r4.lastName, \" \");\n  }\n}\nfunction CustomerGroupMemberListComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-customer-status-label\", 11);\n  }\n  if (rf & 2) {\n    const customer_r5 = ctx.item;\n    i0.ɵɵproperty(\"customer\", customer_r5);\n  }\n}\nfunction CustomerGroupMemberListComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const customer_r6 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", customer_r6.emailAddress, \" \");\n  }\n}\nconst _c1 = a0 => ({\n  groupName: a0\n});\nconst _c2 = a0 => ({\n  count: a0\n});\nfunction AddCustomerToGroupDialogComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, \"customer.add-customers-to-group-with-name\", i0.ɵɵpureFunction1(4, _c1, ctx_r0.group.name)), \"\\n\");\n  }\n}\nfunction AddCustomerToGroupDialogComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function AddCustomerToGroupDialogComponent_ng_template_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.cancel());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function AddCustomerToGroupDialogComponent_ng_template_4_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.add());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 3, \"common.cancel\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.selectedCustomerIds.length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 5, \"customer.add-customers-to-group-with-count\", i0.ɵɵpureFunction1(8, _c2, ctx_r0.selectedCustomerIds.length)), \" \");\n  }\n}\nfunction AddressDetailDialogComponent_ng_template_0_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const streetLine1_r1 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", streetLine1_r1, \",\");\n  }\n}\nfunction AddressDetailDialogComponent_ng_template_0_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const countryCode_r2 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", countryCode_r2, \"\");\n  }\n}\nfunction AddressDetailDialogComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AddressDetailDialogComponent_ng_template_0_span_0_Template, 2, 1, \"span\", 3)(1, AddressDetailDialogComponent_ng_template_0_span_1_Template, 2, 1, \"span\", 3);\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.addressForm.get(\"streetLine1\")) == null ? null : tmp_1_0.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r2.addressForm.get(\"countryCode\")) == null ? null : tmp_2_0.value);\n  }\n}\nfunction AddressDetailDialogComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function AddressDetailDialogComponent_ng_template_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.cancel());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function AddressDetailDialogComponent_ng_template_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 3, \"common.cancel\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.addressForm.valid || !ctx_r2.addressForm.touched);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 5, \"common.update\"), \" \");\n  }\n}\nfunction AddressCardComponent_div_0_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const address_r1 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", address_r1.streetLine1, \",\");\n  }\n}\nfunction AddressCardComponent_div_0_vdr_chip_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\", 15);\n    i0.ɵɵelement(1, \"clr-icon\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"customer.default-shipping-address\"), \" \");\n  }\n}\nfunction AddressCardComponent_div_0_vdr_chip_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\", 15);\n    i0.ɵɵelement(1, \"clr-icon\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"customer.default-billing-address\"), \" \");\n  }\n}\nfunction AddressCardComponent_div_0_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function AddressCardComponent_div_0_ng_container_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.editAddress());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"vdr-dropdown\")(5, \"button\", 19);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelement(8, \"clr-icon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"vdr-dropdown-menu\")(10, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AddressCardComponent_div_0_ng_container_14_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.setAsDefaultShippingAddress());\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AddressCardComponent_div_0_ng_container_14_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.setAsDefaultBillingAddress());\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"div\", 22);\n    i0.ɵɵelementStart(17, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AddressCardComponent_div_0_ng_container_14_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.delete());\n    });\n    i0.ɵɵelement(18, \"clr-icon\", 24);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 7, \"common.edit\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 9, \"common.more\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isDefaultShipping);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 11, \"customer.set-as-default-shipping-address\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isDefaultBilling);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 13, \"customer.set-as-default-billing-address\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 15, \"common.delete\"), \" \");\n  }\n}\nfunction AddressCardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵtemplate(3, AddressCardComponent_div_0_span_3_Template, 2, 1, \"span\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 5);\n    i0.ɵɵtemplate(6, AddressCardComponent_div_0_vdr_chip_6_Template, 4, 3, \"vdr-chip\", 6)(7, AddressCardComponent_div_0_vdr_chip_7_Template, 4, 3, \"vdr-chip\", 6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n    i0.ɵɵelement(10, \"vdr-formatted-address\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 10)(12, \"div\", 11);\n    i0.ɵɵelement(13, \"vdr-entity-info\", 12);\n    i0.ɵɵtemplate(14, AddressCardComponent_div_0_ng_container_14_Template, 21, 17, \"ng-container\", 13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const address_r1 = ctx.ngIf;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", address_r1.streetLine1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", address_r1.countryCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDefaultShipping);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDefaultBilling);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"address\", address_r1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"entity\", address_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.editable);\n  }\n}\nfunction SelectCustomerGroupDialogComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, \"customer.add-customer-to-group\"), \"\\n\");\n  }\n}\nfunction SelectCustomerGroupDialogComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵlistener(\"click\", function SelectCustomerGroupDialogComponent_ng_template_3_Template_span_click_0_listener() {\n      const ctx_r1 = i0.ɵɵrestoreView(_r1);\n      const item_r3 = ctx_r1.item;\n      const clear_r4 = ctx_r1.clear;\n      return i0.ɵɵresetView(clear_r4(item_r3));\n    });\n    i0.ɵɵtext(1, \" \\xD7 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"vdr-chip\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"colorFrom\", item_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3.name);\n  }\n}\nfunction SelectCustomerGroupDialogComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.item;\n    i0.ɵɵproperty(\"colorFrom\", item_r5.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r5.name);\n  }\n}\nfunction SelectCustomerGroupDialogComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function SelectCustomerGroupDialogComponent_ng_template_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.cancel());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function SelectCustomerGroupDialogComponent_ng_template_5_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.add());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 3, \"common.cancel\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r6.selectedGroupIds.length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 5, \"customer.add-customer-to-groups-with-count\", i0.ɵɵpureFunction1(8, _c2, ctx_r6.selectedGroupIds.length)), \" \");\n  }\n}\nconst _c3 = [\"portal\"];\nconst _c4 = a0 => ({\n  strategy: a0\n});\nfunction CustomerHistoryComponent_vdr_timeline_entry_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-timeline-entry\", 6)(1, \"div\", 7)(2, \"textarea\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerHistoryComponent_vdr_timeline_entry_1_Template_textarea_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.note, $event) || (ctx_r1.note = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CustomerHistoryComponent_vdr_timeline_entry_1_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addNoteToCustomer());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"featured\", true)(\"isFirst\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.note);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.note);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 5, \"order.add-note\"), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_vdr_customer_history_entry_host_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-customer-history-entry-host\", 11);\n    i0.ɵɵlistener(\"expandClick\", function CustomerHistoryComponent_ng_container_2_vdr_customer_history_entry_host_1_Template_vdr_customer_history_entry_host_expandClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.expanded = !ctx_r1.expanded);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"customer\", ctx_r1.customer)(\"entry\", entry_r4)(\"expanded\", ctx_r1.expanded);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"customer.history-using-native-auth-strategy\"), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_2_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, \"customer.history-using-external-auth-strategy\", i0.ɵɵpureFunction1(4, _c4, entry_r4.data.strategy)), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_2_ng_container_4_Template, 3, 3, \"ng-container\", 17)(5, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_2_ng_template_5_Template, 2, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const namedStrategy_r5 = i0.ɵɵreference(6);\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"customer.history-customer-registered\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", entry_r4.data.strategy === \"native\")(\"ngIfElse\", namedStrategy_r5);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"customer.history-using-native-auth-strategy\"), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_3_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, \"customer.history-using-external-auth-strategy\", i0.ɵɵpureFunction1(4, _c4, entry_r4.data.strategy)), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_3_ng_container_4_Template, 3, 3, \"ng-container\", 17)(5, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_3_ng_template_5_Template, 2, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const namedStrategy_r6 = i0.ɵɵreference(6);\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"customer.history-customer-verified\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", entry_r4.data.strategy === \"native\")(\"ngIfElse\", namedStrategy_r6);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementStart(4, \"vdr-history-entry-detail\");\n    i0.ɵɵelement(5, \"vdr-object-tree\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"customer.history-customer-detail-updated\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", entry_r4.data.input);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, \"customer.history-customer-added-to-group\", i0.ɵɵpureFunction1(4, _c1, entry_r4.data.groupName)), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, \"customer.history-customer-removed-from-group\", i0.ɵɵpureFunction1(4, _c1, entry_r4.data.groupName)), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"customer.history-customer-address-created\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(entry_r4.data.address);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"vdr-history-entry-detail\");\n    i0.ɵɵelement(7, \"vdr-object-tree\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"customer.history-customer-address-updated\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(entry_r4.data.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", entry_r4.data.input);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"customer.history-customer-address-deleted\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(entry_r4.data.address);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"customer.history-customer-password-updated\"), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"customer.history-customer-password-reset-requested\"), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"customer.history-customer-password-reset-verified\"), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementStart(4, \"vdr-history-entry-detail\")(5, \"vdr-labeled-data\", 21);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"vdr-labeled-data\", 21);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 5, \"customer.history-customer-email-update-requested\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(6, 7, \"customer.old-email-address\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", entry_r4.data.oldEmailAddress, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(9, 9, \"customer.new-email-address\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", entry_r4.data.newEmailAddress, \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementStart(4, \"vdr-history-entry-detail\")(5, \"vdr-labeled-data\", 21);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"vdr-labeled-data\", 21);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 5, \"customer.history-customer-email-update-verified\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(6, 7, \"customer.old-email-address\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", entry_r4.data.oldEmailAddress, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(9, 9, \"customer.new-email-address\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", entry_r4.data.newEmailAddress, \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"div\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 23);\n    i0.ɵɵelementStart(5, \"vdr-dropdown\")(6, \"button\", 24);\n    i0.ɵɵelement(7, \"clr-icon\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"vdr-dropdown-menu\", 26)(9, \"button\", 27);\n    i0.ɵɵpipe(10, \"hasPermission\");\n    i0.ɵɵlistener(\"click\", function CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_15_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateNote.emit(entry_r4));\n    });\n    i0.ɵɵelement(11, \"clr-icon\", 28);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"div\", 29);\n    i0.ɵɵelementStart(15, \"button\", 27);\n    i0.ɵɵpipe(16, \"hasPermission\");\n    i0.ɵɵlistener(\"click\", function CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_15_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteNote.emit(entry_r4));\n    });\n    i0.ɵɵelement(17, \"clr-icon\", 30);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", entry_r4.data.note, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", !i0.ɵɵpipeBind1(10, 5, \"UpdateCustomer\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 7, \"common.edit\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !i0.ɵɵpipeBind1(16, 9, \"UpdateCustomer\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 11, \"common.delete\"), \" \");\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_16_vdr_history_entry_detail_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-history-entry-detail\");\n    i0.ɵɵelement(1, \"vdr-object-tree\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", entry_r4.data);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_16_vdr_history_entry_detail_4_Template, 2, 1, \"vdr-history-entry-detail\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, entry_r4.type), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", entry_r4.data);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-timeline-entry\", 12);\n    i0.ɵɵelementContainerStart(1, 13);\n    i0.ɵɵtemplate(2, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_2_Template, 7, 5, \"ng-container\", 14)(3, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_3_Template, 7, 5, \"ng-container\", 14)(4, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_4_Template, 6, 4, \"ng-container\", 14)(5, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_5_Template, 3, 6, \"ng-container\", 14)(6, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_6_Template, 3, 6, \"ng-container\", 14)(7, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_7_Template, 6, 4, \"ng-container\", 14)(8, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_8_Template, 8, 5, \"ng-container\", 14)(9, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_9_Template, 5, 4, \"ng-container\", 14)(10, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_10_Template, 3, 3, \"ng-container\", 14)(11, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_11_Template, 3, 3, \"ng-container\", 14)(12, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_12_Template, 3, 3, \"ng-container\", 14)(13, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_13_Template, 11, 11, \"ng-container\", 14)(14, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_14_Template, 11, 11, \"ng-container\", 14)(15, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_15_Template, 20, 13, \"ng-container\", 14)(16, CustomerHistoryComponent_ng_container_2_ng_template_2_ng_container_16_Template, 5, 4, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"displayType\", ctx_r1.getDisplayType(entry_r4))(\"iconShape\", ctx_r1.getTimelineIcon(entry_r4))(\"createdAt\", entry_r4.createdAt)(\"name\", ctx_r1.getName(entry_r4))(\"featured\", ctx_r1.isFeatured(entry_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", entry_r4.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_REGISTERED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_VERIFIED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_DETAIL_UPDATED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_ADDED_TO_GROUP);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_REMOVED_FROM_GROUP);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_ADDRESS_CREATED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_ADDRESS_UPDATED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_ADDRESS_DELETED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_PASSWORD_UPDATED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_PASSWORD_RESET_REQUESTED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_PASSWORD_RESET_VERIFIED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_EMAIL_UPDATE_REQUESTED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_EMAIL_UPDATE_VERIFIED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.type.CUSTOMER_NOTE);\n  }\n}\nfunction CustomerHistoryComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CustomerHistoryComponent_ng_container_2_vdr_customer_history_entry_host_1_Template, 1, 3, \"vdr-customer-history-entry-host\", 10)(2, CustomerHistoryComponent_ng_container_2_ng_template_2_Template, 17, 20, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const entry_r4 = ctx.$implicit;\n    const defaultComponents_r8 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasCustomComponent(entry_r4.type))(\"ngIfElse\", defaultComponents_r8);\n  }\n}\nconst _c5 = () => [\"UpdateCustomer\"];\nconst _c6 = a0 => [\"/orders\", a0];\nfunction CustomerDetailComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.create());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.addressDefaultsUpdated || ctx_r1.detailForm.valid && ctx_r1.detailForm.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"common.create\"), \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_template_7_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_ng_template_7_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.addressDefaultsUpdated || ctx_r1.detailForm.valid && ctx_r1.detailForm.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"common.update\"), \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerDetailComponent_ng_template_7_button_0_Template, 3, 4, \"button\", 23);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdateCustomer\");\n  }\n}\nfunction CustomerDetailComponent_vdr_card_12_vdr_labeled_data_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-labeled-data\", 26);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementStart(2, \"time\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"timeAgo\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const lastLogin_r4 = ctx.ngIf;\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(1, 3, \"customer.last-login\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"dateTime\", lastLogin_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 5, lastLogin_r4));\n  }\n}\nfunction CustomerDetailComponent_vdr_card_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\");\n    i0.ɵɵelement(1, \"vdr-customer-status-label\", 24);\n    i0.ɵɵtemplate(2, CustomerDetailComponent_vdr_card_12_vdr_labeled_data_2_Template, 5, 7, \"vdr-labeled-data\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r5 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customer\", customer_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", customer_r5.user == null ? null : customer_r5.user.lastLogin);\n  }\n}\nfunction CustomerDetailComponent_vdr_card_14_div_2_vdr_chip_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-chip\", 32);\n    i0.ɵɵlistener(\"iconClick\", function CustomerDetailComponent_vdr_card_14_div_2_vdr_chip_1_Template_vdr_chip_iconClick_0_listener() {\n      const group_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeFromGroup(group_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"colorFrom\", group_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(group_r7.name);\n  }\n}\nfunction CustomerDetailComponent_vdr_card_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CustomerDetailComponent_vdr_card_14_div_2_vdr_chip_1_Template, 2, 2, \"vdr-chip\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groups_r8 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", groups_r8);\n  }\n}\nfunction CustomerDetailComponent_vdr_card_14_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"customer.not-a-member-of-any-groups\"), \" \");\n  }\n}\nfunction CustomerDetailComponent_vdr_card_14_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_vdr_card_14_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToGroup());\n    });\n    i0.ɵɵelement(1, \"clr-icon\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"customer.add-customer-to-group\"), \" \");\n  }\n}\nfunction CustomerDetailComponent_vdr_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\", 28);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵtemplate(2, CustomerDetailComponent_vdr_card_14_div_2_Template, 2, 1, \"div\", 29)(3, CustomerDetailComponent_vdr_card_14_ng_template_3_Template, 3, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(5, \"div\");\n    i0.ɵɵtemplate(6, CustomerDetailComponent_vdr_card_14_button_6_Template, 4, 3, \"button\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const groups_r8 = ctx.ngIf;\n    const noGroups_r10 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(1, 4, \"customer.customer-groups\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", groups_r8.length)(\"ngIfElse\", noGroups_r10);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdateCustomerGroup\");\n  }\n}\nfunction CustomerDetailComponent_vdr_page_entity_info_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-page-entity-info\", 36);\n  }\n  if (rf & 2) {\n    const entity_r11 = ctx.ngIf;\n    i0.ɵɵproperty(\"entity\", entity_r11);\n  }\n}\nfunction CustomerDetailComponent_vdr_form_field_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-form-field\", 37);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelement(2, \"input\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(1, 1, \"customer.password\"));\n  }\n}\nfunction CustomerDetailComponent_vdr_card_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\", 39);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelement(2, \"vdr-tabbed-custom-fields\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(1, 3, \"common.custom-fields\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"customFields\", ctx_r1.customFields)(\"customFieldsFormGroup\", ctx_r1.detailForm.get(\"customer.customFields\"));\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_vdr_address_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-address-card\", 55);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵpipe(2, \"hasPermission\");\n    i0.ɵɵlistener(\"setAsDefaultBilling\", function CustomerDetailComponent_ng_container_48_vdr_address_card_4_Template_vdr_address_card_setAsDefaultBilling_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setDefaultBillingAddressId($event));\n    })(\"setAsDefaultShipping\", function CustomerDetailComponent_ng_container_48_vdr_address_card_4_Template_vdr_address_card_setAsDefaultShipping_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setDefaultShippingAddressId($event));\n    })(\"deleteAddress\", function CustomerDetailComponent_ng_container_48_vdr_address_card_4_Template_vdr_address_card_deleteAddress_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleDeleteAddress($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const addressForm_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"to-delete\", ctx_r1.addressesToDeleteIds.has(addressForm_r14.value.id));\n    i0.ɵɵproperty(\"availableCountries\", i0.ɵɵpipeBind1(1, 8, ctx_r1.availableCountries$))(\"isDefaultBilling\", ctx_r1.defaultBillingAddressId === addressForm_r14.value.id)(\"isDefaultShipping\", ctx_r1.defaultShippingAddressId === addressForm_r14.value.id)(\"addressForm\", addressForm_r14)(\"customFields\", ctx_r1.addressCustomFields)(\"editable\", i0.ɵɵpipeBind1(2, 10, i0.ɵɵpureFunction0(12, _c5)) && !ctx_r1.addressesToDeleteIds.has(addressForm_r14.value.id));\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function CustomerDetailComponent_ng_container_48_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addAddress());\n    });\n    i0.ɵɵelement(1, \"clr-icon\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"customer.create-new-address\"), \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const order_r16 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", order_r16.id, \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const order_r17 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, order_r17.createdAt, \"short\"), \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 57)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"clr-icon\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const order_r18 = ctx.item;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c6, order_r18.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(order_r18.code);\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const order_r19 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(order_r19.type);\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-order-state-label\", 59);\n  }\n  if (rf & 2) {\n    const order_r20 = ctx.item;\n    i0.ɵɵproperty(\"state\", order_r20.state);\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeCurrency\");\n  }\n  if (rf & 2) {\n    const order_r21 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, order_r21.totalWithTax, order_r21.currencyCode), \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"timeAgo\");\n  }\n  if (rf & 2) {\n    const order_r22 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, order_r22.updatedAt), \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const order_r23 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, order_r23.orderPlacedAt, \"short\"), \" \");\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_vdr_dt2_custom_field_column_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-dt2-custom-field-column\", 60);\n  }\n  if (rf & 2) {\n    const customField_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"customField\", customField_r24);\n  }\n}\nfunction CustomerDetailComponent_ng_container_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"vdr-card\", 28);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"div\", 8);\n    i0.ɵɵtemplate(4, CustomerDetailComponent_ng_container_48_vdr_address_card_4_Template, 3, 13, \"vdr-address-card\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CustomerDetailComponent_ng_container_48_button_5_Template, 4, 3, \"button\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"vdr-card\", 43);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementStart(8, \"vdr-data-table-2\", 44);\n    i0.ɵɵpipe(9, \"async\");\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵlistener(\"itemsPerPageChange\", function CustomerDetailComponent_ng_container_48_Template_vdr_data_table_2_itemsPerPageChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setOrderItemsPerPage($event));\n    })(\"pageChange\", function CustomerDetailComponent_ng_container_48_Template_vdr_data_table_2_pageChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setOrderCurrentPage($event));\n    });\n    i0.ɵɵelementStart(12, \"vdr-dt2-column\", 45);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵtemplate(14, CustomerDetailComponent_ng_container_48_ng_template_14_Template, 1, 1, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"vdr-dt2-column\", 46);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵtemplate(17, CustomerDetailComponent_ng_container_48_ng_template_17_Template, 2, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"vdr-dt2-column\", 47);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵtemplate(20, CustomerDetailComponent_ng_container_48_ng_template_20_Template, 4, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"vdr-dt2-column\", 48);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵtemplate(23, CustomerDetailComponent_ng_container_48_ng_template_23_Template, 2, 1, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"vdr-dt2-column\", 49);\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵtemplate(26, CustomerDetailComponent_ng_container_48_ng_template_26_Template, 1, 1, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"vdr-dt2-column\", 50);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵtemplate(29, CustomerDetailComponent_ng_container_48_ng_template_29_Template, 2, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"vdr-dt2-column\", 51);\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵtemplate(32, CustomerDetailComponent_ng_container_48_ng_template_32_Template, 2, 3, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"vdr-dt2-column\", 52);\n    i0.ɵɵpipe(34, \"translate\");\n    i0.ɵɵtemplate(35, CustomerDetailComponent_ng_container_48_ng_template_35_Template, 2, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, CustomerDetailComponent_ng_container_48_vdr_dt2_custom_field_column_36_Template, 1, 1, \"vdr-dt2-custom-field-column\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"vdr-card\", 28);\n    i0.ɵɵpipe(38, \"translate\");\n    i0.ɵɵelementStart(39, \"vdr-customer-history\", 54);\n    i0.ɵɵpipe(40, \"async\");\n    i0.ɵɵpipe(41, \"async\");\n    i0.ɵɵlistener(\"addNote\", function CustomerDetailComponent_ng_container_48_Template_vdr_customer_history_addNote_39_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addNoteToCustomer($event));\n    })(\"updateNote\", function CustomerDetailComponent_ng_container_48_Template_vdr_customer_history_updateNote_39_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateNote($event));\n    })(\"deleteNote\", function CustomerDetailComponent_ng_container_48_Template_vdr_customer_history_deleteNote_39_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteNote($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(2, 26, \"customer.addresses\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getAddressFormControls());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdateCustomer\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(7, 28, \"customer.orders\"))(\"paddingX\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(9, 30, ctx_r1.orders$))(\"itemsPerPage\", ctx_r1.ordersPerPage)(\"totalItems\", i0.ɵɵpipeBind1(10, 32, ctx_r1.ordersCount$))(\"currentPage\", ctx_r1.currentOrdersPage)(\"emptyStateLabel\", i0.ɵɵpipeBind1(11, 34, \"customer.no-orders-placed\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(13, 36, \"common.id\"))(\"hiddenByDefault\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(16, 38, \"common.created-at\"))(\"hiddenByDefault\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(19, 40, \"common.code\"))(\"optional\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(22, 42, \"order.order-type\"))(\"hiddenByDefault\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(25, 44, \"order.state\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(28, 46, \"order.total\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(31, 48, \"common.updated-at\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(34, 50, \"order.placed-at\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.customFields);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(38, 52, \"customer.customer-history\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"customer\", i0.ɵɵpipeBind1(40, 54, ctx_r1.entity$))(\"history\", i0.ɵɵpipeBind1(41, 56, ctx_r1.history$));\n  }\n}\nfunction CustomerGroupDetailComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function CustomerGroupDetailComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.create());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.detailForm.valid && ctx_r1.detailForm.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"common.create\"), \" \");\n  }\n}\nfunction CustomerGroupDetailComponent_ng_template_7_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function CustomerGroupDetailComponent_ng_template_7_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.detailForm.valid && ctx_r1.detailForm.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"common.update\"), \" \");\n  }\n}\nfunction CustomerGroupDetailComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerGroupDetailComponent_ng_template_7_button_0_Template, 3, 4, \"button\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdateCustomer\");\n  }\n}\nfunction CustomerGroupDetailComponent_vdr_card_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\");\n    i0.ɵɵelement(1, \"vdr-page-entity-info\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const entity_r4 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"entity\", entity_r4);\n  }\n}\nfunction CustomerGroupDetailComponent_vdr_card_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\", 13);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelement(2, \"vdr-tabbed-custom-fields\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(1, 3, \"common.custom-fields\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"customFields\", ctx_r1.customFields)(\"customFieldsFormGroup\", ctx_r1.detailForm.get(\"customFields\"));\n  }\n}\nconst _c7 = () => [\"CreateCustomerGroup\", \"UpdateCustomerGroup\"];\nfunction CustomerGroupDetailDialogComponent_ng_template_0_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"customer.update-customer-group\"));\n  }\n}\nfunction CustomerGroupDetailDialogComponent_ng_template_0_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"customer.create-customer-group\"));\n  }\n}\nfunction CustomerGroupDetailDialogComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerGroupDetailDialogComponent_ng_template_0_span_0_Template, 3, 3, \"span\", 6)(1, CustomerGroupDetailDialogComponent_ng_template_0_span_1_Template, 3, 3, \"span\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.group.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.group.id);\n  }\n}\nfunction CustomerGroupDetailDialogComponent_section_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 7)(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"vdr-tabbed-custom-fields\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 3, \"common.custom-fields\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"customFields\", ctx_r0.customFields)(\"customFieldsFormGroup\", ctx_r0.form.get(\"customFields\"));\n  }\n}\nfunction CustomerGroupDetailDialogComponent_ng_template_7_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"customer.update-customer-group\"));\n  }\n}\nfunction CustomerGroupDetailDialogComponent_ng_template_7_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"customer.create-customer-group\"));\n  }\n}\nfunction CustomerGroupDetailDialogComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CustomerGroupDetailDialogComponent_ng_template_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.cancel());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function CustomerGroupDetailDialogComponent_ng_template_7_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtemplate(4, CustomerGroupDetailDialogComponent_ng_template_7_span_4_Template, 3, 3, \"span\", 6)(5, CustomerGroupDetailDialogComponent_ng_template_7_span_5_Template, 3, 3, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 4, \"common.cancel\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.form.valid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.group.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.group.id);\n  }\n}\nconst _c8 = () => [\"./\", \"create\"];\nconst _c9 = a0 => [\"./\", a0];\nconst _c10 = a0 => ({\n  contents: a0\n});\nfunction CustomerGroupListComponent_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 5);\n    i0.ɵɵelement(1, \"clr-icon\", 6);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"customer.create-new-customer-group\"), \" \");\n  }\n}\nfunction CustomerGroupListComponent_ng_template_9_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const customerGroup_r3 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", customerGroup_r3.id, \" \");\n  }\n}\nfunction CustomerGroupListComponent_ng_template_9_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const customerGroup_r4 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, customerGroup_r4.createdAt, \"short\"), \" \");\n  }\n}\nfunction CustomerGroupListComponent_ng_template_9_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const customerGroup_r5 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, customerGroup_r5.updatedAt, \"short\"), \" \");\n  }\n}\nfunction CustomerGroupListComponent_ng_template_9_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 16)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"clr-icon\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customerGroup_r6 = ctx.item;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c9, customerGroup_r6.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(customerGroup_r6.name);\n  }\n}\nfunction CustomerGroupListComponent_ng_template_9_vdr_dt2_custom_field_column_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-dt2-custom-field-column\", 18);\n  }\n  if (rf & 2) {\n    const field_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"customField\", field_r7)(\"sorts\", ctx_r1.sorts);\n  }\n}\nfunction CustomerGroupListComponent_ng_template_9_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 19)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"clr-icon\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customerGroup_r8 = ctx.item;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(6, _c9, i0.ɵɵpureFunction1(4, _c10, customerGroup_r8.id)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"customer.view-group-members\"));\n  }\n}\nfunction CustomerGroupListComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-data-table-2\", 7);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵlistener(\"pageChange\", function CustomerGroupListComponent_ng_template_9_Template_vdr_data_table_2_pageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setPageNumber($event));\n    })(\"itemsPerPageChange\", function CustomerGroupListComponent_ng_template_9_Template_vdr_data_table_2_itemsPerPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setItemsPerPage($event));\n    })(\"visibleColumnsChange\", function CustomerGroupListComponent_ng_template_9_Template_vdr_data_table_2_visibleColumnsChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setVisibleColumns($event));\n    });\n    i0.ɵɵelement(6, \"vdr-bulk-action-menu\", 8)(7, \"vdr-dt2-search\", 9);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementStart(9, \"vdr-dt2-column\", 10);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵtemplate(11, CustomerGroupListComponent_ng_template_9_ng_template_11_Template, 1, 1, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"vdr-dt2-column\", 11);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵtemplate(14, CustomerGroupListComponent_ng_template_9_ng_template_14_Template, 2, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"vdr-dt2-column\", 12);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵtemplate(17, CustomerGroupListComponent_ng_template_9_ng_template_17_Template, 2, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"vdr-dt2-column\", 13);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵtemplate(20, CustomerGroupListComponent_ng_template_9_ng_template_20_Template, 4, 4, \"ng-template\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, CustomerGroupListComponent_ng_template_9_vdr_dt2_custom_field_column_21_Template, 1, 2, \"vdr-dt2-custom-field-column\", 14);\n    i0.ɵɵelementStart(22, \"vdr-dt2-column\", 15);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵtemplate(24, CustomerGroupListComponent_ng_template_9_ng_template_24_Template, 5, 8, \"ng-template\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r1.dataTableListId)(\"items\", i0.ɵɵpipeBind1(1, 25, ctx_r1.items$))(\"itemsPerPage\", i0.ɵɵpipeBind1(2, 27, ctx_r1.itemsPerPage$))(\"totalItems\", i0.ɵɵpipeBind1(3, 29, ctx_r1.totalItems$))(\"currentPage\", i0.ɵɵpipeBind1(4, 31, ctx_r1.currentPage$))(\"filters\", ctx_r1.filters)(\"activeIndex\", i0.ɵɵpipeBind1(5, 33, ctx_r1.activeIndex$));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"hostComponent\", ctx_r1)(\"selectionManager\", ctx_r1.selectionManager);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"searchTermControl\", ctx_r1.searchTermControl)(\"searchTermPlaceholder\", i0.ɵɵpipeBind1(8, 35, \"common.search-by-name\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(10, 37, \"common.id\"))(\"hiddenByDefault\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(13, 39, \"common.created-at\"))(\"hiddenByDefault\", true)(\"sort\", ctx_r1.sorts.get(\"createdAt\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(16, 41, \"common.updated-at\"))(\"hiddenByDefault\", true)(\"sort\", ctx_r1.sorts.get(\"updatedAt\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(19, 43, \"common.name\"))(\"optional\", false)(\"sort\", ctx_r1.sorts.get(\"name\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.customFields);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(23, 45, \"common.view-contents\"))(\"optional\", false);\n  }\n}\nfunction CustomerGroupListComponent_ng_template_10_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function CustomerGroupListComponent_ng_template_10_ng_container_0_Template_button_click_1_listener() {\n      const activeGroup_r10 = i0.ɵɵrestoreView(_r9).ngIf;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToGroup(activeGroup_r10));\n    });\n    i0.ɵɵelement(2, \"clr-icon\", 6);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"vdr-customer-group-member-list\", 23);\n    i0.ɵɵpipe(7, \"async\");\n    i0.ɵɵpipe(8, \"async\");\n    i0.ɵɵpipe(9, \"async\");\n    i0.ɵɵlistener(\"fetchParamsChange\", function CustomerGroupListComponent_ng_template_10_ng_container_0_Template_vdr_customer_group_member_list_fetchParamsChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.fetchGroupMembers$.next($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const activeGroup_r10 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 5, \"customer.add-customers-to-group\", i0.ɵɵpureFunction1(14, _c1, activeGroup_r10.name)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"members\", i0.ɵɵpipeBind1(7, 8, ctx_r1.members$))(\"route\", ctx_r1.route)(\"totalItems\", i0.ɵɵpipeBind1(8, 10, ctx_r1.membersTotal$))(\"activeGroup\", i0.ɵɵpipeBind1(9, 12, ctx_r1.activeGroup$));\n  }\n}\nfunction CustomerGroupListComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerGroupListComponent_ng_template_10_ng_container_0_Template, 10, 16, \"ng-container\", 21);\n    i0.ɵɵpipe(1, \"async\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx_r1.activeGroup$));\n  }\n}\nconst _c11 = () => [\"./create\"];\nfunction CustomerListComponent_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵelement(1, \"clr-icon\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"customer.create-new-customer\"), \" \");\n  }\n}\nfunction CustomerListComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const customer_r1 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", customer_r1.id, \" \");\n  }\n}\nfunction CustomerListComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const customer_r2 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, customer_r2.createdAt, \"short\"), \" \");\n  }\n}\nfunction CustomerListComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const customer_r3 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, customer_r3.updatedAt, \"short\"), \" \");\n  }\n}\nfunction CustomerListComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 14)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"clr-icon\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r4 = ctx.item;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c9, customer_r4.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", customer_r4.title, \" \", customer_r4.firstName, \" \", customer_r4.lastName, \" \");\n  }\n}\nfunction CustomerListComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-customer-status-label\", 16);\n  }\n  if (rf & 2) {\n    const customer_r5 = ctx.item;\n    i0.ɵɵproperty(\"customer\", customer_r5);\n  }\n}\nfunction CustomerListComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const customer_r6 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", customer_r6.emailAddress, \" \");\n  }\n}\nfunction CustomerListComponent_vdr_dt2_custom_field_column_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-dt2-custom-field-column\", 17);\n  }\n  if (rf & 2) {\n    const field_r7 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"customField\", field_r7)(\"sorts\", ctx_r7.sorts);\n  }\n}\nclass CustomerStatusLabelComponent {\n  static {\n    this.ɵfac = function CustomerStatusLabelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerStatusLabelComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerStatusLabelComponent,\n      selectors: [[\"vdr-customer-status-label\"]],\n      inputs: {\n        customer: \"customer\"\n      },\n      standalone: false,\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [\"shape\", \"check-circle\", 1, \"verified-user-icon\"], [\"shape\", \"check-circle\", 1, \"registered-user-icon\"]],\n      template: function CustomerStatusLabelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CustomerStatusLabelComponent_vdr_chip_0_Template, 3, 2, \"vdr-chip\", 0)(1, CustomerStatusLabelComponent_vdr_chip_1_Template, 3, 3, \"vdr-chip\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.customer.user == null ? null : ctx.customer.user.id);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !(ctx.customer.user == null ? null : ctx.customer.user.id));\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgIf, i1.ChipComponent, i4.TranslatePipe],\n      styles: [\".registered-user-icon[_ngcontent-%COMP%]{color:var(--color-grey-300)}.verified-user-icon[_ngcontent-%COMP%]{color:var(--color-success-500)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerStatusLabelComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-status-label',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-chip *ngIf=\\\"customer.user?.id\\\">\\n    <ng-container *ngIf=\\\"customer.user?.verified\\\">\\n        <clr-icon shape=\\\"check-circle\\\" class=\\\"verified-user-icon\\\"></clr-icon>\\n        {{ 'customer.verified' | translate }}\\n    </ng-container>\\n    <ng-container *ngIf=\\\"!customer.user?.verified\\\">\\n        <clr-icon shape=\\\"check-circle\\\" class=\\\"registered-user-icon\\\"></clr-icon>\\n        {{ 'customer.registered' | translate }}\\n    </ng-container>\\n</vdr-chip>\\n<vdr-chip *ngIf=\\\"!customer.user?.id\\\">{{ 'customer.guest' | translate }}</vdr-chip>\\n\",\n      styles: [\".registered-user-icon{color:var(--color-grey-300)}.verified-user-icon{color:var(--color-success-500)}\\n\"]\n    }]\n  }], null, {\n    customer: [{\n      type: Input\n    }]\n  });\n})();\nclass CustomerGroupMemberListComponent {\n  constructor(router, dataService) {\n    this.router = router;\n    this.dataService = dataService;\n    this.selectedMemberIds = [];\n    this.selectionChange = new EventEmitter();\n    this.fetchParamsChange = new EventEmitter();\n    this.filterTermControl = new FormControl('');\n    this.selectionManager = new SelectionManager({\n      multiSelect: true,\n      itemsAreEqual: (a, b) => a.id === b.id,\n      additiveMode: true\n    });\n    this.refresh$ = new BehaviorSubject(true);\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.membersCurrentPage$ = this.route.paramMap.pipe(map(qpm => qpm.get('membersPage')), map(page => !page ? 1 : +page), startWith(1), distinctUntilChanged());\n    this.membersItemsPerPage$ = this.route.paramMap.pipe(map(qpm => qpm.get('membersPerPage')), map(perPage => !perPage ? 10 : +perPage), startWith(10), distinctUntilChanged());\n    const filterTerm$ = this.filterTermControl.valueChanges.pipe(debounceTime(250), tap(() => this.setContentsPageNumber(1)), startWith(''));\n    combineLatest(this.membersCurrentPage$, this.membersItemsPerPage$, filterTerm$, this.refresh$).pipe(takeUntil(this.destroy$)).subscribe(([currentPage, itemsPerPage, filterTerm]) => {\n      const take = itemsPerPage;\n      const skip = (currentPage - 1) * itemsPerPage;\n      this.fetchParamsChange.emit({\n        filterTerm: filterTerm ?? '',\n        skip,\n        take\n      });\n    });\n    this.selectionManager.setCurrentItems(this.members?.filter(m => this.selectedMemberIds.includes(m.id)) ?? []);\n    this.selectionManager.selectionChanges$.pipe(takeUntil(this.destroy$)).subscribe(selection => {\n      this.selectionChange.emit(selection.map(s => s.id));\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setContentsPageNumber(page) {\n    this.setParam('membersPage', page);\n  }\n  setContentsItemsPerPage(perPage) {\n    this.setParam('membersPerPage', perPage);\n  }\n  refresh() {\n    this.refresh$.next(true);\n  }\n  setParam(key, value) {\n    this.router.navigate(['./', {\n      ...this.route.snapshot.params,\n      [key]: value\n    }], {\n      relativeTo: this.route,\n      queryParamsHandling: 'merge'\n    });\n  }\n  static {\n    this.ɵfac = function CustomerGroupMemberListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerGroupMemberListComponent)(i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerGroupMemberListComponent,\n      selectors: [[\"vdr-customer-group-member-list\"]],\n      inputs: {\n        locationId: \"locationId\",\n        members: \"members\",\n        totalItems: \"totalItems\",\n        route: \"route\",\n        selectedMemberIds: \"selectedMemberIds\",\n        activeGroup: \"activeGroup\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        fetchParamsChange: \"fetchParamsChange\"\n      },\n      standalone: false,\n      decls: 24,\n      vars: 39,\n      consts: [[3, \"pageChange\", \"itemsPerPageChange\", \"id\", \"items\", \"itemsPerPage\", \"totalItems\", \"currentPage\"], [3, \"locationId\", \"hostComponent\", \"selectionManager\"], [3, \"searchTermControl\", \"searchTermPlaceholder\"], [\"id\", \"id\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"created-at\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"updated-at\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"name\", 3, \"heading\", \"optional\"], [\"id\", \"status\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"email-address\", 3, \"heading\"], [1, \"button-ghost\", 3, \"routerLink\"], [\"shape\", \"arrow right\"], [3, \"customer\"]],\n      template: function CustomerGroupMemberListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-data-table-2\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵlistener(\"pageChange\", function CustomerGroupMemberListComponent_Template_vdr_data_table_2_pageChange_0_listener($event) {\n            return ctx.setContentsPageNumber($event);\n          })(\"itemsPerPageChange\", function CustomerGroupMemberListComponent_Template_vdr_data_table_2_itemsPerPageChange_0_listener($event) {\n            return ctx.setContentsItemsPerPage($event);\n          });\n          i0.ɵɵelement(3, \"vdr-bulk-action-menu\", 1)(4, \"vdr-dt2-search\", 2);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementStart(6, \"vdr-dt2-column\", 3);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵtemplate(8, CustomerGroupMemberListComponent_ng_template_8_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"vdr-dt2-column\", 4);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵtemplate(11, CustomerGroupMemberListComponent_ng_template_11_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"vdr-dt2-column\", 5);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵtemplate(14, CustomerGroupMemberListComponent_ng_template_14_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"vdr-dt2-column\", 6);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵtemplate(17, CustomerGroupMemberListComponent_ng_template_17_Template, 4, 6, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"vdr-dt2-column\", 7);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵtemplate(20, CustomerGroupMemberListComponent_ng_template_20_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"vdr-dt2-column\", 8);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵtemplate(23, CustomerGroupMemberListComponent_ng_template_23_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"id\", ctx.locationId)(\"items\", ctx.members)(\"itemsPerPage\", i0.ɵɵpipeBind1(1, 21, ctx.membersItemsPerPage$))(\"totalItems\", ctx.totalItems)(\"currentPage\", i0.ɵɵpipeBind1(2, 23, ctx.membersCurrentPage$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"locationId\", ctx.locationId)(\"hostComponent\", ctx)(\"selectionManager\", ctx.selectionManager);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"searchTermControl\", ctx.filterTermControl)(\"searchTermPlaceholder\", i0.ɵɵpipeBind1(5, 25, \"customer.search-customers-by-email\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(7, 27, \"common.id\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(10, 29, \"common.created-at\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(13, 31, \"common.updated-at\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(16, 33, \"customer.name\"))(\"optional\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(19, 35, \"common.status\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(22, 37, \"customer.email-address\"));\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i2.RouterLink, i1.BulkActionMenuComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i1.DataTable2SearchComponent, CustomerStatusLabelComponent, i3$1.AsyncPipe, i4.TranslatePipe, i1.LocaleDatePipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerGroupMemberListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-group-member-list',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-data-table-2\\n    [id]=\\\"locationId\\\"\\n    [items]=\\\"members\\\"\\n    [itemsPerPage]=\\\"membersItemsPerPage$ | async\\\"\\n    [totalItems]=\\\"totalItems\\\"\\n    [currentPage]=\\\"membersCurrentPage$ | async\\\"\\n    (pageChange)=\\\"setContentsPageNumber($event)\\\"\\n    (itemsPerPageChange)=\\\"setContentsItemsPerPage($event)\\\"\\n>\\n    <vdr-bulk-action-menu\\n        [locationId]=\\\"locationId\\\"\\n        [hostComponent]=\\\"this\\\"\\n        [selectionManager]=\\\"selectionManager\\\"\\n    ></vdr-bulk-action-menu>\\n    <vdr-dt2-search\\n        [searchTermControl]=\\\"filterTermControl\\\"\\n        [searchTermPlaceholder]=\\\"'customer.search-customers-by-email' | translate\\\"\\n    ></vdr-dt2-search>\\n    <vdr-dt2-column [heading]=\\\"'common.id' | translate\\\" id=\\\"id\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-customerGroup=\\\"item\\\">\\n            {{ customerGroup.id }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'common.created-at' | translate\\\" id=\\\"created-at\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            {{ customer.createdAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'common.updated-at' | translate\\\" id=\\\"updated-at\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            {{ customer.createdAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'customer.name' | translate\\\" id=\\\"name\\\" [optional]=\\\"false\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            <a class=\\\"button-ghost\\\" [routerLink]=\\\"['/customer/customers', customer.id]\\\"\\n                ><span> {{ customer.title }} {{ customer.firstName }} {{ customer.lastName }} </span>\\n                <clr-icon shape=\\\"arrow right\\\"></clr-icon>\\n            </a>\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'common.status' | translate\\\" id=\\\"status\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            <vdr-customer-status-label [customer]=\\\"customer\\\" />\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'customer.email-address' | translate\\\" id=\\\"email-address\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            {{ customer.emailAddress }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n</vdr-data-table-2>\\n\"\n    }]\n  }], () => [{\n    type: i2.Router\n  }, {\n    type: i1.DataService\n  }], {\n    locationId: [{\n      type: Input\n    }],\n    members: [{\n      type: Input\n    }],\n    totalItems: [{\n      type: Input\n    }],\n    route: [{\n      type: Input\n    }],\n    selectedMemberIds: [{\n      type: Input\n    }],\n    activeGroup: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    fetchParamsChange: [{\n      type: Output\n    }]\n  });\n})();\nclass AddCustomerToGroupDialogComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.selectedCustomerIds = [];\n    this.fetchGroupMembers$ = new BehaviorSubject({\n      skip: 0,\n      take: 10,\n      filterTerm: ''\n    });\n  }\n  ngOnInit() {\n    const customerResult$ = this.fetchGroupMembers$.pipe(switchMap(({\n      skip,\n      take,\n      filterTerm\n    }) => this.dataService.customer.getCustomerList(take, skip, filterTerm).mapStream(res => res.customers)));\n    this.customers$ = customerResult$.pipe(map(res => res.items));\n    this.customersTotal$ = customerResult$.pipe(map(res => res.totalItems));\n  }\n  cancel() {\n    this.resolveWith();\n  }\n  add() {\n    this.resolveWith(this.selectedCustomerIds);\n  }\n  static {\n    this.ɵfac = function AddCustomerToGroupDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddCustomerToGroupDialogComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AddCustomerToGroupDialogComponent,\n      selectors: [[\"vdr-add-customer-to-group-dialog\"]],\n      standalone: false,\n      decls: 5,\n      vars: 8,\n      consts: [[\"vdrDialogTitle\", \"\"], [\"locationId\", \"customer-group-members-picker-list\", 3, \"fetchParamsChange\", \"selectionChange\", \"members\", \"totalItems\", \"route\", \"selectedMemberIds\"], [\"vdrDialogButtons\", \"\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"]],\n      template: function AddCustomerToGroupDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AddCustomerToGroupDialogComponent_ng_template_0_Template, 2, 6, \"ng-template\", 0);\n          i0.ɵɵelementStart(1, \"vdr-customer-group-member-list\", 1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵlistener(\"fetchParamsChange\", function AddCustomerToGroupDialogComponent_Template_vdr_customer_group_member_list_fetchParamsChange_1_listener($event) {\n            return ctx.fetchGroupMembers$.next($event);\n          })(\"selectionChange\", function AddCustomerToGroupDialogComponent_Template_vdr_customer_group_member_list_selectionChange_1_listener($event) {\n            return ctx.selectedCustomerIds = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, AddCustomerToGroupDialogComponent_ng_template_4_Template, 6, 10, \"ng-template\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"members\", i0.ɵɵpipeBind1(2, 4, ctx.customers$))(\"totalItems\", i0.ɵɵpipeBind1(3, 6, ctx.customersTotal$))(\"route\", ctx.route)(\"selectedMemberIds\", ctx.selectedCustomerIds);\n        }\n      },\n      dependencies: [i1.DialogButtonsDirective, i1.DialogTitleDirective, CustomerGroupMemberListComponent, i3$1.AsyncPipe, i4.TranslatePipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AddCustomerToGroupDialogComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-add-customer-to-group-dialog',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<ng-template vdrDialogTitle>\\n    {{ 'customer.add-customers-to-group-with-name' | translate: {groupName: group.name} }}\\n</ng-template>\\n\\n<vdr-customer-group-member-list\\n    locationId=\\\"customer-group-members-picker-list\\\"\\n    [members]=\\\"customers$ | async\\\"\\n    [totalItems]=\\\"customersTotal$ | async\\\"\\n    [route]=\\\"route\\\"\\n    [selectedMemberIds]=\\\"selectedCustomerIds\\\"\\n    (fetchParamsChange)=\\\"fetchGroupMembers$.next($event)\\\"\\n    (selectionChange)=\\\"selectedCustomerIds = $event\\\"\\n/>\\n\\n<ng-template vdrDialogButtons>\\n    <button type=\\\"button\\\" class=\\\"btn\\\" (click)=\\\"cancel()\\\">{{ 'common.cancel' | translate }}</button>\\n    <button type=\\\"submit\\\" (click)=\\\"add()\\\" [disabled]=\\\"!selectedCustomerIds.length\\\" class=\\\"btn btn-primary\\\">\\n        {{ 'customer.add-customers-to-group-with-count' | translate: {count: selectedCustomerIds.length} }}\\n    </button>\\n</ng-template>\\n\"\n    }]\n  }], () => [{\n    type: i1.DataService\n  }], null);\n})();\nclass AddressDetailDialogComponent {\n  constructor(changeDetector) {\n    this.changeDetector = changeDetector;\n    this.availableCountries = [];\n  }\n  ngOnInit() {\n    this.addressForm.valueChanges.subscribe(() => this.changeDetector.markForCheck());\n  }\n  cancel() {\n    this.resolveWith();\n  }\n  save() {\n    this.resolveWith(this.addressForm);\n  }\n  static {\n    this.ɵfac = function AddressDetailDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddressDetailDialogComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AddressDetailDialogComponent,\n      selectors: [[\"vdr-address-detail-dialog\"]],\n      standalone: false,\n      decls: 3,\n      vars: 3,\n      consts: [[\"vdrDialogTitle\", \"\"], [3, \"formGroup\", \"availableCountries\", \"customFields\"], [\"vdrDialogButtons\", \"\"], [4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"]],\n      template: function AddressDetailDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AddressDetailDialogComponent_ng_template_0_Template, 2, 2, \"ng-template\", 0);\n          i0.ɵɵelement(1, \"vdr-address-form\", 1);\n          i0.ɵɵtemplate(2, AddressDetailDialogComponent_ng_template_2_Template, 6, 7, \"ng-template\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.addressForm)(\"availableCountries\", ctx.availableCountries)(\"customFields\", ctx.customFields);\n        }\n      },\n      dependencies: [i3$1.NgIf, i1$1.NgControlStatusGroup, i1$1.FormGroupDirective, i1.DialogButtonsDirective, i1.DialogTitleDirective, i1.AddressFormComponent, i4.TranslatePipe],\n      styles: [\"clr-input-container[_ngcontent-%COMP%]{margin-bottom:12px}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AddressDetailDialogComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-address-detail-dialog',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<ng-template vdrDialogTitle>\\n    <span *ngIf=\\\"addressForm.get('streetLine1')?.value as streetLine1\\\">{{ streetLine1 }},</span>\\n    <span *ngIf=\\\"addressForm.get('countryCode')?.value as countryCode\\\"> {{ countryCode }}</span>\\n</ng-template>\\n\\n<vdr-address-form\\n    [formGroup]=\\\"addressForm\\\"\\n    [availableCountries]=\\\"availableCountries\\\"\\n    [customFields]=\\\"customFields\\\"\\n></vdr-address-form>\\n\\n<ng-template vdrDialogButtons>\\n    <button type=\\\"button\\\" class=\\\"btn\\\" (click)=\\\"cancel()\\\">{{ 'common.cancel' | translate }}</button>\\n    <button\\n        type=\\\"submit\\\"\\n        (click)=\\\"save()\\\"\\n        [disabled]=\\\"!addressForm.valid || !addressForm.touched\\\"\\n        class=\\\"btn btn-primary\\\"\\n    >\\n        {{ 'common.update' | translate }}\\n    </button>\\n</ng-template>\\n\",\n      styles: [\"clr-input-container{margin-bottom:12px}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nclass AddressCardComponent {\n  constructor(modalService, changeDetector) {\n    this.modalService = modalService;\n    this.changeDetector = changeDetector;\n    this.availableCountries = [];\n    this.editable = true;\n    this.setAsDefaultShipping = new EventEmitter();\n    this.setAsDefaultBilling = new EventEmitter();\n    this.deleteAddress = new EventEmitter();\n    this.dataDependenciesPopulated = new BehaviorSubject(false);\n  }\n  ngOnInit() {\n    const streetLine1 = this.addressForm.get('streetLine1');\n    // Make the address dialog display automatically if there is no address line\n    // as is the case when adding a new address.\n    if (!streetLine1.value) {\n      this.dataDependenciesPopulated.pipe(filter(value => value), take(1)).subscribe(() => {\n        this.editAddress();\n      });\n    }\n  }\n  ngOnChanges(changes) {\n    if (this.customFields != null && this.availableCountries != null) {\n      this.dataDependenciesPopulated.next(true);\n    }\n  }\n  getCountryName(countryCode) {\n    if (!this.availableCountries) {\n      return '';\n    }\n    const match = this.availableCountries.find(c => c.code === countryCode);\n    return match ? match.name : '';\n  }\n  setAsDefaultBillingAddress() {\n    this.setAsDefaultBilling.emit(this.addressForm.value.id);\n    this.addressForm.markAsDirty();\n  }\n  setAsDefaultShippingAddress() {\n    this.setAsDefaultShipping.emit(this.addressForm.value.id);\n    this.addressForm.markAsDirty();\n  }\n  delete() {\n    this.deleteAddress.emit(this.addressForm.value.id);\n    this.addressForm.markAsDirty();\n  }\n  editAddress() {\n    this.modalService.fromComponent(AddressDetailDialogComponent, {\n      locals: {\n        addressForm: this.addressForm,\n        customFields: this.customFields,\n        availableCountries: this.availableCountries\n      },\n      size: 'md',\n      closable: true\n    }).subscribe(() => {\n      this.changeDetector.markForCheck();\n    });\n  }\n  static {\n    this.ɵfac = function AddressCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddressCardComponent)(i0.ɵɵdirectiveInject(i1.ModalService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AddressCardComponent,\n      selectors: [[\"vdr-address-card\"]],\n      inputs: {\n        addressForm: \"addressForm\",\n        customFields: \"customFields\",\n        availableCountries: \"availableCountries\",\n        isDefaultBilling: \"isDefaultBilling\",\n        isDefaultShipping: \"isDefaultShipping\",\n        editable: \"editable\"\n      },\n      outputs: {\n        setAsDefaultShipping: \"setAsDefaultShipping\",\n        setAsDefaultBilling: \"setAsDefaultBilling\",\n        deleteAddress: \"deleteAddress\"\n      },\n      standalone: false,\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"card\", 4, \"ngIf\"], [1, \"card\"], [1, \"card-header\"], [1, \"address-title\"], [\"class\", \"street-line\", 4, \"ngIf\"], [1, \"default-controls\"], [\"class\", \"is-default p8\", 4, \"ngIf\"], [1, \"card-block\"], [1, \"card-text\"], [3, \"address\"], [1, \"card-footer\"], [1, \"address-actions\"], [3, \"entity\"], [4, \"ngIf\"], [1, \"street-line\"], [1, \"is-default\", \"p8\"], [\"shape\", \"truck\"], [\"shape\", \"credit-card\"], [1, \"button-small\", 3, \"click\"], [\"type\", \"button\", \"vdrDropdownTrigger\", \"\", 1, \"button-small\"], [\"shape\", \"ellipsis-vertical\", \"size\", \"12\"], [\"vdrDropdownItem\", \"\", 3, \"click\", \"disabled\"], [1, \"dropdown-divider\"], [\"type\", \"button\", \"vdrDropdownItem\", \"\", 3, \"click\"], [\"shape\", \"trash\", 1, \"is-danger\"]],\n      template: function AddressCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AddressCardComponent_div_0_Template, 15, 7, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.addressForm.value);\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgIf, i1.ChipComponent, i1.DropdownComponent, i1.DropdownMenuComponent, i1.DropdownTriggerDirective, i1.DropdownItemDirective, i1.FormattedAddressComponent, i1.EntityInfoComponent, i4.TranslatePipe],\n      styles: [\"[_nghost-%COMP%]{display:block;max-width:360px}clr-input-container[_ngcontent-%COMP%]{margin-bottom:12px}.defaul-controls[_ngcontent-%COMP%]{display:flex}.is-default[_ngcontent-%COMP%]{margin:0;color:var(--color-success-500)}.address-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--space-unit)}.address-actions[_ngcontent-%COMP%]   vdr-entity-info[_ngcontent-%COMP%]{margin-top:1px}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AddressCardComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-address-card',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<div class=\\\"card\\\" *ngIf=\\\"addressForm.value as address\\\">\\n    <div class=\\\"card-header\\\">\\n        <div class=\\\"address-title\\\">\\n            <span class=\\\"street-line\\\" *ngIf=\\\"address.streetLine1\\\">{{ address.streetLine1 }},</span>\\n            {{ address.countryCode }}\\n        </div>\\n        <div class=\\\"default-controls\\\">\\n            <vdr-chip class=\\\"is-default p8\\\" *ngIf=\\\"isDefaultShipping\\\">\\n                <clr-icon shape=\\\"truck\\\"></clr-icon>\\n                {{ 'customer.default-shipping-address' | translate }}\\n            </vdr-chip>\\n            <vdr-chip class=\\\"is-default p8\\\" *ngIf=\\\"isDefaultBilling\\\">\\n                <clr-icon shape=\\\"credit-card\\\"></clr-icon>\\n                {{ 'customer.default-billing-address' | translate }}\\n            </vdr-chip>\\n        </div>\\n    </div>\\n    <div class=\\\"card-block\\\">\\n        <div class=\\\"card-text\\\">\\n            <vdr-formatted-address [address]=\\\"address\\\"></vdr-formatted-address>\\n        </div>\\n    </div>\\n    <div class=\\\"card-footer\\\">\\n        <div class=\\\"address-actions\\\">\\n            <vdr-entity-info [entity]=\\\"address\\\"></vdr-entity-info>\\n            <ng-container *ngIf=\\\"editable\\\">\\n                <button class=\\\"button-small\\\" (click)=\\\"editAddress()\\\">\\n                    {{ 'common.edit' | translate }}\\n                </button>\\n                <vdr-dropdown>\\n                    <button type=\\\"button\\\" class=\\\"button-small\\\" vdrDropdownTrigger>\\n                        {{ 'common.more' | translate }}\\n                        <clr-icon shape=\\\"ellipsis-vertical\\\" size=\\\"12\\\"></clr-icon>\\n                    </button>\\n                    <vdr-dropdown-menu>\\n                        <button\\n                            vdrDropdownItem\\n                            [disabled]=\\\"isDefaultShipping\\\"\\n                            (click)=\\\"setAsDefaultShippingAddress()\\\"\\n                        >\\n                            {{ 'customer.set-as-default-shipping-address' | translate }}\\n                        </button>\\n                        <button\\n                            vdrDropdownItem\\n                            [disabled]=\\\"isDefaultBilling\\\"\\n                            (click)=\\\"setAsDefaultBillingAddress()\\\"\\n                        >\\n                            {{ 'customer.set-as-default-billing-address' | translate }}\\n                        </button>\\n                        <div class=\\\"dropdown-divider\\\"></div>\\n                        <button type=\\\"button\\\" (click)=\\\"delete()\\\" vdrDropdownItem>\\n                            <clr-icon shape=\\\"trash\\\" class=\\\"is-danger\\\"></clr-icon>\\n                            {{ 'common.delete' | translate }}\\n                        </button>\\n                    </vdr-dropdown-menu>\\n                </vdr-dropdown>\\n            </ng-container>\\n        </div>\\n    </div>\\n</div>\\n\",\n      styles: [\":host{display:block;max-width:360px}clr-input-container{margin-bottom:12px}.defaul-controls{display:flex}.is-default{margin:0;color:var(--color-success-500)}.address-actions{display:flex;align-items:center;gap:var(--space-unit)}.address-actions vdr-entity-info{margin-top:1px}\\n\"]\n    }]\n  }], () => [{\n    type: i1.ModalService\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    addressForm: [{\n      type: Input\n    }],\n    customFields: [{\n      type: Input\n    }],\n    availableCountries: [{\n      type: Input\n    }],\n    isDefaultBilling: [{\n      type: Input\n    }],\n    isDefaultShipping: [{\n      type: Input\n    }],\n    editable: [{\n      type: Input\n    }],\n    setAsDefaultShipping: [{\n      type: Output\n    }],\n    setAsDefaultBilling: [{\n      type: Output\n    }],\n    deleteAddress: [{\n      type: Output\n    }]\n  });\n})();\nclass SelectCustomerGroupDialogComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.selectedGroupIds = [];\n  }\n  ngOnInit() {\n    this.groups$ = this.dataService.customer.getCustomerGroupList().mapStream(res => res.customerGroups.items);\n  }\n  cancel() {\n    this.resolveWith();\n  }\n  add() {\n    this.resolveWith(this.selectedGroupIds);\n  }\n  static {\n    this.ɵfac = function SelectCustomerGroupDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SelectCustomerGroupDialogComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SelectCustomerGroupDialogComponent,\n      selectors: [[\"vdr-select-customer-group-dialog\"]],\n      standalone: false,\n      decls: 6,\n      vars: 8,\n      consts: [[\"vdrDialogTitle\", \"\"], [\"appendTo\", \"body\", \"bindValue\", \"id\", 3, \"ngModelChange\", \"items\", \"addTag\", \"multiple\", \"ngModel\", \"clearable\", \"searchable\"], [\"ng-label-tmp\", \"\"], [\"ng-option-tmp\", \"\"], [\"vdrDialogButtons\", \"\"], [\"aria-hidden\", \"true\", 1, \"ng-value-icon\", \"left\", 3, \"click\"], [3, \"colorFrom\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"]],\n      template: function SelectCustomerGroupDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SelectCustomerGroupDialogComponent_ng_template_0_Template, 2, 3, \"ng-template\", 0);\n          i0.ɵɵelementStart(1, \"ng-select\", 1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SelectCustomerGroupDialogComponent_Template_ng_select_ngModelChange_1_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedGroupIds, $event) || (ctx.selectedGroupIds = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(3, SelectCustomerGroupDialogComponent_ng_template_3_Template, 4, 2, \"ng-template\", 2)(4, SelectCustomerGroupDialogComponent_ng_template_4_Template, 2, 2, \"ng-template\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, SelectCustomerGroupDialogComponent_ng_template_5_Template, 6, 10, \"ng-template\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(2, 6, ctx.groups$))(\"addTag\", false)(\"multiple\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedGroupIds);\n          i0.ɵɵproperty(\"clearable\", true)(\"searchable\", false);\n        }\n      },\n      dependencies: [i1$1.NgControlStatus, i1$1.NgModel, i3$2.NgSelectComponent, i3$2.NgOptionTemplateDirective, i3$2.NgLabelTemplateDirective, i1.ChipComponent, i1.DialogButtonsDirective, i1.DialogTitleDirective, i3$1.AsyncPipe, i4.TranslatePipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SelectCustomerGroupDialogComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-select-customer-group-dialog',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<ng-template vdrDialogTitle>\\n    {{ 'customer.add-customer-to-group' | translate }}\\n</ng-template>\\n\\n<ng-select\\n    [items]=\\\"groups$ | async\\\"\\n    appendTo=\\\"body\\\"\\n    [addTag]=\\\"false\\\"\\n    [multiple]=\\\"true\\\"\\n    bindValue=\\\"id\\\"\\n    [(ngModel)]=\\\"selectedGroupIds\\\"\\n    [clearable]=\\\"true\\\"\\n    [searchable]=\\\"false\\\"\\n>\\n    <ng-template ng-label-tmp let-item=\\\"item\\\" let-clear=\\\"clear\\\">\\n        <span aria-hidden=\\\"true\\\" class=\\\"ng-value-icon left\\\" (click)=\\\"clear(item)\\\"> \\u00D7 </span>\\n        <vdr-chip [colorFrom]=\\\"item.id\\\">{{ item.name }}</vdr-chip>\\n    </ng-template>\\n    <ng-template ng-option-tmp let-item=\\\"item\\\">\\n        <vdr-chip [colorFrom]=\\\"item.id\\\">{{ item.name }}</vdr-chip>\\n    </ng-template>\\n</ng-select>\\n\\n\\n<ng-template vdrDialogButtons>\\n    <button type=\\\"button\\\" class=\\\"btn\\\" (click)=\\\"cancel()\\\">{{ 'common.cancel' | translate }}</button>\\n    <button type=\\\"submit\\\" (click)=\\\"add()\\\" [disabled]=\\\"!selectedGroupIds.length\\\" class=\\\"btn btn-primary\\\">\\n        {{ 'customer.add-customer-to-groups-with-count' | translate: {count: selectedGroupIds.length} }}\\n    </button>\\n</ng-template>\\n\"\n    }]\n  }], () => [{\n    type: i1.DataService\n  }], null);\n})();\nclass CustomerHistoryEntryHostComponent {\n  constructor(historyEntryComponentService) {\n    this.historyEntryComponentService = historyEntryComponentService;\n    this.expandClick = new EventEmitter();\n  }\n  ngOnInit() {\n    const componentType = this.historyEntryComponentService.getComponent(this.entry.type);\n    const componentRef = this.portalRef.createComponent(componentType);\n    componentRef.instance.entry = this.entry;\n    componentRef.instance.customer = this.customer;\n    this.instance = componentRef.instance;\n    this.componentRef = componentRef;\n  }\n  ngOnDestroy() {\n    this.componentRef?.destroy();\n  }\n  static {\n    this.ɵfac = function CustomerHistoryEntryHostComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerHistoryEntryHostComponent)(i0.ɵɵdirectiveInject(i1.HistoryEntryComponentService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerHistoryEntryHostComponent,\n      selectors: [[\"vdr-customer-history-entry-host\"]],\n      viewQuery: function CustomerHistoryEntryHostComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 7, ViewContainerRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.portalRef = _t.first);\n        }\n      },\n      inputs: {\n        entry: \"entry\",\n        customer: \"customer\",\n        expanded: \"expanded\"\n      },\n      outputs: {\n        expandClick: \"expandClick\"\n      },\n      exportAs: [\"historyEntry\"],\n      standalone: false,\n      decls: 3,\n      vars: 6,\n      consts: [[\"portal\", \"\"], [3, \"expandClick\", \"displayType\", \"iconShape\", \"createdAt\", \"name\", \"featured\", \"collapsed\"]],\n      template: function CustomerHistoryEntryHostComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"vdr-timeline-entry\", 1);\n          i0.ɵɵlistener(\"expandClick\", function CustomerHistoryEntryHostComponent_Template_vdr_timeline_entry_expandClick_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.expandClick.emit());\n          });\n          i0.ɵɵelement(1, \"div\", null, 0);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"displayType\", ctx.instance.getDisplayType(ctx.entry))(\"iconShape\", ctx.instance.getIconShape && ctx.instance.getIconShape(ctx.entry))(\"createdAt\", ctx.entry.createdAt)(\"name\", ctx.instance.getName && ctx.instance.getName(ctx.entry))(\"featured\", ctx.instance.isFeatured(ctx.entry))(\"collapsed\", !ctx.expanded && !ctx.instance.isFeatured(ctx.entry));\n        }\n      },\n      dependencies: [i1.TimelineEntryComponent],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerHistoryEntryHostComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-history-entry-host',\n      template: `\n        <vdr-timeline-entry\n            [displayType]=\"instance.getDisplayType(entry)\"\n            [iconShape]=\"instance.getIconShape && instance.getIconShape(entry)\"\n            [createdAt]=\"entry.createdAt\"\n            [name]=\"instance.getName && instance.getName(entry)\"\n            [featured]=\"instance.isFeatured(entry)\"\n            [collapsed]=\"!expanded && !instance.isFeatured(entry)\"\n            (expandClick)=\"expandClick.emit()\"\n        >\n            <div #portal></div>\n        </vdr-timeline-entry>\n    `,\n      exportAs: 'historyEntry',\n      standalone: false\n    }]\n  }], () => [{\n    type: i1.HistoryEntryComponentService\n  }], {\n    entry: [{\n      type: Input\n    }],\n    customer: [{\n      type: Input\n    }],\n    expanded: [{\n      type: Input\n    }],\n    expandClick: [{\n      type: Output\n    }],\n    portalRef: [{\n      type: ViewChild,\n      args: ['portal', {\n        static: true,\n        read: ViewContainerRef\n      }]\n    }]\n  });\n})();\nclass CustomerHistoryComponent {\n  constructor(historyEntryComponentService) {\n    this.historyEntryComponentService = historyEntryComponentService;\n    this.addNote = new EventEmitter();\n    this.updateNote = new EventEmitter();\n    this.deleteNote = new EventEmitter();\n    this.note = '';\n    this.expanded = false;\n    this.type = HistoryEntryType;\n  }\n  hasCustomComponent(type) {\n    return !!this.historyEntryComponentService.getComponent(type);\n  }\n  getDisplayType(entry) {\n    switch (entry.type) {\n      case HistoryEntryType.CUSTOMER_VERIFIED:\n      case HistoryEntryType.CUSTOMER_EMAIL_UPDATE_VERIFIED:\n      case HistoryEntryType.CUSTOMER_PASSWORD_RESET_VERIFIED:\n        return 'success';\n      case HistoryEntryType.CUSTOMER_REGISTERED:\n        return 'muted';\n      case HistoryEntryType.CUSTOMER_REMOVED_FROM_GROUP:\n        return 'error';\n      default:\n        return 'default';\n    }\n  }\n  getTimelineIcon(entry) {\n    switch (entry.type) {\n      case HistoryEntryType.CUSTOMER_REGISTERED:\n        return 'user';\n      case HistoryEntryType.CUSTOMER_VERIFIED:\n        return ['assign-user', 'is-solid'];\n      case HistoryEntryType.CUSTOMER_NOTE:\n        return 'note';\n      case HistoryEntryType.CUSTOMER_ADDED_TO_GROUP:\n      case HistoryEntryType.CUSTOMER_REMOVED_FROM_GROUP:\n        return 'users';\n    }\n  }\n  isFeatured(entry) {\n    switch (entry.type) {\n      case HistoryEntryType.CUSTOMER_REGISTERED:\n      case HistoryEntryType.CUSTOMER_VERIFIED:\n        return true;\n      default:\n        return false;\n    }\n  }\n  getName(entry) {\n    const {\n      administrator\n    } = entry;\n    if (administrator) {\n      return `${administrator.firstName} ${administrator.lastName}`;\n    } else {\n      return `${this.customer.firstName} ${this.customer.lastName}`;\n    }\n  }\n  addNoteToCustomer() {\n    this.addNote.emit({\n      note: this.note\n    });\n    this.note = '';\n  }\n  static {\n    this.ɵfac = function CustomerHistoryComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerHistoryComponent)(i0.ɵɵdirectiveInject(i1.HistoryEntryComponentService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerHistoryComponent,\n      selectors: [[\"vdr-customer-history\"]],\n      inputs: {\n        customer: \"customer\",\n        history: \"history\"\n      },\n      outputs: {\n        addNote: \"addNote\",\n        updateNote: \"updateNote\",\n        deleteNote: \"deleteNote\"\n      },\n      standalone: false,\n      decls: 4,\n      vars: 3,\n      consts: [[\"defaultComponents\", \"\"], [\"namedStrategy\", \"\"], [1, \"entry-list\"], [\"iconShape\", \"note\", \"displayType\", \"muted\", 3, \"featured\", \"isFirst\", 4, \"vdrIfPermissions\"], [4, \"ngFor\", \"ngForOf\"], [3, \"isLast\"], [\"iconShape\", \"note\", \"displayType\", \"muted\", 3, \"featured\", \"isFirst\"], [1, \"note-entry\"], [\"name\", \"note\", 1, \"note\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", 3, \"click\", \"disabled\"], [3, \"customer\", \"entry\", \"expanded\", \"expandClick\", 4, \"ngIf\", \"ngIfElse\"], [3, \"expandClick\", \"customer\", \"entry\", \"expanded\"], [3, \"displayType\", \"iconShape\", \"createdAt\", \"name\", \"featured\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [1, \"title\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"flex\"], [3, \"value\"], [1, \"address-string\"], [3, \"label\"], [1, \"note-text\"], [1, \"flex-spacer\"], [\"vdrDropdownTrigger\", \"\", 1, \"button-small\", \"ml-1\"], [\"shape\", \"ellipsis-vertical\", \"size\", \"12\"], [\"vdrPosition\", \"bottom-right\"], [\"vdrDropdownItem\", \"\", 3, \"click\", \"disabled\"], [\"shape\", \"edit\"], [1, \"dropdown-divider\"], [\"shape\", \"trash\", 1, \"is-danger\"], [4, \"ngIf\"]],\n      template: function CustomerHistoryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, CustomerHistoryComponent_vdr_timeline_entry_1_Template, 6, 7, \"vdr-timeline-entry\", 3)(2, CustomerHistoryComponent_ng_container_2_Template, 4, 2, \"ng-container\", 4);\n          i0.ɵɵelement(3, \"vdr-timeline-entry\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdateCustomer\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.history);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"isLast\", true);\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgForOf, i3$1.NgIf, i3$1.NgSwitch, i3$1.NgSwitchCase, i3$1.NgSwitchDefault, i1$1.DefaultValueAccessor, i1$1.NgControlStatus, i1$1.NgModel, i1.FormFieldControlDirective, i1.DropdownComponent, i1.DropdownMenuComponent, i1.DropdownTriggerDirective, i1.DropdownItemDirective, i1.LabeledDataComponent, i1.ObjectTreeComponent, i1.IfPermissionsDirective, i1.TimelineEntryComponent, i1.HistoryEntryDetailComponent, CustomerHistoryEntryHostComponent, i4.TranslatePipe, i1.HasPermissionPipe],\n      styles: [\".entry-list[_ngcontent-%COMP%]{margin:24px 12px 24px 24px}.note-entry[_ngcontent-%COMP%]{display:flex;align-items:center}.note-entry[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]{flex:1}.note-entry[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin:0}textarea.note[_ngcontent-%COMP%]{flex:1;height:36px;border-radius:3px;margin-inline-end:6px}.note-text[_ngcontent-%COMP%]{color:var(--color-text-100);white-space:pre-wrap}.address-string[_ngcontent-%COMP%]{font-size:smaller;color:var(--color-text-200)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerHistoryComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-history',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<div class=\\\"entry-list\\\">\\n    <vdr-timeline-entry iconShape=\\\"note\\\" displayType=\\\"muted\\\" [featured]=\\\"true\\\" *vdrIfPermissions=\\\"'UpdateCustomer'\\\"\\n                        [isFirst]=\\\"true\\\">\\n        <div class=\\\"note-entry\\\">\\n            <textarea [(ngModel)]=\\\"note\\\" name=\\\"note\\\" class=\\\"note\\\"></textarea>\\n            <button class=\\\"btn btn-secondary\\\" [disabled]=\\\"!note\\\" (click)=\\\"addNoteToCustomer()\\\">\\n                {{ 'order.add-note' | translate }}\\n            </button>\\n        </div>\\n    </vdr-timeline-entry>\\n    <ng-container *ngFor=\\\"let entry of history\\\">\\n        <vdr-customer-history-entry-host\\n            *ngIf=\\\"hasCustomComponent(entry.type); else defaultComponents\\\"\\n            [customer]=\\\"customer\\\"\\n            [entry]=\\\"entry\\\"\\n            [expanded]=\\\"expanded\\\"\\n            (expandClick)=\\\"expanded = !expanded\\\"\\n        ></vdr-customer-history-entry-host>\\n        <ng-template #defaultComponents>\\n            <vdr-timeline-entry\\n                [displayType]=\\\"getDisplayType(entry)\\\"\\n                [iconShape]=\\\"getTimelineIcon(entry)\\\"\\n                [createdAt]=\\\"entry.createdAt\\\"\\n                [name]=\\\"getName(entry)\\\"\\n                [featured]=\\\"isFeatured(entry)\\\"\\n            >\\n                <ng-container [ngSwitch]=\\\"entry.type\\\">\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_REGISTERED\\\">\\n                        <div class=\\\"title\\\">\\n                            {{ 'customer.history-customer-registered' | translate }}\\n                        </div>\\n                        <ng-container *ngIf=\\\"entry.data.strategy === 'native'; else namedStrategy\\\">\\n                            {{ 'customer.history-using-native-auth-strategy' | translate }}\\n                        </ng-container>\\n                        <ng-template #namedStrategy>\\n                            {{\\n                                'customer.history-using-external-auth-strategy'\\n                                    | translate: { strategy: entry.data.strategy }\\n                            }}\\n                        </ng-template>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_VERIFIED\\\">\\n                        <div class=\\\"title\\\">\\n                            {{ 'customer.history-customer-verified' | translate }}\\n                        </div>\\n                        <ng-container *ngIf=\\\"entry.data.strategy === 'native'; else namedStrategy\\\">\\n                            {{ 'customer.history-using-native-auth-strategy' | translate }}\\n                        </ng-container>\\n                        <ng-template #namedStrategy>\\n                            {{\\n                                'customer.history-using-external-auth-strategy'\\n                                    | translate: { strategy: entry.data.strategy }\\n                            }}\\n                        </ng-template>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_DETAIL_UPDATED\\\">\\n                        <div class=\\\"flex\\\">\\n                            {{ 'customer.history-customer-detail-updated' | translate }}\\n                            <vdr-history-entry-detail>\\n                                <vdr-object-tree [value]=\\\"entry.data.input\\\"></vdr-object-tree>\\n                            </vdr-history-entry-detail>\\n                        </div>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_ADDED_TO_GROUP\\\">\\n                        {{\\n                            'customer.history-customer-added-to-group'\\n                                | translate: { groupName: entry.data.groupName }\\n                        }}\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_REMOVED_FROM_GROUP\\\">\\n                        {{\\n                            'customer.history-customer-removed-from-group'\\n                                | translate: { groupName: entry.data.groupName }\\n                        }}\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_ADDRESS_CREATED\\\">\\n                        {{ 'customer.history-customer-address-created' | translate }}\\n                        <div class=\\\"flex\\\">\\n                            <div class=\\\"address-string\\\">{{ entry.data.address }}</div>\\n                        </div>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_ADDRESS_UPDATED\\\">\\n                        {{ 'customer.history-customer-address-updated' | translate }}\\n                        <div class=\\\"flex\\\">\\n                            <div class=\\\"address-string\\\">{{ entry.data.address }}</div>\\n                            <vdr-history-entry-detail>\\n                                <vdr-object-tree [value]=\\\"entry.data.input\\\"></vdr-object-tree>\\n                            </vdr-history-entry-detail>\\n                        </div>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_ADDRESS_DELETED\\\">\\n                        {{ 'customer.history-customer-address-deleted' | translate }}\\n                        <div class=\\\"address-string\\\">{{ entry.data.address }}</div>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_PASSWORD_UPDATED\\\">\\n                        {{ 'customer.history-customer-password-updated' | translate }}\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_PASSWORD_RESET_REQUESTED\\\">\\n                        {{ 'customer.history-customer-password-reset-requested' | translate }}\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_PASSWORD_RESET_VERIFIED\\\">\\n                        {{ 'customer.history-customer-password-reset-verified' | translate }}\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_EMAIL_UPDATE_REQUESTED\\\">\\n                        <div class=\\\"flex\\\">\\n                            {{ 'customer.history-customer-email-update-requested' | translate }}\\n                            <vdr-history-entry-detail>\\n                                <vdr-labeled-data [label]=\\\"'customer.old-email-address' | translate\\\">{{\\n                                        entry.data.oldEmailAddress\\n                                    }}\\n                                </vdr-labeled-data>\\n                                <vdr-labeled-data [label]=\\\"'customer.new-email-address' | translate\\\">{{\\n                                        entry.data.newEmailAddress\\n                                    }}\\n                                </vdr-labeled-data>\\n                            </vdr-history-entry-detail>\\n                        </div>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_EMAIL_UPDATE_VERIFIED\\\">\\n                        <div class=\\\"flex\\\">\\n                            {{ 'customer.history-customer-email-update-verified' | translate }}\\n                            <vdr-history-entry-detail>\\n                                <vdr-labeled-data [label]=\\\"'customer.old-email-address' | translate\\\">{{\\n                                        entry.data.oldEmailAddress\\n                                    }}\\n                                </vdr-labeled-data>\\n                                <vdr-labeled-data [label]=\\\"'customer.new-email-address' | translate\\\">{{\\n                                        entry.data.newEmailAddress\\n                                    }}\\n                                </vdr-labeled-data>\\n                            </vdr-history-entry-detail>\\n                        </div>\\n                    </ng-container>\\n                    <ng-container *ngSwitchCase=\\\"type.CUSTOMER_NOTE\\\">\\n                        <div class=\\\"flex\\\">\\n                            <div class=\\\"note-text\\\">\\n                                {{ entry.data.note }}\\n                            </div>\\n                            <div class=\\\"flex-spacer\\\"></div>\\n                            <vdr-dropdown>\\n                                <button class=\\\"button-small ml-1\\\" vdrDropdownTrigger>\\n                                    <clr-icon shape=\\\"ellipsis-vertical\\\" size=\\\"12\\\"></clr-icon>\\n                                </button>\\n                                <vdr-dropdown-menu vdrPosition=\\\"bottom-right\\\">\\n                                    <button\\n                                        vdrDropdownItem\\n                                        (click)=\\\"updateNote.emit(entry)\\\"\\n                                        [disabled]=\\\"!('UpdateCustomer' | hasPermission)\\\"\\n                                    >\\n                                        <clr-icon shape=\\\"edit\\\"></clr-icon>\\n                                        {{ 'common.edit' | translate }}\\n                                    </button>\\n                                    <div class=\\\"dropdown-divider\\\"></div>\\n                                    <button\\n                                        vdrDropdownItem\\n                                        (click)=\\\"deleteNote.emit(entry)\\\"\\n                                        [disabled]=\\\"!('UpdateCustomer' | hasPermission)\\\"\\n                                    >\\n                                        <clr-icon shape=\\\"trash\\\" class=\\\"is-danger\\\"></clr-icon>\\n                                        {{ 'common.delete' | translate }}\\n                                    </button>\\n                                </vdr-dropdown-menu>\\n                            </vdr-dropdown>\\n                        </div>\\n                    </ng-container>\\n                    <ng-container *ngSwitchDefault>\\n                        <div class=\\\"title\\\">\\n                            {{ entry.type | translate }}\\n                        </div>\\n                        <vdr-history-entry-detail *ngIf=\\\"entry.data\\\">\\n                            <vdr-object-tree [value]=\\\"entry.data\\\"></vdr-object-tree>\\n                        </vdr-history-entry-detail>\\n                    </ng-container>\\n                </ng-container>\\n            </vdr-timeline-entry>\\n        </ng-template>\\n    </ng-container>\\n    <vdr-timeline-entry [isLast]=\\\"true\\\"></vdr-timeline-entry>\\n</div>\\n\",\n      styles: [\".entry-list{margin:24px 12px 24px 24px}.note-entry{display:flex;align-items:center}.note-entry .note{flex:1}.note-entry button{margin:0}textarea.note{flex:1;height:36px;border-radius:3px;margin-inline-end:6px}.note-text{color:var(--color-text-100);white-space:pre-wrap}.address-string{font-size:smaller;color:var(--color-text-200)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.HistoryEntryComponentService\n  }], {\n    customer: [{\n      type: Input\n    }],\n    history: [{\n      type: Input\n    }],\n    addNote: [{\n      type: Output\n    }],\n    updateNote: [{\n      type: Output\n    }],\n    deleteNote: [{\n      type: Output\n    }]\n  });\n})();\nconst CUSTOMER_DETAIL_QUERY = gql`\n    query CustomerDetailQuery($id: ID!, $orderListOptions: OrderListOptions) {\n        customer(id: $id) {\n            ...Customer\n            groups {\n                id\n                name\n            }\n            orders(options: $orderListOptions) {\n                items {\n                    id\n                    code\n                    type\n                    state\n                    total\n                    totalWithTax\n                    currencyCode\n                    createdAt\n                    updatedAt\n                    orderPlacedAt\n                }\n                totalItems\n            }\n        }\n    }\n    ${CUSTOMER_FRAGMENT}\n`;\nclass CustomerDetailComponent extends TypedBaseDetailComponent {\n  constructor(changeDetector, formBuilder, dataService, modalService, notificationService) {\n    super();\n    this.changeDetector = changeDetector;\n    this.formBuilder = formBuilder;\n    this.dataService = dataService;\n    this.modalService = modalService;\n    this.notificationService = notificationService;\n    this.customFields = this.getCustomFieldConfig('Customer');\n    this.addressCustomFields = this.getCustomFieldConfig('Address');\n    this.detailForm = this.formBuilder.group({\n      customer: this.formBuilder.group({\n        title: '',\n        firstName: ['', Validators.required],\n        lastName: ['', Validators.required],\n        phoneNumber: '',\n        emailAddress: ['', [Validators.required, Validators.email]],\n        password: '',\n        customFields: this.formBuilder.group(getCustomFieldsDefaults(this.customFields))\n      }),\n      addresses: new UntypedFormArray([])\n    });\n    this.fetchHistory = new Subject();\n    this.addressesToDeleteIds = new Set();\n    this.addressDefaultsUpdated = false;\n    this.ordersPerPage = 10;\n    this.currentOrdersPage = 1;\n    this.orderListUpdates$ = new Subject();\n  }\n  ngOnInit() {\n    this.init();\n    this.availableCountries$ = this.dataService.settings.getAvailableCountries().mapSingle(result => result.countries.items).pipe(shareReplay(1));\n    const customerWithUpdates$ = this.entity$.pipe(merge(this.orderListUpdates$));\n    this.orders$ = customerWithUpdates$.pipe(map(customer => customer.orders.items));\n    this.ordersCount$ = this.entity$.pipe(map(customer => customer.orders.totalItems));\n    this.history$ = this.fetchHistory.pipe(startWith(null), switchMap(() => this.dataService.customer.getCustomerHistory(this.id, {\n      sort: {\n        createdAt: SortOrder.DESC\n      }\n    }).mapStream(data => data.customer?.history.items)));\n  }\n  ngOnDestroy() {\n    this.destroy();\n    this.orderListUpdates$.complete();\n  }\n  getAddressFormControls() {\n    const formArray = this.detailForm.get(['addresses']);\n    return formArray.controls;\n  }\n  setDefaultBillingAddressId(id) {\n    this.defaultBillingAddressId = id;\n    this.addressDefaultsUpdated = true;\n  }\n  setDefaultShippingAddressId(id) {\n    this.defaultShippingAddressId = id;\n    this.addressDefaultsUpdated = true;\n  }\n  toggleDeleteAddress(id) {\n    if (this.addressesToDeleteIds.has(id)) {\n      this.addressesToDeleteIds.delete(id);\n    } else {\n      this.addressesToDeleteIds.add(id);\n    }\n  }\n  addAddress() {\n    const addressFormArray = this.detailForm.get('addresses');\n    const newAddress = this.formBuilder.group({\n      fullName: '',\n      company: '',\n      streetLine1: ['', Validators.required],\n      streetLine2: '',\n      city: '',\n      province: '',\n      postalCode: '',\n      countryCode: ['', Validators.required],\n      phoneNumber: '',\n      defaultShippingAddress: false,\n      defaultBillingAddress: false,\n      customFields: this.formBuilder.group(this.addressCustomFields.reduce((hash, field) => ({\n        ...hash,\n        [field.name]: ''\n      }), {}))\n    });\n    addressFormArray.push(newAddress);\n  }\n  setOrderItemsPerPage(itemsPerPage) {\n    this.ordersPerPage = +itemsPerPage;\n    this.fetchOrdersList();\n  }\n  setOrderCurrentPage(page) {\n    this.currentOrdersPage = +page;\n    this.fetchOrdersList();\n  }\n  create() {\n    const customerForm = this.detailForm.get('customer');\n    if (!customerForm) {\n      return;\n    }\n    const {\n      title,\n      emailAddress,\n      firstName,\n      lastName,\n      phoneNumber,\n      password\n    } = customerForm.value;\n    const customFields = customerForm.get('customFields')?.value;\n    if (!emailAddress || !firstName || !lastName) {\n      return;\n    }\n    const customer = {\n      title,\n      emailAddress,\n      firstName,\n      lastName,\n      phoneNumber,\n      customFields\n    };\n    this.dataService.customer.createCustomer(customer, password).subscribe(({\n      createCustomer\n    }) => {\n      switch (createCustomer.__typename) {\n        case 'Customer':\n          this.notificationService.success(marker('common.notify-create-success'), {\n            entity: 'Customer'\n          });\n          if (createCustomer.emailAddress && !password) {\n            this.notificationService.notify({\n              message: marker('customer.email-verification-sent'),\n              translationVars: {\n                emailAddress\n              },\n              type: 'info',\n              duration: 10000\n            });\n          }\n          this.detailForm.markAsPristine();\n          this.addressDefaultsUpdated = false;\n          this.changeDetector.markForCheck();\n          this.router.navigate(['../', createCustomer.id], {\n            relativeTo: this.route\n          });\n          break;\n        case 'EmailAddressConflictError':\n          this.notificationService.error(createCustomer.message);\n      }\n    });\n  }\n  save() {\n    this.entity$.pipe(take(1), mergeMap(({\n      id\n    }) => {\n      const saveOperations = [];\n      const customerForm = this.detailForm.get('customer');\n      if (customerForm && customerForm.dirty) {\n        const formValue = customerForm.value;\n        const customFields = customerForm.get('customFields')?.value;\n        const customer = {\n          id,\n          title: formValue.title,\n          emailAddress: formValue.emailAddress,\n          firstName: formValue.firstName,\n          lastName: formValue.lastName,\n          phoneNumber: formValue.phoneNumber,\n          customFields\n        };\n        saveOperations.push(this.dataService.customer.updateCustomer(customer).pipe(map(res => res.updateCustomer)));\n      }\n      const addressFormArray = this.detailForm.get('addresses');\n      if (addressFormArray && addressFormArray.dirty || this.addressDefaultsUpdated) {\n        for (const addressControl of addressFormArray.controls) {\n          if (addressControl.dirty || this.addressDefaultsUpdated) {\n            const address = addressControl.value;\n            const input = {\n              fullName: address.fullName,\n              company: address.company,\n              streetLine1: address.streetLine1,\n              streetLine2: address.streetLine2,\n              city: address.city,\n              province: address.province,\n              postalCode: address.postalCode,\n              countryCode: address.countryCode,\n              phoneNumber: address.phoneNumber,\n              defaultShippingAddress: this.defaultShippingAddressId === address.id,\n              defaultBillingAddress: this.defaultBillingAddressId === address.id,\n              customFields: address.customFields\n            };\n            if (!address.id) {\n              saveOperations.push(this.dataService.customer.createCustomerAddress(id, input).pipe(map(res => res.createCustomerAddress)));\n            } else {\n              if (this.addressesToDeleteIds.has(address.id)) {\n                saveOperations.push(this.dataService.customer.deleteCustomerAddress(address.id).pipe(map(res => res.deleteCustomerAddress)));\n              } else {\n                saveOperations.push(this.dataService.customer.updateCustomerAddress({\n                  ...input,\n                  id: address.id\n                }).pipe(map(res => res.updateCustomerAddress)));\n              }\n            }\n          }\n        }\n      }\n      return forkJoin(saveOperations);\n    })).subscribe(data => {\n      let notified = false;\n      for (const result of data) {\n        switch (result.__typename) {\n          case 'Customer':\n          case 'Address':\n          case 'Success':\n            if (!notified) {\n              this.notificationService.success(marker('common.notify-update-success'), {\n                entity: 'Customer'\n              });\n              notified = true;\n              this.detailForm.markAsPristine();\n              this.addressDefaultsUpdated = false;\n              this.changeDetector.markForCheck();\n              this.fetchHistory.next();\n              this.refreshCustomer().subscribe();\n            }\n            break;\n          case 'EmailAddressConflictError':\n            this.notificationService.error(result.message);\n            break;\n        }\n      }\n    }, err => {\n      this.notificationService.error(marker('common.notify-update-error'), {\n        entity: 'Customer'\n      });\n    });\n  }\n  addToGroup() {\n    this.modalService.fromComponent(SelectCustomerGroupDialogComponent, {\n      size: 'md'\n    }).pipe(switchMap(groupIds => groupIds ? from(groupIds) : EMPTY), concatMap(groupId => this.dataService.customer.addCustomersToGroup(groupId, [this.id]))).subscribe({\n      next: res => {\n        this.notificationService.success(marker(`customer.add-customers-to-group-success`), {\n          customerCount: 1,\n          groupName: res.addCustomersToGroup.name\n        });\n      },\n      complete: () => {\n        this.refreshCustomer().subscribe();\n        this.fetchHistory.next();\n      }\n    });\n  }\n  removeFromGroup(group) {\n    this.modalService.dialog({\n      title: marker('customer.confirm-remove-customer-from-group'),\n      buttons: [{\n        type: 'secondary',\n        label: marker('common.cancel')\n      }, {\n        type: 'danger',\n        label: marker('common.delete'),\n        returnValue: true\n      }]\n    }).pipe(switchMap(response => response ? this.dataService.customer.removeCustomersFromGroup(group.id, [this.id]) : EMPTY), switchMap(() => this.refreshCustomer())).subscribe(result => {\n      this.notificationService.success(marker(`customer.remove-customers-from-group-success`), {\n        customerCount: 1,\n        groupName: group.name\n      });\n      this.fetchHistory.next();\n    });\n  }\n  addNoteToCustomer({\n    note\n  }) {\n    this.dataService.customer.addNoteToCustomer(this.id, note).subscribe(() => {\n      this.fetchHistory.next();\n      this.notificationService.success(marker('common.notify-create-success'), {\n        entity: 'Note'\n      });\n    });\n  }\n  updateNote(entry) {\n    this.modalService.fromComponent(EditNoteDialogComponent, {\n      closable: true,\n      locals: {\n        displayPrivacyControls: false,\n        note: entry.data.note\n      }\n    }).pipe(switchMap(result => {\n      if (result) {\n        return this.dataService.customer.updateCustomerNote({\n          noteId: entry.id,\n          note: result.note\n        });\n      } else {\n        return EMPTY;\n      }\n    })).subscribe(result => {\n      this.fetchHistory.next();\n      this.notificationService.success(marker('common.notify-update-success'), {\n        entity: 'Note'\n      });\n    });\n  }\n  deleteNote(entry) {\n    return this.modalService.dialog({\n      title: marker('common.confirm-delete-note'),\n      body: entry.data.note,\n      buttons: [{\n        type: 'secondary',\n        label: marker('common.cancel')\n      }, {\n        type: 'danger',\n        label: marker('common.delete'),\n        returnValue: true\n      }]\n    }).pipe(switchMap(res => res ? this.dataService.customer.deleteCustomerNote(entry.id) : EMPTY)).subscribe(() => {\n      this.fetchHistory.next();\n      this.notificationService.success(marker('common.notify-delete-success'), {\n        entity: 'Note'\n      });\n    });\n  }\n  setFormValues(entity) {\n    const customerGroup = this.detailForm.get('customer');\n    if (customerGroup) {\n      customerGroup.patchValue({\n        title: entity.title ?? null,\n        firstName: entity.firstName,\n        lastName: entity.lastName,\n        phoneNumber: entity.phoneNumber ?? null,\n        emailAddress: entity.emailAddress,\n        password: '',\n        customFields: {}\n      });\n    }\n    if (entity.addresses) {\n      const addressesArray = new UntypedFormArray([]);\n      for (const address of entity.addresses) {\n        const {\n          customFields,\n          ...rest\n        } = address;\n        const addressGroup = this.formBuilder.group({\n          ...rest,\n          countryCode: address.country.code,\n          customFields: this.formBuilder.group(this.addressCustomFields.reduce((hash, field) => ({\n            ...hash,\n            [field.name]: address['customFields'][field.name]\n          }), {}))\n        });\n        addressesArray.push(addressGroup);\n        if (address.defaultShippingAddress) {\n          this.defaultShippingAddressId = address.id;\n        }\n        if (address.defaultBillingAddress) {\n          this.defaultBillingAddressId = address.id;\n        }\n      }\n      this.detailForm.setControl('addresses', addressesArray);\n    }\n    if (this.customFields.length) {\n      this.setCustomFieldFormValues(this.customFields, this.detailForm.get(['customer', 'customFields']), entity);\n    }\n    this.changeDetector.markForCheck();\n  }\n  /**\n   * Refetch the customer with the current order list settings.\n   */\n  fetchOrdersList() {\n    this.dataService.query(CustomerDetailQueryDocument, {\n      id: this.id,\n      orderListOptions: {\n        take: this.ordersPerPage,\n        skip: (this.currentOrdersPage - 1) * this.ordersPerPage,\n        sort: {\n          orderPlacedAt: SortOrder.DESC\n        }\n      }\n    }).single$.pipe(map(data => data.customer), filter(notNullOrUndefined)).subscribe(result => this.orderListUpdates$.next(result));\n  }\n  refreshCustomer() {\n    return this.dataService.query(CustomerDetailQueryDocument, {\n      id: this.id,\n      orderListOptions: {\n        take: 0\n      }\n    }).single$;\n  }\n  static {\n    this.ɵfac = function CustomerDetailComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerDetailComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.FormBuilder), i0.ɵɵdirectiveInject(i1.DataService), i0.ɵɵdirectiveInject(i1.ModalService), i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerDetailComponent,\n      selectors: [[\"vdr-customer-detail\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 50,\n      vars: 53,\n      consts: [[\"updateButton\", \"\"], [\"noGroups\", \"\"], [\"locationId\", \"customer-detail\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\", \"ngIfElse\"], [4, \"ngIf\"], [3, \"title\", 4, \"ngIf\"], [3, \"entity\", 4, \"ngIf\"], [1, \"form\", 3, \"formGroup\"], [1, \"form-grid\"], [\"for\", \"title\", 3, \"label\", \"readOnlyToggle\"], [\"id\", \"title\", \"type\", \"text\", \"formControlName\", \"title\"], [\"for\", \"firstName\", 3, \"label\", \"readOnlyToggle\"], [\"id\", \"firstName\", \"type\", \"text\", \"formControlName\", \"firstName\"], [\"for\", \"lastName\", 3, \"label\", \"readOnlyToggle\"], [\"id\", \"lastName\", \"type\", \"text\", \"formControlName\", \"lastName\"], [\"for\", \"emailAddress\", 3, \"label\", \"readOnlyToggle\"], [\"id\", \"emailAddress\", \"type\", \"text\", \"formControlName\", \"emailAddress\"], [\"for\", \"phoneNumber\", 3, \"label\", \"readOnlyToggle\"], [\"id\", \"phoneNumber\", \"type\", \"text\", \"formControlName\", \"phoneNumber\"], [\"for\", \"password\", 3, \"label\", 4, \"ngIf\"], [\"formGroupName\", \"customFields\", 3, \"title\", 4, \"ngIf\"], [\"locationId\", \"customer-detail\", 3, \"entity$\", \"detailForm\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"vdrIfPermissions\"], [3, \"customer\"], [\"class\", \"last-login\", 3, \"label\", 4, \"ngIf\"], [1, \"last-login\", 3, \"label\"], [3, \"dateTime\"], [3, \"title\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"button-small mt-1\", 3, \"click\", 4, \"vdrIfPermissions\"], [\"icon\", \"times\", 3, \"colorFrom\", \"iconClick\", 4, \"ngFor\", \"ngForOf\"], [\"icon\", \"times\", 3, \"iconClick\", \"colorFrom\"], [1, \"color-weight-400\"], [1, \"button-small\", \"mt-1\", 3, \"click\"], [\"shape\", \"plus\"], [3, \"entity\"], [\"for\", \"password\", 3, \"label\"], [\"id\", \"password\", \"type\", \"password\", \"formControlName\", \"password\"], [\"formGroupName\", \"customFields\", 3, \"title\"], [\"entityName\", \"Customer\", 3, \"customFields\", \"customFieldsFormGroup\"], [3, \"to-delete\", \"availableCountries\", \"isDefaultBilling\", \"isDefaultShipping\", \"addressForm\", \"customFields\", \"editable\", \"setAsDefaultBilling\", \"setAsDefaultShipping\", \"deleteAddress\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"btn btn-secondary mt-2\", 3, \"click\", 4, \"vdrIfPermissions\"], [3, \"title\", \"paddingX\"], [\"id\", \"customer-order-list\", 3, \"itemsPerPageChange\", \"pageChange\", \"items\", \"itemsPerPage\", \"totalItems\", \"currentPage\", \"emptyStateLabel\"], [\"id\", \"id\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"created-at\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"code\", 3, \"heading\", \"optional\"], [\"id\", \"order-type\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"state\", 3, \"heading\"], [\"id\", \"total\", 3, \"heading\"], [\"id\", \"updated-at\", 3, \"heading\"], [\"id\", \"placed-at\", 3, \"heading\"], [3, \"customField\", 4, \"ngFor\", \"ngForOf\"], [3, \"addNote\", \"updateNote\", \"deleteNote\", \"customer\", \"history\"], [3, \"setAsDefaultBilling\", \"setAsDefaultShipping\", \"deleteAddress\", \"availableCountries\", \"isDefaultBilling\", \"isDefaultShipping\", \"addressForm\", \"customFields\", \"editable\"], [1, \"btn\", \"btn-secondary\", \"mt-2\", 3, \"click\"], [1, \"button-ghost\", 3, \"routerLink\"], [\"shape\", \"arrow right\"], [3, \"state\"], [3, \"customField\"]],\n      template: function CustomerDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-block\")(1, \"vdr-action-bar\");\n          i0.ɵɵelement(2, \"vdr-ab-left\");\n          i0.ɵɵelementStart(3, \"vdr-ab-right\");\n          i0.ɵɵelement(4, \"vdr-action-bar-items\", 2);\n          i0.ɵɵtemplate(5, CustomerDetailComponent_button_5_Template, 3, 4, \"button\", 3);\n          i0.ɵɵpipe(6, \"async\");\n          i0.ɵɵtemplate(7, CustomerDetailComponent_ng_template_7_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelement(9, \"vdr-action-bar-dropdown-menu\", 2);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"vdr-page-detail-layout\")(11, \"vdr-page-detail-sidebar\");\n          i0.ɵɵtemplate(12, CustomerDetailComponent_vdr_card_12_Template, 3, 2, \"vdr-card\", 4);\n          i0.ɵɵpipe(13, \"async\");\n          i0.ɵɵtemplate(14, CustomerDetailComponent_vdr_card_14_Template, 7, 6, \"vdr-card\", 5);\n          i0.ɵɵpipe(15, \"async\");\n          i0.ɵɵelementStart(16, \"vdr-card\");\n          i0.ɵɵtemplate(17, CustomerDetailComponent_vdr_page_entity_info_17_Template, 1, 1, \"vdr-page-entity-info\", 6);\n          i0.ɵɵpipe(18, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"vdr-page-block\")(20, \"form\", 7)(21, \"vdr-card\")(22, \"div\", 8)(23, \"vdr-form-field\", 9);\n          i0.ɵɵpipe(24, \"translate\");\n          i0.ɵɵpipe(25, \"async\");\n          i0.ɵɵelement(26, \"input\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"div\");\n          i0.ɵɵelementStart(28, \"vdr-form-field\", 11);\n          i0.ɵɵpipe(29, \"translate\");\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵelement(31, \"input\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"vdr-form-field\", 13);\n          i0.ɵɵpipe(33, \"translate\");\n          i0.ɵɵpipe(34, \"async\");\n          i0.ɵɵelement(35, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"vdr-form-field\", 15);\n          i0.ɵɵpipe(37, \"translate\");\n          i0.ɵɵpipe(38, \"async\");\n          i0.ɵɵelement(39, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"vdr-form-field\", 17);\n          i0.ɵɵpipe(41, \"translate\");\n          i0.ɵɵpipe(42, \"async\");\n          i0.ɵɵelement(43, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(44, CustomerDetailComponent_vdr_form_field_44_Template, 3, 3, \"vdr-form-field\", 19);\n          i0.ɵɵpipe(45, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, CustomerDetailComponent_vdr_card_46_Template, 3, 5, \"vdr-card\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(47, \"vdr-custom-detail-component-host\", 21);\n          i0.ɵɵtemplate(48, CustomerDetailComponent_ng_container_48_Template, 42, 58, \"ng-container\", 4);\n          i0.ɵɵpipe(49, \"async\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          const updateButton_r25 = i0.ɵɵreference(8);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(6, 21, ctx.isNew$))(\"ngIfElse\", updateButton_r25);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(13, 23, ctx.entity$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = i0.ɵɵpipeBind1(15, 25, ctx.entity$)) == null ? null : tmp_4_0.groups);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(18, 27, ctx.entity$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.detailForm.get(\"customer\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(24, 29, \"customer.title\"))(\"readOnlyToggle\", !i0.ɵɵpipeBind1(25, 31, ctx.isNew$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(29, 33, \"customer.first-name\"))(\"readOnlyToggle\", !i0.ɵɵpipeBind1(30, 35, ctx.isNew$));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(33, 37, \"customer.last-name\"))(\"readOnlyToggle\", !i0.ɵɵpipeBind1(34, 39, ctx.isNew$));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(37, 41, \"customer.email-address\"))(\"readOnlyToggle\", !i0.ɵɵpipeBind1(38, 43, ctx.isNew$));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(41, 45, \"customer.phone-number\"))(\"readOnlyToggle\", !i0.ɵɵpipeBind1(42, 47, ctx.isNew$));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(45, 49, ctx.isNew$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.customFields.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"entity$\", ctx.entity$)(\"detailForm\", ctx.detailForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(49, 51, ctx.isNew$));\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgForOf, i3$1.NgIf, i1$1.ɵNgNoValidate, i1$1.DefaultValueAccessor, i1$1.NgControlStatus, i1$1.NgControlStatusGroup, i1$1.FormGroupDirective, i1$1.FormControlName, i1$1.FormGroupName, i2.RouterLink, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ActionBarDropdownMenuComponent, i1.ChipComponent, i1.FormFieldComponent, i1.FormFieldControlDirective, i1.OrderStateLabelComponent, i1.LabeledDataComponent, i1.IfPermissionsDirective, i1.ActionBarItemsComponent, i1.TabbedCustomFieldsComponent, i1.CustomDetailComponentHostComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i1.DataTableCustomFieldColumnComponent, i1.PageBlockComponent, i1.PageEntityInfoComponent, i1.PageDetailLayoutComponent, i1.PageDetailSidebarComponent, i1.CardComponent, CustomerStatusLabelComponent, AddressCardComponent, CustomerHistoryComponent, i3$1.AsyncPipe, i4.TranslatePipe, i1.HasPermissionPipe, i1.TimeAgoPipe, i1.LocaleDatePipe, i1.LocaleCurrencyPipe],\n      styles: [\".last-login[_ngcontent-%COMP%]{margin-inline-start:6px;color:var(--color-grey-500)}.to-delete[_ngcontent-%COMP%]{opacity:.5}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerDetailComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-detail',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-block>\\n    <vdr-action-bar>\\n        <vdr-ab-left> </vdr-ab-left>\\n\\n        <vdr-ab-right>\\n            <vdr-action-bar-items locationId=\\\"customer-detail\\\"></vdr-action-bar-items>\\n            <button\\n                class=\\\"btn btn-primary\\\"\\n                *ngIf=\\\"isNew$ | async; else updateButton\\\"\\n                (click)=\\\"create()\\\"\\n                [disabled]=\\\"!(addressDefaultsUpdated || (detailForm.valid && detailForm.dirty))\\\"\\n            >\\n                {{ 'common.create' | translate }}\\n            </button>\\n            <ng-template #updateButton>\\n                <button\\n                    *vdrIfPermissions=\\\"'UpdateCustomer'\\\"\\n                    class=\\\"btn btn-primary\\\"\\n                    (click)=\\\"save()\\\"\\n                    [disabled]=\\\"!(addressDefaultsUpdated || (detailForm.valid && detailForm.dirty))\\\"\\n                >\\n                    {{ 'common.update' | translate }}\\n                </button>\\n            </ng-template>\\n            <vdr-action-bar-dropdown-menu locationId=\\\"customer-detail\\\" />\\n        </vdr-ab-right>\\n    </vdr-action-bar>\\n</vdr-page-block>\\n<vdr-page-detail-layout>\\n    <vdr-page-detail-sidebar>\\n        <vdr-card *ngIf=\\\"entity$ | async as customer\\\">\\n            <vdr-customer-status-label [customer]=\\\"customer\\\"></vdr-customer-status-label>\\n            <vdr-labeled-data\\n                class=\\\"last-login\\\"\\n                *ngIf=\\\"customer.user?.lastLogin as lastLogin\\\"\\n                [label]=\\\"'customer.last-login' | translate\\\"\\n            >\\n                <time [dateTime]=\\\"lastLogin\\\">{{ lastLogin | timeAgo }}</time>\\n            </vdr-labeled-data>\\n        </vdr-card>\\n        <vdr-card\\n            [title]=\\\"'customer.customer-groups' | translate\\\"\\n            *ngIf=\\\"(entity$ | async)?.groups as groups\\\"\\n        >\\n            <div *ngIf=\\\"groups.length; else noGroups\\\">\\n                <vdr-chip\\n                    *ngFor=\\\"let group of groups\\\"\\n                    [colorFrom]=\\\"group.id\\\"\\n                    icon=\\\"times\\\"\\n                    (iconClick)=\\\"removeFromGroup(group)\\\"\\n                    >{{ group.name }}</vdr-chip\\n                >\\n            </div>\\n            <ng-template #noGroups>\\n                <span class=\\\"color-weight-400\\\">\\n                    {{ 'customer.not-a-member-of-any-groups' | translate }}\\n                </span>\\n            </ng-template>\\n            <div>\\n                <button\\n                    class=\\\"button-small mt-1\\\"\\n                    (click)=\\\"addToGroup()\\\"\\n                    *vdrIfPermissions=\\\"'UpdateCustomerGroup'\\\"\\n                >\\n                    <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                    {{ 'customer.add-customer-to-group' | translate }}\\n                </button>\\n            </div>\\n        </vdr-card>\\n        <vdr-card>\\n            <vdr-page-entity-info *ngIf=\\\"entity$ | async as entity\\\" [entity]=\\\"entity\\\" />\\n        </vdr-card>\\n    </vdr-page-detail-sidebar>\\n    <vdr-page-block>\\n        <form class=\\\"form\\\" [formGroup]=\\\"detailForm.get('customer')\\\">\\n            <vdr-card>\\n                <div class=\\\"form-grid\\\">\\n                    <vdr-form-field\\n                        [label]=\\\"'customer.title' | translate\\\"\\n                        for=\\\"title\\\"\\n                        [readOnlyToggle]=\\\"!(isNew$ | async)\\\"\\n                    >\\n                        <input id=\\\"title\\\" type=\\\"text\\\" formControlName=\\\"title\\\" />\\n                    </vdr-form-field>\\n                    <div><!-- spacer --></div>\\n                    <vdr-form-field\\n                        [label]=\\\"'customer.first-name' | translate\\\"\\n                        for=\\\"firstName\\\"\\n                        [readOnlyToggle]=\\\"!(isNew$ | async)\\\"\\n                    >\\n                        <input id=\\\"firstName\\\" type=\\\"text\\\" formControlName=\\\"firstName\\\" />\\n                    </vdr-form-field>\\n                    <vdr-form-field\\n                        [label]=\\\"'customer.last-name' | translate\\\"\\n                        for=\\\"lastName\\\"\\n                        [readOnlyToggle]=\\\"!(isNew$ | async)\\\"\\n                    >\\n                        <input id=\\\"lastName\\\" type=\\\"text\\\" formControlName=\\\"lastName\\\" />\\n                    </vdr-form-field>\\n                    <vdr-form-field\\n                        [label]=\\\"'customer.email-address' | translate\\\"\\n                        for=\\\"emailAddress\\\"\\n                        [readOnlyToggle]=\\\"!(isNew$ | async)\\\"\\n                    >\\n                        <input id=\\\"emailAddress\\\" type=\\\"text\\\" formControlName=\\\"emailAddress\\\" />\\n                    </vdr-form-field>\\n                    <vdr-form-field\\n                        [label]=\\\"'customer.phone-number' | translate\\\"\\n                        for=\\\"phoneNumber\\\"\\n                        [readOnlyToggle]=\\\"!(isNew$ | async)\\\"\\n                    >\\n                        <input id=\\\"phoneNumber\\\" type=\\\"text\\\" formControlName=\\\"phoneNumber\\\" />\\n                    </vdr-form-field>\\n                    <vdr-form-field\\n                        [label]=\\\"'customer.password' | translate\\\"\\n                        for=\\\"password\\\"\\n                        *ngIf=\\\"isNew$ | async\\\"\\n                    >\\n                        <input id=\\\"password\\\" type=\\\"password\\\" formControlName=\\\"password\\\" />\\n                    </vdr-form-field>\\n                </div>\\n            </vdr-card>\\n            <vdr-card\\n                formGroupName=\\\"customFields\\\"\\n                *ngIf=\\\"customFields.length\\\"\\n                [title]=\\\"'common.custom-fields' | translate\\\"\\n            >\\n                <vdr-tabbed-custom-fields\\n                    entityName=\\\"Customer\\\"\\n                    [customFields]=\\\"customFields\\\"\\n                    [customFieldsFormGroup]=\\\"detailForm.get('customer.customFields')\\\"\\n                ></vdr-tabbed-custom-fields>\\n            </vdr-card>\\n        </form>\\n        <vdr-custom-detail-component-host\\n            locationId=\\\"customer-detail\\\"\\n            [entity$]=\\\"entity$\\\"\\n            [detailForm]=\\\"detailForm\\\"\\n        ></vdr-custom-detail-component-host>\\n        <ng-container *ngIf=\\\"!(isNew$ | async)\\\">\\n            <vdr-card [title]=\\\"'customer.addresses' | translate\\\">\\n                <div class=\\\"form-grid\\\">\\n                    <vdr-address-card\\n                        *ngFor=\\\"let addressForm of getAddressFormControls()\\\"\\n                        [class.to-delete]=\\\"addressesToDeleteIds.has(addressForm.value.id)\\\"\\n                        [availableCountries]=\\\"availableCountries$ | async\\\"\\n                        [isDefaultBilling]=\\\"defaultBillingAddressId === addressForm.value.id\\\"\\n                        [isDefaultShipping]=\\\"defaultShippingAddressId === addressForm.value.id\\\"\\n                        [addressForm]=\\\"addressForm\\\"\\n                        [customFields]=\\\"addressCustomFields\\\"\\n                        [editable]=\\\"\\n                            (['UpdateCustomer'] | hasPermission) &&\\n                            !addressesToDeleteIds.has(addressForm.value.id)\\n                        \\\"\\n                        (setAsDefaultBilling)=\\\"setDefaultBillingAddressId($event)\\\"\\n                        (setAsDefaultShipping)=\\\"setDefaultShippingAddressId($event)\\\"\\n                        (deleteAddress)=\\\"toggleDeleteAddress($event)\\\"\\n                    ></vdr-address-card>\\n                </div>\\n                <button\\n                    class=\\\"btn btn-secondary mt-2\\\"\\n                    (click)=\\\"addAddress()\\\"\\n                    *vdrIfPermissions=\\\"'UpdateCustomer'\\\"\\n                >\\n                    <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                    {{ 'customer.create-new-address' | translate }}\\n                </button>\\n            </vdr-card>\\n            <vdr-card [title]=\\\"'customer.orders' | translate\\\" [paddingX]=\\\"false\\\">\\n                <vdr-data-table-2\\n                    id=\\\"customer-order-list\\\"\\n                    [items]=\\\"orders$ | async\\\"\\n                    [itemsPerPage]=\\\"ordersPerPage\\\"\\n                    [totalItems]=\\\"ordersCount$ | async\\\"\\n                    [currentPage]=\\\"currentOrdersPage\\\"\\n                    [emptyStateLabel]=\\\"'customer.no-orders-placed' | translate\\\"\\n                    (itemsPerPageChange)=\\\"setOrderItemsPerPage($event)\\\"\\n                    (pageChange)=\\\"setOrderCurrentPage($event)\\\"\\n                >\\n                    <vdr-dt2-column [heading]=\\\"'common.id' | translate\\\" id=\\\"id\\\" [hiddenByDefault]=\\\"true\\\">\\n                        <ng-template let-order=\\\"item\\\">\\n                            {{ order.id }}\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-column\\n                        [heading]=\\\"'common.created-at' | translate\\\"\\n                        id=\\\"created-at\\\"\\n                        [hiddenByDefault]=\\\"true\\\"\\n                    >\\n                        <ng-template let-order=\\\"item\\\">\\n                            {{ order.createdAt | localeDate : 'short' }}\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-column [heading]=\\\"'common.code' | translate\\\" id=\\\"code\\\" [optional]=\\\"false\\\">\\n                        <ng-template let-order=\\\"item\\\">\\n                            <a class=\\\"button-ghost\\\" [routerLink]=\\\"['/orders', order.id]\\\"\\n                                ><span>{{ order.code }}</span>\\n                                <clr-icon shape=\\\"arrow right\\\"></clr-icon>\\n                            </a>\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-column\\n                        [heading]=\\\"'order.order-type' | translate\\\"\\n                        id=\\\"order-type\\\"\\n                        [hiddenByDefault]=\\\"true\\\"\\n                    >\\n                        <ng-template let-order=\\\"item\\\">\\n                            <vdr-chip>{{ order.type }}</vdr-chip>\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-column [heading]=\\\"'order.state' | translate\\\" id=\\\"state\\\">\\n                        <ng-template let-order=\\\"item\\\">\\n                            <vdr-order-state-label [state]=\\\"order.state\\\"></vdr-order-state-label>\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-column [heading]=\\\"'order.total' | translate\\\" id=\\\"total\\\">\\n                        <ng-template let-order=\\\"item\\\">\\n                            {{ order.totalWithTax | localeCurrency : order.currencyCode }}\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-column [heading]=\\\"'common.updated-at' | translate\\\" id=\\\"updated-at\\\">\\n                        <ng-template let-order=\\\"item\\\">\\n                            {{ order.updatedAt | timeAgo }}\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-column [heading]=\\\"'order.placed-at' | translate\\\" id=\\\"placed-at\\\">\\n                        <ng-template let-order=\\\"item\\\">\\n                            {{ order.orderPlacedAt | localeDate : 'short' }}\\n                        </ng-template>\\n                    </vdr-dt2-column>\\n                    <vdr-dt2-custom-field-column\\n                        *ngFor=\\\"let customField of customFields\\\"\\n                        [customField]=\\\"customField\\\"\\n                    />\\n                </vdr-data-table-2>\\n            </vdr-card>\\n            <vdr-card [title]=\\\"'customer.customer-history' | translate\\\">\\n                <vdr-customer-history\\n                    [customer]=\\\"entity$ | async\\\"\\n                    [history]=\\\"history$ | async\\\"\\n                    (addNote)=\\\"addNoteToCustomer($event)\\\"\\n                    (updateNote)=\\\"updateNote($event)\\\"\\n                    (deleteNote)=\\\"deleteNote($event)\\\"\\n                ></vdr-customer-history>\\n            </vdr-card>\\n        </ng-container>\\n    </vdr-page-block>\\n</vdr-page-detail-layout>\\n\",\n      styles: [\".last-login{margin-inline-start:6px;color:var(--color-grey-500)}.to-delete{opacity:.5}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.FormBuilder\n  }, {\n    type: i1.DataService\n  }, {\n    type: i1.ModalService\n  }, {\n    type: i1.NotificationService\n  }], null);\n})();\nconst CUSTOMER_GROUP_DETAIL_QUERY = gql`\n    query GetCustomerGroupDetail($id: ID!) {\n        customerGroup(id: $id) {\n            ...CustomerGroupDetail\n        }\n    }\n    fragment CustomerGroupDetail on CustomerGroup {\n        id\n        createdAt\n        updatedAt\n        name\n    }\n`;\nclass CustomerGroupDetailComponent extends TypedBaseDetailComponent {\n  constructor(formBuilder, dataService, modalService, notificationService) {\n    super();\n    this.formBuilder = formBuilder;\n    this.dataService = dataService;\n    this.modalService = modalService;\n    this.notificationService = notificationService;\n    this.customFields = this.getCustomFieldConfig('CustomerGroup');\n    this.detailForm = this.formBuilder.group({\n      name: '',\n      customFields: this.formBuilder.group(getCustomFieldsDefaults(this.customFields))\n    });\n  }\n  ngOnInit() {\n    super.init();\n  }\n  create() {\n    const formvalue = this.detailForm.value;\n    if (formvalue.name) {\n      this.dataService.customer.createCustomerGroup({\n        name: formvalue.name,\n        customFields: formvalue.customFields,\n        customerIds: []\n      }).subscribe(({\n        createCustomerGroup\n      }) => {\n        this.notificationService.success(marker('common.notify-create-success'), {\n          entity: 'CustomerGroup'\n        });\n        this.detailForm.markAsPristine();\n        this.router.navigate(['../', createCustomerGroup.id], {\n          relativeTo: this.route\n        });\n      }, err => {\n        this.notificationService.error(marker('common.notify-create-error'), {\n          entity: 'CustomerGroup'\n        });\n      });\n    }\n  }\n  save() {\n    const formValue = this.detailForm.value;\n    this.dataService.customer.updateCustomerGroup({\n      id: this.id,\n      ...formValue\n    }).subscribe(() => {\n      this.notificationService.success(marker('common.notify-update-success'), {\n        entity: 'CustomerGroup'\n      });\n      this.detailForm.markAsPristine();\n    }, err => {\n      this.notificationService.error(marker('common.notify-update-error'), {\n        entity: 'CustomerGroup'\n      });\n    });\n  }\n  setFormValues(entity) {\n    this.detailForm.patchValue({\n      name: entity.name\n    });\n    if (this.customFields.length) {\n      const customFieldsGroup = this.detailForm.get(['customFields']);\n      this.setCustomFieldFormValues(this.customFields, this.detailForm.get('customFields'), entity);\n    }\n  }\n  static {\n    this.ɵfac = function CustomerGroupDetailComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerGroupDetailComponent)(i0.ɵɵdirectiveInject(i1$1.FormBuilder), i0.ɵɵdirectiveInject(i1.DataService), i0.ɵɵdirectiveInject(i1.ModalService), i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerGroupDetailComponent,\n      selectors: [[\"vdr-customer-group-detail\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 23,\n      vars: 14,\n      consts: [[\"updateButton\", \"\"], [\"locationId\", \"customer-group-detail\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\", \"ngIfElse\"], [1, \"form\", 3, \"formGroup\"], [4, \"ngIf\"], [1, \"form-grid\"], [\"for\", \"name\", 3, \"label\"], [\"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\"], [\"formGroupName\", \"customFields\", 3, \"title\", 4, \"ngIf\"], [\"locationId\", \"customer-group-detail\", 3, \"entity$\", \"detailForm\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"vdrIfPermissions\"], [3, \"entity\"], [\"formGroupName\", \"customFields\", 3, \"title\"], [\"entityName\", \"CustomerGroup\", 3, \"customFields\", \"customFieldsFormGroup\"]],\n      template: function CustomerGroupDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-block\")(1, \"vdr-action-bar\");\n          i0.ɵɵelement(2, \"vdr-ab-left\");\n          i0.ɵɵelementStart(3, \"vdr-ab-right\");\n          i0.ɵɵelement(4, \"vdr-action-bar-items\", 1);\n          i0.ɵɵtemplate(5, CustomerGroupDetailComponent_button_5_Template, 3, 4, \"button\", 2);\n          i0.ɵɵpipe(6, \"async\");\n          i0.ɵɵtemplate(7, CustomerGroupDetailComponent_ng_template_7_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelement(9, \"vdr-action-bar-dropdown-menu\", 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"form\", 3)(11, \"vdr-page-detail-layout\")(12, \"vdr-page-detail-sidebar\");\n          i0.ɵɵtemplate(13, CustomerGroupDetailComponent_vdr_card_13_Template, 2, 1, \"vdr-card\", 4);\n          i0.ɵɵpipe(14, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"vdr-page-block\")(16, \"vdr-card\")(17, \"div\", 5)(18, \"vdr-form-field\", 6);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵelement(20, \"input\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(21, CustomerGroupDetailComponent_vdr_card_21_Template, 3, 5, \"vdr-card\", 8);\n          i0.ɵɵelement(22, \"vdr-custom-detail-component-host\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const updateButton_r5 = i0.ɵɵreference(8);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(6, 8, ctx.isNew$))(\"ngIfElse\", updateButton_r5);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.detailForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(14, 10, ctx.entity$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(19, 12, \"common.name\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.customFields.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"entity$\", ctx.entity$)(\"detailForm\", ctx.detailForm);\n        }\n      },\n      dependencies: [i3$1.NgIf, i1$1.ɵNgNoValidate, i1$1.DefaultValueAccessor, i1$1.NgControlStatus, i1$1.NgControlStatusGroup, i1$1.FormGroupDirective, i1$1.FormControlName, i1$1.FormGroupName, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ActionBarDropdownMenuComponent, i1.FormFieldComponent, i1.FormFieldControlDirective, i1.IfPermissionsDirective, i1.ActionBarItemsComponent, i1.TabbedCustomFieldsComponent, i1.CustomDetailComponentHostComponent, i1.PageBlockComponent, i1.PageEntityInfoComponent, i1.PageDetailLayoutComponent, i1.PageDetailSidebarComponent, i1.CardComponent, i3$1.AsyncPipe, i4.TranslatePipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerGroupDetailComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-group-detail',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-block>\\n    <vdr-action-bar>\\n        <vdr-ab-left> </vdr-ab-left>\\n\\n        <vdr-ab-right>\\n            <vdr-action-bar-items locationId=\\\"customer-group-detail\\\"></vdr-action-bar-items>\\n            <button\\n                class=\\\"btn btn-primary\\\"\\n                *ngIf=\\\"isNew$ | async; else updateButton\\\"\\n                (click)=\\\"create()\\\"\\n                [disabled]=\\\"!(detailForm.valid && detailForm.dirty)\\\"\\n            >\\n                {{ 'common.create' | translate }}\\n            </button>\\n            <ng-template #updateButton>\\n                <button\\n                    *vdrIfPermissions=\\\"'UpdateCustomer'\\\"\\n                    class=\\\"btn btn-primary\\\"\\n                    (click)=\\\"save()\\\"\\n                    [disabled]=\\\"!(detailForm.valid && detailForm.dirty)\\\"\\n                >\\n                    {{ 'common.update' | translate }}\\n                </button>\\n            </ng-template>\\n            <vdr-action-bar-dropdown-menu locationId=\\\"customer-group-detail\\\" />\\n        </vdr-ab-right>\\n    </vdr-action-bar>\\n</vdr-page-block>\\n<form class=\\\"form\\\" [formGroup]=\\\"detailForm\\\">\\n    <vdr-page-detail-layout>\\n        <vdr-page-detail-sidebar>\\n            <vdr-card *ngIf=\\\"entity$ | async as entity\\\">\\n                <vdr-page-entity-info [entity]=\\\"entity\\\" />\\n            </vdr-card>\\n        </vdr-page-detail-sidebar>\\n        <vdr-page-block>\\n            <vdr-card>\\n                <div class=\\\"form-grid\\\">\\n                    <vdr-form-field [label]=\\\"'common.name' | translate\\\" for=\\\"name\\\">\\n                        <input id=\\\"name\\\" type=\\\"text\\\" formControlName=\\\"name\\\" />\\n                    </vdr-form-field>\\n                </div>\\n            </vdr-card>\\n            <vdr-card\\n                formGroupName=\\\"customFields\\\"\\n                *ngIf=\\\"customFields.length\\\"\\n                [title]=\\\"'common.custom-fields' | translate\\\"\\n            >\\n                <vdr-tabbed-custom-fields\\n                    entityName=\\\"CustomerGroup\\\"\\n                    [customFields]=\\\"customFields\\\"\\n                    [customFieldsFormGroup]=\\\"detailForm.get('customFields')\\\"\\n                ></vdr-tabbed-custom-fields>\\n            </vdr-card>\\n            <vdr-custom-detail-component-host\\n                locationId=\\\"customer-group-detail\\\"\\n                [entity$]=\\\"entity$\\\"\\n                [detailForm]=\\\"detailForm\\\"\\n            ></vdr-custom-detail-component-host>\\n        </vdr-page-block>\\n    </vdr-page-detail-layout>\\n</form>\\n\"\n    }]\n  }], () => [{\n    type: i1$1.FormBuilder\n  }, {\n    type: i1.DataService\n  }, {\n    type: i1.ModalService\n  }, {\n    type: i1.NotificationService\n  }], null);\n})();\nclass CustomerGroupDetailDialogComponent {\n  constructor(serverConfigService, formBuilder) {\n    this.serverConfigService = serverConfigService;\n    this.formBuilder = formBuilder;\n    this.customFields = this.serverConfigService.getCustomFieldsFor('CustomerGroup');\n  }\n  ngOnInit() {\n    this.form = this.formBuilder.group({\n      name: [this.group.name, Validators.required],\n      customFields: this.formBuilder.group(getCustomFieldsDefaults(this.customFields))\n    });\n    if (this.customFields.length) {\n      const customFieldsGroup = this.form.get('customFields');\n      for (const fieldDef of this.customFields) {\n        const key = fieldDef.name;\n        const value = this.group.customFields?.[key];\n        const control = customFieldsGroup.get(key);\n        if (control) {\n          control.patchValue(value);\n        }\n      }\n    }\n  }\n  cancel() {\n    this.resolveWith();\n  }\n  save() {\n    this.resolveWith(this.form.value);\n  }\n  static {\n    this.ɵfac = function CustomerGroupDetailDialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerGroupDetailDialogComponent)(i0.ɵɵdirectiveInject(i1.ServerConfigService), i0.ɵɵdirectiveInject(i1$1.UntypedFormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerGroupDetailDialogComponent,\n      selectors: [[\"vdr-customer-group-detail-dialog\"]],\n      standalone: false,\n      decls: 8,\n      vars: 9,\n      consts: [[\"vdrDialogTitle\", \"\"], [3, \"formGroup\"], [\"for\", \"name\", 3, \"label\"], [\"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", 3, \"readonly\"], [\"formGroupName\", \"customFields\", 4, \"ngIf\"], [\"vdrDialogButtons\", \"\"], [4, \"ngIf\"], [\"formGroupName\", \"customFields\"], [\"entityName\", \"CustomerGroup\", 3, \"customFields\", \"customFieldsFormGroup\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"]],\n      template: function CustomerGroupDetailDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CustomerGroupDetailDialogComponent_ng_template_0_Template, 2, 2, \"ng-template\", 0);\n          i0.ɵɵelementStart(1, \"form\", 1)(2, \"vdr-form-field\", 2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelement(4, \"input\", 3);\n          i0.ɵɵpipe(5, \"hasPermission\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, CustomerGroupDetailDialogComponent_section_6_Template, 5, 5, \"section\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, CustomerGroupDetailDialogComponent_ng_template_7_Template, 6, 6, \"ng-template\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(3, 4, \"common.name\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"readonly\", !i0.ɵɵpipeBind1(5, 6, i0.ɵɵpureFunction0(8, _c7)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.customFields.length);\n        }\n      },\n      dependencies: [i3.ClrLabel, i3$1.NgIf, i1$1.ɵNgNoValidate, i1$1.DefaultValueAccessor, i1$1.NgControlStatus, i1$1.NgControlStatusGroup, i1$1.FormGroupDirective, i1$1.FormControlName, i1$1.FormGroupName, i1.FormFieldComponent, i1.FormFieldControlDirective, i1.DialogButtonsDirective, i1.DialogTitleDirective, i1.TabbedCustomFieldsComponent, i4.TranslatePipe, i1.HasPermissionPipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerGroupDetailDialogComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-group-detail-dialog',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<ng-template vdrDialogTitle>\\n    <span *ngIf=\\\"group.id\\\">{{ 'customer.update-customer-group' | translate }}</span>\\n    <span *ngIf=\\\"!group.id\\\">{{ 'customer.create-customer-group' | translate }}</span>\\n</ng-template>\\n<form [formGroup]=\\\"form\\\">\\n    <vdr-form-field [label]=\\\"'common.name' | translate\\\" for=\\\"name\\\">\\n        <input\\n            id=\\\"name\\\"\\n            type=\\\"text\\\"\\n            formControlName=\\\"name\\\"\\n            [readonly]=\\\"!(['CreateCustomerGroup', 'UpdateCustomerGroup'] | hasPermission)\\\"\\n        />\\n    </vdr-form-field>\\n    <section formGroupName=\\\"customFields\\\" *ngIf=\\\"customFields.length\\\">\\n        <label>{{ 'common.custom-fields' | translate }}</label>\\n        <vdr-tabbed-custom-fields\\n            entityName=\\\"CustomerGroup\\\"\\n            [customFields]=\\\"customFields\\\"\\n            [customFieldsFormGroup]=\\\"form.get('customFields')\\\"\\n        ></vdr-tabbed-custom-fields>\\n    </section>\\n</form>\\n<ng-template vdrDialogButtons>\\n    <button type=\\\"button\\\" class=\\\"btn\\\" (click)=\\\"cancel()\\\">{{ 'common.cancel' | translate }}</button>\\n    <button type=\\\"submit\\\" (click)=\\\"save()\\\" [disabled]=\\\"!form.valid\\\" class=\\\"btn btn-primary\\\">\\n        <span *ngIf=\\\"group.id\\\">{{ 'customer.update-customer-group' | translate }}</span>\\n        <span *ngIf=\\\"!group.id\\\">{{ 'customer.create-customer-group' | translate }}</span>\\n    </button>\\n</ng-template>\\n\"\n    }]\n  }], () => [{\n    type: i1.ServerConfigService\n  }, {\n    type: i1$1.UntypedFormBuilder\n  }], null);\n})();\nconst deleteCustomerGroupsBulkAction = createBulkDeleteAction({\n  location: 'customer-group-list',\n  requiresPermission: userPermissions => userPermissions.includes(Permission.DeleteCustomerGroup),\n  getItemName: item => item.name,\n  bulkDelete: (dataService, ids) => dataService.customer.deleteCustomerGroups(ids).pipe(map(res => res.deleteCustomerGroups))\n});\nconst GET_CUSTOMER_GROUP_LIST = gql`\n    query GetCustomerGroupList($options: CustomerGroupListOptions) {\n        customerGroups(options: $options) {\n            items {\n                ...CustomerGroup\n            }\n            totalItems\n        }\n    }\n    ${CUSTOMER_GROUP_FRAGMENT}\n`;\nclass CustomerGroupListComponent extends TypedBaseListComponent {\n  constructor(dataService, notificationService, modalService, route, router) {\n    super();\n    this.dataService = dataService;\n    this.notificationService = notificationService;\n    this.modalService = modalService;\n    this.route = route;\n    this.router = router;\n    this.dataTableListId = 'customer-group-list';\n    this.customFields = this.getCustomFieldConfig('CustomerGroup');\n    this.fetchGroupMembers$ = new BehaviorSubject({\n      skip: 0,\n      take: 0,\n      filterTerm: ''\n    });\n    this.filters = this.createFilterCollection().addIdFilter().addDateFilters().addFilter({\n      name: 'name',\n      type: {\n        kind: 'text'\n      },\n      label: marker('common.name'),\n      filterField: 'name'\n    }).connectToRoute(this.route);\n    this.sorts = this.createSortCollection().defaultSort('createdAt', 'DESC').addSort({\n      name: 'createdAt'\n    }).addSort({\n      name: 'updatedAt'\n    }).addSort({\n      name: 'name'\n    }).connectToRoute(this.route);\n    this.refreshActiveGroupMembers$ = new BehaviorSubject(undefined);\n    super.configure({\n      document: GetCustomerGroupListDocument,\n      getItems: data => data.customerGroups,\n      setVariables: (skip, take) => ({\n        options: {\n          skip,\n          take,\n          filter: {\n            name: {\n              contains: this.searchTermControl.value\n            },\n            ...this.filters.createFilterInput()\n          },\n          sort: this.sorts.createSortInput()\n        }\n      }),\n      refreshListOnChanges: [this.filters.valueChanges, this.sorts.valueChanges]\n    });\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    const activeGroupId$ = this.route.paramMap.pipe(map(pm => pm.get('contents')), distinctUntilChanged());\n    this.listIsEmpty$ = this.items$.pipe(map(groups => groups.length === 0));\n    this.activeGroup$ = combineLatest(this.items$, activeGroupId$).pipe(map(([groups, activeGroupId]) => {\n      if (activeGroupId) {\n        return groups.find(g => g.id === activeGroupId);\n      }\n    }));\n    this.activeIndex$ = combineLatest(this.items$, activeGroupId$).pipe(map(([groups, activeGroupId]) => {\n      if (activeGroupId) {\n        return groups.findIndex(g => g.id === activeGroupId);\n      } else {\n        return -1;\n      }\n    }));\n    const membersResult$ = combineLatest(this.activeGroup$, this.fetchGroupMembers$, this.refreshActiveGroupMembers$).pipe(switchMap(([activeGroup, {\n      skip,\n      take,\n      filterTerm\n    }]) => {\n      if (activeGroup) {\n        return this.dataService.customer.getCustomerGroupWithCustomers(activeGroup.id, {\n          skip,\n          take,\n          filter: {\n            emailAddress: {\n              contains: filterTerm\n            }\n          }\n        }).mapStream(res => res.customerGroup?.customers);\n      } else {\n        return of(undefined);\n      }\n    }));\n    this.members$ = membersResult$.pipe(map(res => res?.items ?? []));\n    this.membersTotal$ = membersResult$.pipe(map(res => res?.totalItems ?? 0));\n  }\n  closeMembers() {\n    const params = {\n      ...this.route.snapshot.params\n    };\n    delete params.contents;\n    this.router.navigate(['./', params], {\n      relativeTo: this.route,\n      queryParamsHandling: 'preserve'\n    });\n  }\n  addToGroup(group) {\n    this.modalService.fromComponent(AddCustomerToGroupDialogComponent, {\n      locals: {\n        group,\n        route: this.route\n      },\n      size: 'md',\n      verticalAlign: 'top'\n    }).pipe(switchMap(customerIds => customerIds ? this.dataService.customer.addCustomersToGroup(group.id, customerIds).pipe(mapTo(customerIds)) : EMPTY)).subscribe({\n      next: result => {\n        this.notificationService.success(marker(`customer.add-customers-to-group-success`), {\n          customerCount: result.length,\n          groupName: group.name\n        });\n        this.refreshActiveGroupMembers$.next();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function CustomerGroupListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerGroupListComponent)(i0.ɵɵdirectiveInject(i1.DataService), i0.ɵɵdirectiveInject(i1.NotificationService), i0.ɵɵdirectiveInject(i1.ModalService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerGroupListComponent,\n      selectors: [[\"vdr-customer-group-list\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 12,\n      vars: 7,\n      consts: [[\"locationId\", \"customer-group-list\"], [\"class\", \"btn btn-primary\", 3, \"routerLink\", 4, \"vdrIfPermissions\"], [3, \"closeClicked\", \"rightPanelOpen\"], [\"vdrSplitViewLeft\", \"\"], [\"vdrSplitViewRight\", \"\", 3, \"splitViewTitle\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [\"shape\", \"plus\"], [1, \"mt-2\", 3, \"pageChange\", \"itemsPerPageChange\", \"visibleColumnsChange\", \"id\", \"items\", \"itemsPerPage\", \"totalItems\", \"currentPage\", \"filters\", \"activeIndex\"], [\"locationId\", \"customer-group-list\", 3, \"hostComponent\", \"selectionManager\"], [3, \"searchTermControl\", \"searchTermPlaceholder\"], [\"id\", \"id\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"created-at\", 3, \"heading\", \"hiddenByDefault\", \"sort\"], [\"id\", \"updated-at\", 3, \"heading\", \"hiddenByDefault\", \"sort\"], [\"id\", \"name\", 3, \"heading\", \"optional\", \"sort\"], [3, \"customField\", \"sorts\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"view-contents\", 3, \"heading\", \"optional\"], [1, \"button-ghost\", 3, \"routerLink\"], [\"shape\", \"arrow right\"], [3, \"customField\", \"sorts\"], [\"queryParamsHandling\", \"preserve\", 1, \"button-small\", \"bg-weight-150\", 3, \"routerLink\"], [\"shape\", \"file-group\"], [4, \"ngIf\"], [1, \"button-ghost\", \"ml-4\", 3, \"click\"], [\"locationId\", \"customer-group-members-list\", 3, \"fetchParamsChange\", \"members\", \"route\", \"totalItems\", \"activeGroup\"]],\n      template: function CustomerGroupListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-block\")(1, \"vdr-action-bar\");\n          i0.ɵɵelement(2, \"vdr-ab-left\");\n          i0.ɵɵelementStart(3, \"vdr-ab-right\");\n          i0.ɵɵelement(4, \"vdr-action-bar-items\", 0);\n          i0.ɵɵtemplate(5, CustomerGroupListComponent_a_5_Template, 4, 5, \"a\", 1);\n          i0.ɵɵelement(6, \"vdr-action-bar-dropdown-menu\", 0);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"vdr-split-view\", 2);\n          i0.ɵɵpipe(8, \"async\");\n          i0.ɵɵlistener(\"closeClicked\", function CustomerGroupListComponent_Template_vdr_split_view_closeClicked_7_listener() {\n            return ctx.closeMembers();\n          });\n          i0.ɵɵtemplate(9, CustomerGroupListComponent_ng_template_9_Template, 25, 47, \"ng-template\", 3)(10, CustomerGroupListComponent_ng_template_10_Template, 2, 3, \"ng-template\", 4);\n          i0.ɵɵpipe(11, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"vdrIfPermissions\", \"CreateCustomerGroup\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"rightPanelOpen\", i0.ɵɵpipeBind1(8, 3, ctx.activeGroup$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"splitViewTitle\", (tmp_2_0 = i0.ɵɵpipeBind1(11, 5, ctx.activeGroup$)) == null ? null : tmp_2_0.name);\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgForOf, i3$1.NgIf, i2.RouterLink, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ActionBarDropdownMenuComponent, i1.IfPermissionsDirective, i1.ActionBarItemsComponent, i1.BulkActionMenuComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i1.DataTable2SearchComponent, i1.DataTableCustomFieldColumnComponent, i1.SplitViewComponent, i1.SplitViewLeftDirective, i1.SplitViewRightDirective, i1.PageBlockComponent, CustomerGroupMemberListComponent, i3$1.AsyncPipe, i4.TranslatePipe, i1.LocaleDatePipe],\n      styles: [\"vdr-empty-placeholder[_ngcontent-%COMP%]{flex:1}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerGroupListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-group-list',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-block>\\n    <vdr-action-bar>\\n        <vdr-ab-left> </vdr-ab-left>\\n        <vdr-ab-right>\\n            <vdr-action-bar-items locationId=\\\"customer-group-list\\\"></vdr-action-bar-items>\\n            <a class=\\\"btn btn-primary\\\" *vdrIfPermissions=\\\"'CreateCustomerGroup'\\\" [routerLink]=\\\"['./', 'create']\\\">\\n                <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                {{ 'customer.create-new-customer-group' | translate }}\\n            </a>\\n            <vdr-action-bar-dropdown-menu locationId=\\\"customer-group-list\\\" />\\n        </vdr-ab-right>\\n    </vdr-action-bar>\\n</vdr-page-block>\\n<vdr-split-view [rightPanelOpen]=\\\"activeGroup$ | async\\\" (closeClicked)=\\\"closeMembers()\\\">\\n    <ng-template vdrSplitViewLeft>\\n        <vdr-data-table-2\\n            class=\\\"mt-2\\\"\\n            [id]=\\\"dataTableListId\\\"\\n            [items]=\\\"items$ | async\\\"\\n            [itemsPerPage]=\\\"itemsPerPage$ | async\\\"\\n            [totalItems]=\\\"totalItems$ | async\\\"\\n            [currentPage]=\\\"currentPage$ | async\\\"\\n            [filters]=\\\"filters\\\"\\n            [activeIndex]=\\\"activeIndex$ | async\\\"\\n            (pageChange)=\\\"setPageNumber($event)\\\"\\n            (itemsPerPageChange)=\\\"setItemsPerPage($event)\\\"\\n            (visibleColumnsChange)=\\\"setVisibleColumns($event)\\\"\\n        >\\n            <vdr-bulk-action-menu\\n                locationId=\\\"customer-group-list\\\"\\n                [hostComponent]=\\\"this\\\"\\n                [selectionManager]=\\\"selectionManager\\\"\\n            ></vdr-bulk-action-menu>\\n            <vdr-dt2-search\\n                [searchTermControl]=\\\"searchTermControl\\\"\\n                [searchTermPlaceholder]=\\\"'common.search-by-name' | translate\\\"\\n            ></vdr-dt2-search>\\n            <vdr-dt2-column [heading]=\\\"'common.id' | translate\\\" id=\\\"id\\\" [hiddenByDefault]=\\\"true\\\">\\n                <ng-template let-customerGroup=\\\"item\\\">\\n                    {{ customerGroup.id }}\\n                </ng-template>\\n            </vdr-dt2-column>\\n            <vdr-dt2-column\\n                [heading]=\\\"'common.created-at' | translate\\\" id=\\\"created-at\\\"\\n                [hiddenByDefault]=\\\"true\\\"\\n                [sort]=\\\"sorts.get('createdAt')\\\"\\n            >\\n                <ng-template let-customerGroup=\\\"item\\\">\\n                    {{ customerGroup.createdAt | localeDate : 'short' }}\\n                </ng-template>\\n            </vdr-dt2-column>\\n            <vdr-dt2-column\\n                [heading]=\\\"'common.updated-at' | translate\\\" id=\\\"updated-at\\\"\\n                [hiddenByDefault]=\\\"true\\\"\\n                [sort]=\\\"sorts.get('updatedAt')\\\"\\n            >\\n                <ng-template let-customerGroup=\\\"item\\\">\\n                    {{ customerGroup.updatedAt | localeDate : 'short' }}\\n                </ng-template>\\n            </vdr-dt2-column>\\n            <vdr-dt2-column\\n                [heading]=\\\"'common.name' | translate\\\" id=\\\"name\\\"\\n                [optional]=\\\"false\\\"\\n                [sort]=\\\"sorts.get('name')\\\"\\n            >\\n                <ng-template let-customerGroup=\\\"item\\\">\\n                    <a class=\\\"button-ghost\\\" [routerLink]=\\\"['./', customerGroup.id]\\\"\\n                        ><span>{{ customerGroup.name }}</span>\\n                        <clr-icon shape=\\\"arrow right\\\"></clr-icon>\\n                    </a>\\n                </ng-template>\\n            </vdr-dt2-column>\\n            <vdr-dt2-custom-field-column *ngFor=\\\"let field of customFields\\\" [customField]=\\\"field\\\" [sorts]=\\\"sorts\\\" />\\n            <vdr-dt2-column\\n                [heading]=\\\"'common.view-contents' | translate\\\" id=\\\"view-contents\\\"\\n                [optional]=\\\"false\\\"\\n            >\\n                <ng-template let-customerGroup=\\\"item\\\">\\n                    <a\\n                        class=\\\"button-small bg-weight-150\\\"\\n                        [routerLink]=\\\"['./', { contents: customerGroup.id }]\\\"\\n                        queryParamsHandling=\\\"preserve\\\"\\n                    >\\n                        <span>{{ 'customer.view-group-members' | translate }}</span>\\n                        <clr-icon shape=\\\"file-group\\\"></clr-icon>\\n                    </a>\\n                </ng-template>\\n            </vdr-dt2-column>\\n        </vdr-data-table-2>\\n    </ng-template>\\n    <ng-template vdrSplitViewRight [splitViewTitle]=\\\"(activeGroup$ | async)?.name\\\">\\n        <ng-container *ngIf=\\\"activeGroup$ | async as activeGroup\\\">\\n            <button class=\\\"button-ghost ml-4\\\" (click)=\\\"addToGroup(activeGroup)\\\">\\n                <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                <span>{{\\n                    'customer.add-customers-to-group' | translate : { groupName: activeGroup.name }\\n                }}</span>\\n            </button>\\n            <vdr-customer-group-member-list\\n                locationId=\\\"customer-group-members-list\\\"\\n                [members]=\\\"members$ | async\\\"\\n                [route]=\\\"route\\\"\\n                [totalItems]=\\\"membersTotal$ | async\\\"\\n                [activeGroup]=\\\"activeGroup$ | async\\\"\\n                (fetchParamsChange)=\\\"fetchGroupMembers$.next($event)\\\"\\n            />\\n        </ng-container>\\n    </ng-template>\\n</vdr-split-view>\\n\",\n      styles: [\"vdr-empty-placeholder{flex:1}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DataService\n  }, {\n    type: i1.NotificationService\n  }, {\n    type: i1.ModalService\n  }, {\n    type: i2.ActivatedRoute\n  }, {\n    type: i2.Router\n  }], null);\n})();\nconst removeCustomerGroupMembersBulkAction = {\n  location: 'customer-group-members-list',\n  label: marker('customer.remove-from-group'),\n  icon: 'trash',\n  iconClass: 'is-danger',\n  requiresPermission: Permission.UpdateCustomerGroup,\n  onClick: ({\n    injector,\n    selection,\n    hostComponent,\n    clearSelection\n  }) => {\n    const modalService = injector.get(ModalService);\n    const dataService = injector.get(DataService);\n    const notificationService = injector.get(NotificationService);\n    const group = hostComponent.activeGroup;\n    const customerIds = selection.map(s => s.id);\n    dataService.customer.removeCustomersFromGroup(group.id, customerIds).subscribe({\n      complete: () => {\n        notificationService.success(marker(`customer.remove-customers-from-group-success`), {\n          customerCount: customerIds.length,\n          groupName: group.name\n        });\n        clearSelection();\n        hostComponent.refresh();\n      }\n    });\n  }\n};\nconst deleteCustomersBulkAction = createBulkDeleteAction({\n  location: 'customer-list',\n  requiresPermission: userPermissions => userPermissions.includes(Permission.DeleteCustomer),\n  getItemName: item => item.firstName + ' ' + item.lastName,\n  bulkDelete: (dataService, ids) => dataService.customer.deleteCustomers(ids).pipe(map(res => res.deleteCustomers))\n});\nconst CUSTOMER_LIST_QUERY = gql`\n    query CustomerListQuery($options: CustomerListOptions) {\n        customers(options: $options) {\n            items {\n                ...CustomerListItem\n            }\n            totalItems\n        }\n    }\n\n    fragment CustomerListItem on Customer {\n        id\n        createdAt\n        updatedAt\n        title\n        firstName\n        lastName\n        emailAddress\n        user {\n            id\n            verified\n        }\n    }\n`;\nclass CustomerListComponent extends TypedBaseListComponent {\n  constructor() {\n    super();\n    this.dataTableListId = 'customer-list';\n    this.customFields = this.getCustomFieldConfig('Customer');\n    this.filters = this.createFilterCollection().addIdFilter().addDateFilters().addFilter({\n      name: 'firstName',\n      type: {\n        kind: 'text'\n      },\n      label: marker('customer.first-name'),\n      filterField: 'firstName'\n    }).addFilter({\n      name: 'lastName',\n      type: {\n        kind: 'text'\n      },\n      label: marker('customer.last-name'),\n      filterField: 'lastName'\n    }).addFilter({\n      name: 'emailAddress',\n      type: {\n        kind: 'text'\n      },\n      label: marker('customer.email-address'),\n      filterField: 'emailAddress'\n    }).addCustomFieldFilters(this.customFields).connectToRoute(this.route);\n    this.sorts = this.createSortCollection().defaultSort('createdAt', 'DESC').addSort({\n      name: 'createdAt'\n    }).addSort({\n      name: 'updatedAt'\n    }).addSort({\n      name: 'lastName'\n    }).addSort({\n      name: 'emailAddress'\n    }).addCustomFieldSorts(this.customFields).connectToRoute(this.route);\n    this.configure({\n      document: CustomerListQueryDocument,\n      getItems: data => data.customers,\n      setVariables: (skip, take) => ({\n        options: {\n          skip,\n          take,\n          filter: {\n            ...(this.searchTermControl.value ? {\n              emailAddress: {\n                contains: this.searchTermControl.value\n              },\n              lastName: {\n                contains: this.searchTermControl.value\n              },\n              postalCode: {\n                contains: this.searchTermControl.value\n              }\n            } : {}),\n            ...this.filters.createFilterInput()\n          },\n          filterOperator: this.searchTermControl.value ? LogicalOperator.OR : LogicalOperator.AND,\n          sort: this.sorts.createSortInput()\n        }\n      }),\n      refreshListOnChanges: [this.sorts.valueChanges, this.filters.valueChanges]\n    });\n  }\n  static {\n    this.ɵfac = function CustomerListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CustomerListComponent,\n      selectors: [[\"vdr-customer-list\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 34,\n      vars: 48,\n      consts: [[\"locationId\", \"customer-list\"], [\"class\", \"btn btn-primary\", 3, \"routerLink\", 4, \"vdrIfPermissions\"], [1, \"mt-2\", 3, \"pageChange\", \"itemsPerPageChange\", \"visibleColumnsChange\", \"id\", \"items\", \"itemsPerPage\", \"totalItems\", \"currentPage\", \"filters\"], [\"locationId\", \"customer-list\", 3, \"hostComponent\", \"selectionManager\"], [3, \"searchTermControl\", \"searchTermPlaceholder\"], [\"id\", \"id\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"created-at\", 3, \"heading\", \"hiddenByDefault\", \"sort\"], [\"id\", \"updated-at\", 3, \"heading\", \"hiddenByDefault\", \"sort\"], [\"id\", \"name\", 3, \"heading\", \"optional\", \"sort\"], [\"id\", \"status\", 3, \"heading\"], [\"id\", \"email-address\", 3, \"heading\", \"sort\"], [3, \"customField\", \"sorts\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [\"shape\", \"plus\"], [1, \"button-ghost\", 3, \"routerLink\"], [\"shape\", \"arrow right\"], [3, \"customer\"], [3, \"customField\", \"sorts\"]],\n      template: function CustomerListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-block\")(1, \"vdr-action-bar\");\n          i0.ɵɵelement(2, \"vdr-ab-left\");\n          i0.ɵɵelementStart(3, \"vdr-ab-right\");\n          i0.ɵɵelement(4, \"vdr-action-bar-items\", 0);\n          i0.ɵɵtemplate(5, CustomerListComponent_a_5_Template, 4, 5, \"a\", 1);\n          i0.ɵɵelement(6, \"vdr-action-bar-dropdown-menu\", 0);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"vdr-data-table-2\", 2);\n          i0.ɵɵpipe(8, \"async\");\n          i0.ɵɵpipe(9, \"async\");\n          i0.ɵɵpipe(10, \"async\");\n          i0.ɵɵpipe(11, \"async\");\n          i0.ɵɵlistener(\"pageChange\", function CustomerListComponent_Template_vdr_data_table_2_pageChange_7_listener($event) {\n            return ctx.setPageNumber($event);\n          })(\"itemsPerPageChange\", function CustomerListComponent_Template_vdr_data_table_2_itemsPerPageChange_7_listener($event) {\n            return ctx.setItemsPerPage($event);\n          })(\"visibleColumnsChange\", function CustomerListComponent_Template_vdr_data_table_2_visibleColumnsChange_7_listener($event) {\n            return ctx.setVisibleColumns($event);\n          });\n          i0.ɵɵelement(12, \"vdr-bulk-action-menu\", 3)(13, \"vdr-dt2-search\", 4);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵelementStart(15, \"vdr-dt2-column\", 5);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵtemplate(17, CustomerListComponent_ng_template_17_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"vdr-dt2-column\", 6);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵtemplate(20, CustomerListComponent_ng_template_20_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"vdr-dt2-column\", 7);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵtemplate(23, CustomerListComponent_ng_template_23_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"vdr-dt2-column\", 8);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵtemplate(26, CustomerListComponent_ng_template_26_Template, 4, 6, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"vdr-dt2-column\", 9);\n          i0.ɵɵpipe(28, \"translate\");\n          i0.ɵɵtemplate(29, CustomerListComponent_ng_template_29_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"vdr-dt2-column\", 10);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵtemplate(32, CustomerListComponent_ng_template_32_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, CustomerListComponent_vdr_dt2_custom_field_column_33_Template, 1, 2, \"vdr-dt2-custom-field-column\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"vdrIfPermissions\", \"CreateCustomer\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.dataTableListId)(\"items\", i0.ɵɵpipeBind1(8, 26, ctx.items$))(\"itemsPerPage\", i0.ɵɵpipeBind1(9, 28, ctx.itemsPerPage$))(\"totalItems\", i0.ɵɵpipeBind1(10, 30, ctx.totalItems$))(\"currentPage\", i0.ɵɵpipeBind1(11, 32, ctx.currentPage$))(\"filters\", ctx.filters);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"hostComponent\", ctx)(\"selectionManager\", ctx.selectionManager);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"searchTermControl\", ctx.searchTermControl)(\"searchTermPlaceholder\", i0.ɵɵpipeBind1(14, 34, \"customer.search-customers-by-email-last-name-postal-code\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(16, 36, \"common.id\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(19, 38, \"common.created-at\"))(\"hiddenByDefault\", true)(\"sort\", ctx.sorts.get(\"createdAt\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(22, 40, \"common.updated-at\"))(\"hiddenByDefault\", true)(\"sort\", ctx.sorts.get(\"updatedAt\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(25, 42, \"customer.name\"))(\"optional\", false)(\"sort\", ctx.sorts.get(\"lastName\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(28, 44, \"common.status\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(31, 46, \"customer.email-address\"))(\"sort\", ctx.sorts.get(\"emailAddress\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.customFields);\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgForOf, i2.RouterLink, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ActionBarDropdownMenuComponent, i1.IfPermissionsDirective, i1.ActionBarItemsComponent, i1.BulkActionMenuComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i1.DataTable2SearchComponent, i1.DataTableCustomFieldColumnComponent, i1.PageBlockComponent, CustomerStatusLabelComponent, i3$1.AsyncPipe, i4.TranslatePipe, i1.LocaleDatePipe],\n      styles: [\".search-input[_ngcontent-%COMP%]{margin-top:6px;min-width:300px}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-customer-list',\n      standalone: false,\n      template: \"<vdr-page-block>\\n    <vdr-action-bar>\\n        <vdr-ab-left> </vdr-ab-left>\\n        <vdr-ab-right>\\n            <vdr-action-bar-items locationId=\\\"customer-list\\\"></vdr-action-bar-items>\\n            <a class=\\\"btn btn-primary\\\" [routerLink]=\\\"['./create']\\\" *vdrIfPermissions=\\\"'CreateCustomer'\\\">\\n                <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                {{ 'customer.create-new-customer' | translate }}\\n            </a>\\n            <vdr-action-bar-dropdown-menu locationId=\\\"customer-list\\\" />\\n        </vdr-ab-right>\\n    </vdr-action-bar>\\n</vdr-page-block>\\n\\n<vdr-data-table-2\\n    class=\\\"mt-2\\\"\\n    [id]=\\\"dataTableListId\\\"\\n    [items]=\\\"items$ | async\\\"\\n    [itemsPerPage]=\\\"itemsPerPage$ | async\\\"\\n    [totalItems]=\\\"totalItems$ | async\\\"\\n    [currentPage]=\\\"currentPage$ | async\\\"\\n    [filters]=\\\"filters\\\"\\n    (pageChange)=\\\"setPageNumber($event)\\\"\\n    (itemsPerPageChange)=\\\"setItemsPerPage($event)\\\"\\n    (visibleColumnsChange)=\\\"setVisibleColumns($event)\\\"\\n>\\n    <vdr-bulk-action-menu\\n        locationId=\\\"customer-list\\\"\\n        [hostComponent]=\\\"this\\\"\\n        [selectionManager]=\\\"selectionManager\\\"\\n    ></vdr-bulk-action-menu>\\n    <vdr-dt2-search\\n        [searchTermControl]=\\\"searchTermControl\\\"\\n        [searchTermPlaceholder]=\\\"'customer.search-customers-by-email-last-name-postal-code' | translate\\\"\\n    ></vdr-dt2-search>\\n    <vdr-dt2-column [heading]=\\\"'common.id' | translate\\\" id=\\\"id\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            {{ customer.id }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'common.created-at' | translate\\\" id=\\\"created-at\\\"\\n        [hiddenByDefault]=\\\"true\\\"\\n        [sort]=\\\"sorts.get('createdAt')\\\"\\n    >\\n        <ng-template let-customer=\\\"item\\\">\\n            {{ customer.createdAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'common.updated-at' | translate\\\" id=\\\"updated-at\\\"\\n        [hiddenByDefault]=\\\"true\\\"\\n        [sort]=\\\"sorts.get('updatedAt')\\\"\\n    >\\n        <ng-template let-customer=\\\"item\\\">\\n            {{ customer.updatedAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'customer.name' | translate\\\" id=\\\"name\\\" [optional]=\\\"false\\\" [sort]=\\\"sorts.get('lastName')\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            <a class=\\\"button-ghost\\\" [routerLink]=\\\"['./', customer.id]\\\"\\n                ><span> {{ customer.title }} {{ customer.firstName }} {{ customer.lastName }} </span>\\n                <clr-icon shape=\\\"arrow right\\\"></clr-icon>\\n            </a>\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'common.status' | translate\\\" id=\\\"status\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            <vdr-customer-status-label [customer]=\\\"customer\\\" />\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'customer.email-address' | translate\\\" id=\\\"email-address\\\" [sort]=\\\"sorts.get('emailAddress')\\\">\\n        <ng-template let-customer=\\\"item\\\">\\n            {{ customer.emailAddress }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-custom-field-column *ngFor=\\\"let field of customFields\\\" [customField]=\\\"field\\\" [sorts]=\\\"sorts\\\" />\\n</vdr-data-table-2>\\n\",\n      styles: [\".search-input{margin-top:6px;min-width:300px}\\n\"]\n    }]\n  }], () => [], null);\n})();\nconst createRoutes = pageService => [{\n  path: 'customers',\n  component: PageComponent,\n  data: {\n    locationId: 'customer-list',\n    breadcrumb: marker('breadcrumb.customers')\n  },\n  children: pageService.getPageTabRoutes('customer-list')\n}, {\n  path: 'customers/:id',\n  component: PageComponent,\n  data: {\n    locationId: 'customer-detail',\n    breadcrumb: {\n      label: marker('breadcrumb.customers'),\n      link: ['../', 'customers']\n    }\n  },\n  children: pageService.getPageTabRoutes('customer-detail')\n}, {\n  path: 'groups',\n  component: PageComponent,\n  data: {\n    locationId: 'customer-group-list',\n    breadcrumb: marker('breadcrumb.customer-groups')\n  },\n  children: pageService.getPageTabRoutes('customer-group-list')\n}, {\n  path: 'groups/:id',\n  component: PageComponent,\n  data: {\n    locationId: 'customer-group-detail',\n    breadcrumb: {\n      label: marker('breadcrumb.customer-groups'),\n      link: ['../', 'groups']\n    }\n  },\n  children: pageService.getPageTabRoutes('customer-group-detail')\n}];\nfunction customerBreadcrumb(data, params) {\n  return detailBreadcrumb({\n    entity: data.entity,\n    id: params.id,\n    breadcrumbKey: 'breadcrumb.customers',\n    getName: customer => `${customer.firstName} ${customer.lastName}`,\n    route: 'customers'\n  });\n}\nclass CustomerModule {\n  static {\n    this.hasRegisteredTabsAndBulkActions = false;\n  }\n  constructor(bulkActionRegistryService, pageService) {\n    if (CustomerModule.hasRegisteredTabsAndBulkActions) {\n      return;\n    }\n    bulkActionRegistryService.registerBulkAction(deleteCustomersBulkAction);\n    bulkActionRegistryService.registerBulkAction(deleteCustomerGroupsBulkAction);\n    bulkActionRegistryService.registerBulkAction(removeCustomerGroupMembersBulkAction);\n    pageService.registerPageTab({\n      priority: 0,\n      location: 'customer-list',\n      tab: marker('customer.customers'),\n      route: '',\n      component: CustomerListComponent\n    });\n    pageService.registerPageTab({\n      priority: 0,\n      location: 'customer-detail',\n      tab: marker('customer.customer'),\n      route: '',\n      component: detailComponentWithResolver({\n        component: CustomerDetailComponent,\n        query: CustomerDetailQueryDocument,\n        entityKey: 'customer',\n        variables: {\n          orderListOptions: {\n            sort: {\n              orderPlacedAt: SortOrder.DESC\n            }\n          }\n        },\n        getBreadcrumbs: entity => [{\n          label: entity ? `${entity?.firstName} ${entity?.lastName}` : marker('customer.create-new-customer'),\n          link: [entity?.id]\n        }]\n      })\n    });\n    pageService.registerPageTab({\n      priority: 0,\n      location: 'customer-group-list',\n      tab: marker('customer.customer-groups'),\n      route: '',\n      component: CustomerGroupListComponent\n    });\n    pageService.registerPageTab({\n      priority: 0,\n      location: 'customer-group-detail',\n      tab: marker('customer.customer-group'),\n      route: '',\n      component: detailComponentWithResolver({\n        component: CustomerGroupDetailComponent,\n        query: GetCustomerGroupDetailDocument,\n        entityKey: 'customerGroup',\n        getBreadcrumbs: entity => [{\n          label: entity ? entity.name : marker('customer.create-new-customer-group'),\n          link: [entity?.id]\n        }]\n      })\n    });\n    CustomerModule.hasRegisteredTabsAndBulkActions = true;\n  }\n  static {\n    this.ɵfac = function CustomerModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerModule)(i0.ɵɵinject(i1.BulkActionRegistryService), i0.ɵɵinject(i1.PageService));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CustomerModule,\n      declarations: [CustomerListComponent, CustomerDetailComponent, CustomerStatusLabelComponent, AddressCardComponent, CustomerGroupListComponent, CustomerGroupDetailDialogComponent, AddCustomerToGroupDialogComponent, CustomerGroupMemberListComponent, SelectCustomerGroupDialogComponent, CustomerHistoryComponent, AddressDetailDialogComponent, CustomerHistoryEntryHostComponent, CustomerGroupDetailComponent],\n      imports: [SharedModule, i2.RouterModule],\n      exports: [AddressCardComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: ROUTES,\n        useFactory: pageService => createRoutes(pageService),\n        multi: true,\n        deps: [PageService]\n      }],\n      imports: [SharedModule, RouterModule.forChild([])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CustomerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [SharedModule, RouterModule.forChild([])],\n      providers: [{\n        provide: ROUTES,\n        useFactory: pageService => createRoutes(pageService),\n        multi: true,\n        deps: [PageService]\n      }],\n      declarations: [CustomerListComponent, CustomerDetailComponent, CustomerStatusLabelComponent, AddressCardComponent, CustomerGroupListComponent, CustomerGroupDetailDialogComponent, AddCustomerToGroupDialogComponent, CustomerGroupMemberListComponent, SelectCustomerGroupDialogComponent, CustomerHistoryComponent, AddressDetailDialogComponent, CustomerHistoryEntryHostComponent, CustomerGroupDetailComponent],\n      exports: [AddressCardComponent]\n    }]\n  }], () => [{\n    type: i1.BulkActionRegistryService\n  }, {\n    type: i1.PageService\n  }], null);\n})();\n\n// This file was generated by the build-public-api.ts script\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AddCustomerToGroupDialogComponent, AddressCardComponent, AddressDetailDialogComponent, CUSTOMER_DETAIL_QUERY, CUSTOMER_GROUP_DETAIL_QUERY, CUSTOMER_LIST_QUERY, CustomerDetailComponent, CustomerGroupDetailComponent, CustomerGroupDetailDialogComponent, CustomerGroupListComponent, CustomerGroupMemberListComponent, CustomerHistoryComponent, CustomerHistoryEntryHostComponent, CustomerListComponent, CustomerModule, CustomerStatusLabelComponent, GET_CUSTOMER_GROUP_LIST, SelectCustomerGroupDialogComponent, createRoutes, customerBreadcrumb, deleteCustomerGroupsBulkAction, deleteCustomersBulkAction, removeCustomerGroupMembersBulkAction };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,0BAAmC;AAGnC,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,mBAAmB,GAAG,GAAG;AAAA,EAC3E;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,qBAAqB,GAAG,GAAG;AAAA,EAC7E;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,iEAAiE,GAAG,GAAG,gBAAgB,CAAC;AACtM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS,QAAQ,OAAO,OAAO,OAAO,SAAS,KAAK,QAAQ;AACzF,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,EAAE,OAAO,SAAS,QAAQ,OAAO,OAAO,OAAO,SAAS,KAAK,SAAS;AAAA,EAC9F;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC7D;AACF;AACA,IAAM,MAAM,QAAM,CAAC,uBAAuB,EAAE;AAC5C,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,IAAG,mBAAmB,KAAK,iBAAiB,IAAI,GAAG;AAAA,EACrD;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,YAAY,WAAW,OAAO,GAAG,GAAG;AAAA,EACtF;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,YAAY,WAAW,OAAO,GAAG,GAAG;AAAA,EACtF;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC,EAAE,GAAG,MAAM;AACtC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,YAAY,EAAE,CAAC;AACtE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,YAAY,OAAO,KAAK,YAAY,WAAW,KAAK,YAAY,UAAU,GAAG;AAAA,EAC1G;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,6BAA6B,EAAE;AAAA,EACjD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,WAAW,YAAY,WAAW;AAAA,EACvC;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,mBAAmB,KAAK,YAAY,cAAc,GAAG;AAAA,EAC1D;AACF;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AACT;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,6CAAgD,gBAAgB,GAAG,KAAK,OAAO,MAAM,IAAI,CAAC,GAAG,IAAI;AAAA,EACnJ;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,mFAAmF;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,mFAAmF;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,IAAI,CAAC;AAAA,IACpC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,eAAe,CAAC;AAC1D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAC,OAAO,oBAAoB,MAAM;AAC5D,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,8CAAiD,gBAAgB,GAAG,KAAK,OAAO,oBAAoB,MAAM,CAAC,GAAG,GAAG;AAAA,EACnK;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAiB,IAAI;AAC3B,IAAG,UAAU;AACb,IAAG,mBAAmB,IAAI,gBAAgB,GAAG;AAAA,EAC/C;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAiB,IAAI;AAC3B,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,gBAAgB,EAAE;AAAA,EAC/C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,4DAA4D,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC9K;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,QAAI;AACJ,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,UAAU,OAAO,YAAY,IAAI,aAAa,MAAM,OAAO,OAAO,QAAQ,KAAK;AACtG,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,UAAU,OAAO,YAAY,IAAI,aAAa,MAAM,OAAO,OAAO,QAAQ,KAAK;AAAA,EACxG;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,8EAA8E;AAC5G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,8EAA8E;AAC5G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,eAAe,CAAC;AAC1D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAC,OAAO,YAAY,SAAS,CAAC,OAAO,YAAY,OAAO;AAClF,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,UAAU;AACb,IAAG,mBAAmB,IAAI,WAAW,aAAa,GAAG;AAAA,EACvD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,mCAAmC,GAAG,GAAG;AAAA,EAC3F;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,kCAAkC,GAAG,GAAG;AAAA,EAC1F;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,8EAA8E;AAC5G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,CAAC;AAAA,IAC5C,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,mBAAmB,EAAE,IAAI,UAAU,EAAE;AAC1D,IAAG,WAAW,SAAS,SAAS,+EAA+E;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,4BAA4B,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,WAAW,SAAS,SAAS,+EAA+E;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,2BAA2B,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,WAAW,SAAS,SAAS,+EAA+E;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,UAAU,IAAI,YAAY,EAAE;AAC/B,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,aAAa,GAAG,GAAG;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,aAAa,GAAG,GAAG;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,OAAO,iBAAiB;AAClD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,0CAA0C,GAAG,GAAG;AAClG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,OAAO,gBAAgB;AACjD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,yCAAyC,GAAG,GAAG;AACjG,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,eAAe,GAAG,GAAG;AAAA,EACzE;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,IAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,QAAQ,CAAC;AAC5E,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,YAAY,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,YAAY,CAAC;AAC5J,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,UAAU,IAAI,yBAAyB,CAAC;AAC3C,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE;AAC9C,IAAG,UAAU,IAAI,mBAAmB,EAAE;AACtC,IAAG,WAAW,IAAI,qDAAqD,IAAI,IAAI,gBAAgB,EAAE;AACjG,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,WAAW,WAAW;AAC5C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,WAAW,aAAa,GAAG;AACtD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,iBAAiB;AAC9C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,gBAAgB;AAC7C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,UAAU;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,UAAU,UAAU;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,QAAQ;AAAA,EACvC;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,gCAAgC,GAAG,IAAI;AAAA,EACzF;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,SAAS,SAAS,kFAAkF;AAChH,YAAM,SAAY,cAAc,GAAG;AACnC,YAAM,UAAU,OAAO;AACvB,YAAM,WAAW,OAAO;AACxB,aAAU,YAAY,SAAS,OAAO,CAAC;AAAA,IACzC,CAAC;AACD,IAAG,OAAO,GAAG,KAAQ;AACrB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,YAAY,CAAC;AAClC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,aAAa,QAAQ,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,kBAAkB,QAAQ,IAAI;AAAA,EACnC;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,CAAC;AAClC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,aAAa,QAAQ,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,kBAAkB,QAAQ,IAAI;AAAA,EACnC;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,oFAAoF;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,oFAAoF;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,IAAI,CAAC;AAAA,IACpC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,eAAe,CAAC;AAC1D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAC,OAAO,iBAAiB,MAAM;AACzD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,8CAAiD,gBAAgB,GAAG,KAAK,OAAO,iBAAiB,MAAM,CAAC,GAAG,GAAG;AAAA,EAChK;AACF;AACA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,SAAO;AAAA,EACjB,UAAU;AACZ;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,sBAAsB,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,YAAY,CAAC;AAC3E,IAAG,iBAAiB,iBAAiB,SAAS,yFAAyF,QAAQ;AAC7I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,MAAG,mBAAmB,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO;AAC7D,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,iFAAiF;AAC/G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,CAAC;AAAA,IAClD,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,IAAI,EAAE,WAAW,IAAI;AAC/C,IAAG,UAAU,CAAC;AACd,IAAG,iBAAiB,WAAW,OAAO,IAAI;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,CAAC,OAAO,IAAI;AACtC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,gBAAgB,GAAG,GAAG;AAAA,EACxE;AACF;AACA,SAAS,mFAAmF,IAAI,KAAK;AACnG,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,mCAAmC,EAAE;AAC1D,IAAG,WAAW,eAAe,SAAS,4IAA4I;AAChL,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,CAAC,OAAO,QAAQ;AAAA,IAC1D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,SAAS,QAAQ,EAAE,YAAY,OAAO,QAAQ;AAAA,EAC3F;AACF;AACA,SAAS,6FAA6F,IAAI,KAAK;AAC7G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,6CAA6C,GAAG,GAAG;AAAA,EACrG;AACF;AACA,SAAS,4FAA4F,IAAI,KAAK;AAC5G,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,iDAAoD,gBAAgB,GAAG,KAAK,SAAS,KAAK,QAAQ,CAAC,GAAG,GAAG;AAAA,EAC3J;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,8FAA8F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,6FAA6F,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAChS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,sCAAsC,GAAG,GAAG;AAC5F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,SAAS,KAAK,aAAa,QAAQ,EAAE,YAAY,gBAAgB;AAAA,EACzF;AACF;AACA,SAAS,6FAA6F,IAAI,KAAK;AAC7G,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,6CAA6C,GAAG,GAAG;AAAA,EACrG;AACF;AACA,SAAS,4FAA4F,IAAI,KAAK;AAC5G,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,iDAAoD,gBAAgB,GAAG,KAAK,SAAS,KAAK,QAAQ,CAAC,GAAG,GAAG;AAAA,EAC3J;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,8FAA8F,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,6FAA6F,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAChS,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,oCAAoC,GAAG,GAAG;AAC1F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,SAAS,KAAK,aAAa,QAAQ,EAAE,YAAY,gBAAgB;AAAA,EACzF;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,0BAA0B;AAC/C,IAAG,UAAU,GAAG,mBAAmB,EAAE;AACrC,IAAG,aAAa,EAAE;AAClB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,0CAA0C,GAAG,GAAG;AAChG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,SAAS,KAAK,KAAK;AAAA,EAC5C;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,4CAA+C,gBAAgB,GAAG,KAAK,SAAS,KAAK,SAAS,CAAC,GAAG,GAAG;AAAA,EACvJ;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,gDAAmD,gBAAgB,GAAG,KAAK,SAAS,KAAK,SAAS,CAAC,GAAG,GAAG;AAAA,EAC3J;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC5C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAClB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,2CAA2C,GAAG,GAAG;AACjG,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,SAAS,KAAK,OAAO;AAAA,EAC5C;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC5C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,0BAA0B;AAC/C,IAAG,UAAU,GAAG,mBAAmB,EAAE;AACrC,IAAG,aAAa,EAAE;AAClB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,2CAA2C,GAAG,GAAG;AACjG,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,SAAS,KAAK,OAAO;AAC1C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,SAAS,KAAK,KAAK;AAAA,EAC5C;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,2CAA2C,GAAG,GAAG;AACjG,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,SAAS,KAAK,OAAO;AAAA,EAC5C;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,4CAA4C,GAAG,GAAG;AAAA,EACpG;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,oDAAoD,GAAG,GAAG;AAAA,EAC5G;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,mDAAmD,GAAG,GAAG;AAAA,EAC3G;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,0BAA0B,EAAE,GAAG,oBAAoB,EAAE;AAC1E,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,oBAAoB,EAAE;AAC3C,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,OAAO,EAAE;AACZ,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,kDAAkD,GAAG,GAAG;AACxG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,4BAA4B,CAAC;AACzE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,IAAI,SAAS,KAAK,iBAAiB,GAAG;AAC5D,IAAG,UAAU;AACb,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,4BAA4B,CAAC;AACzE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,IAAI,SAAS,KAAK,iBAAiB,GAAG;AAAA,EAC9D;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,0BAA0B,EAAE,GAAG,oBAAoB,EAAE;AAC1E,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,oBAAoB,EAAE;AAC3C,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,OAAO,EAAE;AACZ,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,iDAAiD,GAAG,GAAG;AACvG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,4BAA4B,CAAC;AACzE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,IAAI,SAAS,KAAK,iBAAiB,GAAG;AAC5D,IAAG,UAAU;AACb,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,4BAA4B,CAAC;AACzE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,IAAI,SAAS,KAAK,iBAAiB,GAAG;AAAA,EAC9D;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC5C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,qBAAqB,EAAE,EAAE,GAAG,UAAU,EAAE;AAC7D,IAAG,OAAO,IAAI,eAAe;AAC7B,IAAG,WAAW,SAAS,SAAS,yGAAyG;AACvI,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,CAAC,EAAE;AACrC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,UAAU,IAAI,YAAY,EAAE;AAC/B,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,eAAe;AAC7B,IAAG,WAAW,SAAS,SAAS,0GAA0G;AACxI,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,CAAC,EAAE;AACrC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,UAAU,IAAI,YAAY,EAAE;AAC/B,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa,EAAE,EAAE,EAAE;AACtB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,SAAS,KAAK,MAAM,GAAG;AAClD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAI,YAAY,IAAI,GAAG,gBAAgB,CAAC;AAClE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,IAAI,GAAG,aAAa,GAAG,GAAG;AACpE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAI,YAAY,IAAI,GAAG,gBAAgB,CAAC;AAClE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,eAAe,GAAG,GAAG;AAAA,EACzE;AACF;AACA,SAAS,0GAA0G,IAAI,KAAK;AAC1H,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,0BAA0B;AAC/C,IAAG,UAAU,GAAG,mBAAmB,EAAE;AACrC,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,SAAS,IAAI;AAAA,EACtC;AACF;AACA,SAAS,+EAA+E,IAAI,KAAK;AAC/F,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,2GAA2G,GAAG,GAAG,4BAA4B,EAAE;AAChK,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,SAAS,IAAI,GAAG,GAAG;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,SAAS,IAAI;AAAA,EACrC;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,sBAAsB,EAAE;AAC7C,IAAG,wBAAwB,GAAG,EAAE;AAChC,IAAG,WAAW,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,gBAAgB,EAAE,EAAE,IAAI,gFAAgF,GAAG,GAAG,gBAAgB,EAAE,EAAE,IAAI,gFAAgF,GAAG,GAAG,gBAAgB,EAAE,EAAE,IAAI,gFAAgF,GAAG,GAAG,gBAAgB,EAAE,EAAE,IAAI,gFAAgF,IAAI,IAAI,gBAAgB,EAAE,EAAE,IAAI,gFAAgF,IAAI,IAAI,gBAAgB,EAAE,EAAE,IAAI,gFAAgF,IAAI,IAAI,gBAAgB,EAAE,EAAE,IAAI,gFAAgF,GAAG,GAAG,gBAAgB,EAAE;AACpnD,IAAG,sBAAsB;AACzB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,eAAe,OAAO,eAAe,QAAQ,CAAC,EAAE,aAAa,OAAO,gBAAgB,QAAQ,CAAC,EAAE,aAAa,SAAS,SAAS,EAAE,QAAQ,OAAO,QAAQ,QAAQ,CAAC,EAAE,YAAY,OAAO,WAAW,QAAQ,CAAC;AACvN,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,SAAS,IAAI;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,mBAAmB;AAC7D,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,iBAAiB;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,uBAAuB;AACjE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,uBAAuB;AACjE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,2BAA2B;AACrE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,wBAAwB;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,wBAAwB;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,wBAAwB;AAClE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,yBAAyB;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,iCAAiC;AAC3E,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,gCAAgC;AAC1E,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,+BAA+B;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,8BAA8B;AACxE,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,KAAK,aAAa;AAAA,EACzD;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,oFAAoF,GAAG,GAAG,mCAAmC,EAAE,EAAE,GAAG,gEAAgE,IAAI,IAAI,eAAe,MAAM,GAAM,sBAAsB;AAC9Q,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,uBAA0B,YAAY,CAAC;AAC7C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,mBAAmB,SAAS,IAAI,CAAC,EAAE,YAAY,oBAAoB;AAAA,EAClG;AACF;AACA,IAAM,MAAM,MAAM,CAAC,gBAAgB;AACnC,IAAM,MAAM,QAAM,CAAC,WAAW,EAAE;AAChC,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,oEAAoE;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,EAAE,OAAO,0BAA0B,OAAO,WAAW,SAAS,OAAO,WAAW,MAAM;AAChH,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,kFAAkF;AAChH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,EAAE,OAAO,0BAA0B,OAAO,WAAW,SAAS,OAAO,WAAW,MAAM;AAChH,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,UAAU,EAAE;AAAA,EAC9F;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,oBAAoB,EAAE;AAC3C,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,SAAS;AACtB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,qBAAqB,CAAC;AAClE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,YAAY;AACtC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,YAAY,CAAC;AAAA,EACzD;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,UAAU,GAAG,6BAA6B,EAAE;AAC/C,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,oBAAoB,EAAE;AAC9G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,WAAW;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,YAAY,QAAQ,OAAO,OAAO,YAAY,KAAK,SAAS;AAAA,EACpF;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,WAAW,aAAa,SAAS,8FAA8F;AAChI,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,aAAa,SAAS,EAAE;AACtC,IAAG,UAAU;AACb,IAAG,kBAAkB,SAAS,IAAI;AAAA,EACpC;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,YAAY,EAAE;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,SAAS;AAAA,EACpC;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,qCAAqC,GAAG,GAAG;AAAA,EAC7F;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,gFAAgF;AAC9G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,gCAAgC,GAAG,GAAG;AAAA,EACxF;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,4DAA4D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC5M,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,UAAU,EAAE;AAC1F,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,eAAkB,YAAY,CAAC;AACrC,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,0BAA0B,CAAC;AACvE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,UAAU,MAAM,EAAE,YAAY,YAAY;AAChE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,qBAAqB;AAAA,EACzD;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,wBAAwB,EAAE;AAAA,EAC5C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,IAAG,WAAW,UAAU,UAAU;AAAA,EACpC;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,kBAAkB,EAAE;AACzC,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,mBAAmB,CAAC;AAAA,EAClE;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,UAAU,GAAG,4BAA4B,EAAE;AAC9C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,sBAAsB,CAAC;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,gBAAgB,OAAO,YAAY,EAAE,yBAAyB,OAAO,WAAW,IAAI,uBAAuB,CAAC;AAAA,EAC5H;AACF;AACA,SAAS,oEAAoE,IAAI,KAAK;AACpF,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,oBAAoB,EAAE;AAC3C,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,WAAW,uBAAuB,SAAS,oHAAoH,QAAQ;AACxK,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,2BAA2B,MAAM,CAAC;AAAA,IACjE,CAAC,EAAE,wBAAwB,SAAS,qHAAqH,QAAQ;AAC/J,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,4BAA4B,MAAM,CAAC;AAAA,IAClE,CAAC,EAAE,iBAAiB,SAAS,8GAA8G,QAAQ;AACjJ,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,kBAAkB,IAAI;AAC5B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,aAAa,OAAO,qBAAqB,IAAI,gBAAgB,MAAM,EAAE,CAAC;AACrF,IAAG,WAAW,sBAAyB,YAAY,GAAG,GAAG,OAAO,mBAAmB,CAAC,EAAE,oBAAoB,OAAO,4BAA4B,gBAAgB,MAAM,EAAE,EAAE,qBAAqB,OAAO,6BAA6B,gBAAgB,MAAM,EAAE,EAAE,eAAe,eAAe,EAAE,gBAAgB,OAAO,mBAAmB,EAAE,YAAe,YAAY,GAAG,IAAO,gBAAgB,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,qBAAqB,IAAI,gBAAgB,MAAM,EAAE,CAAC;AAAA,EACpc;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,oFAAoF;AAClH,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,6BAA6B,GAAG,GAAG;AAAA,EACrF;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,mBAAmB,KAAK,UAAU,IAAI,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,UAAU,WAAW,OAAO,GAAG,GAAG;AAAA,EACpF;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,EAAE,GAAG,MAAM;AACvC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,UAAU,EAAE,CAAC;AACpE,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,UAAU,IAAI;AAAA,EACrC;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,UAAU;AACb,IAAG,kBAAkB,UAAU,IAAI;AAAA,EACrC;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,EAAE;AAAA,EAC7C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,SAAS,UAAU,KAAK;AAAA,EACxC;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,gBAAgB;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,UAAU,cAAc,UAAU,YAAY,GAAG,GAAG;AAAA,EACtG;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,SAAS;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,UAAU,SAAS,GAAG,GAAG;AAAA,EAC3E;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,UAAU,eAAe,OAAO,GAAG,GAAG;AAAA,EACxF;AACF;AACA,SAAS,gFAAgF,IAAI,KAAK;AAChG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,+BAA+B,EAAE;AAAA,EACnD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,kBAAkB,IAAI;AAC5B,IAAG,WAAW,eAAe,eAAe;AAAA,EAC9C;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,qEAAqE,GAAG,IAAI,oBAAoB,EAAE;AACnH,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,UAAU,EAAE;AAC9F,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,oBAAoB,EAAE;AAC3C,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,sBAAsB,SAAS,gGAAgG,QAAQ;AACnJ,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,qBAAqB,MAAM,CAAC;AAAA,IAC3D,CAAC,EAAE,cAAc,SAAS,wFAAwF,QAAQ;AACxH,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,iEAAiE,GAAG,GAAG,aAAa;AACtG,IAAG,aAAa;AAChB,IAAG,WAAW,IAAI,iFAAiF,GAAG,GAAG,+BAA+B,EAAE;AAC1I,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,IAAI,YAAY,EAAE;AACpC,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,eAAe,IAAI,wBAAwB,EAAE;AAChD,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,WAAW,WAAW,SAAS,0FAA0F,QAAQ;AAClI,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC,EAAE,cAAc,SAAS,6FAA6F,QAAQ;AAC7H,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC,EAAE,cAAc,SAAS,6FAA6F,QAAQ;AAC7H,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,aAAa,EAAE;AAClB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,SAAY,YAAY,GAAG,IAAI,oBAAoB,CAAC;AAClE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,uBAAuB,CAAC;AACxD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,gBAAgB;AAClD,IAAG,UAAU;AACb,IAAG,WAAW,SAAY,YAAY,GAAG,IAAI,iBAAiB,CAAC,EAAE,YAAY,KAAK;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAY,YAAY,GAAG,IAAI,OAAO,OAAO,CAAC,EAAE,gBAAgB,OAAO,aAAa,EAAE,cAAiB,YAAY,IAAI,IAAI,OAAO,YAAY,CAAC,EAAE,eAAe,OAAO,iBAAiB,EAAE,mBAAsB,YAAY,IAAI,IAAI,2BAA2B,CAAC;AAC9Q,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,WAAW,CAAC,EAAE,mBAAmB,IAAI;AACrF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI;AAC7F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,aAAa,CAAC,EAAE,YAAY,KAAK;AACjF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,kBAAkB,CAAC,EAAE,mBAAmB,IAAI;AAC5F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,aAAa,CAAC;AAC9D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,aAAa,CAAC;AAC9D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC;AACpE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,iBAAiB,CAAC;AAClE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,YAAY;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,SAAY,YAAY,IAAI,IAAI,2BAA2B,CAAC;AAC1E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAe,YAAY,IAAI,IAAI,OAAO,OAAO,CAAC,EAAE,WAAc,YAAY,IAAI,IAAI,OAAO,QAAQ,CAAC;AAAA,EACtH;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,EAAE,OAAO,WAAW,SAAS,OAAO,WAAW,MAAM;AAC/E,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,uFAAuF;AACrH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,EAAE,OAAO,WAAW,SAAS,OAAO,WAAW,MAAM;AAC/E,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,UAAU,EAAE;AAAA,EACnG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,oBAAoB,gBAAgB;AAAA,EACpD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,UAAU,GAAG,wBAAwB,EAAE;AAC1C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,SAAS;AAAA,EACnC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,UAAU,GAAG,4BAA4B,EAAE;AAC9C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,sBAAsB,CAAC;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,gBAAgB,OAAO,YAAY,EAAE,yBAAyB,OAAO,WAAW,IAAI,cAAc,CAAC;AAAA,EACnH;AACF;AACA,IAAM,MAAM,MAAM,CAAC,uBAAuB,qBAAqB;AAC/D,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gCAAgC,CAAC;AAAA,EAC7E;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gCAAgC,CAAC;AAAA,EAC7E;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,kEAAkE,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC1L;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,MAAM,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,MAAM,EAAE;AAAA,EACxC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,WAAW,CAAC,EAAE,GAAG,OAAO;AAC7C,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,4BAA4B,CAAC;AAC7C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,sBAAsB,CAAC;AACjE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,gBAAgB,OAAO,YAAY,EAAE,yBAAyB,OAAO,KAAK,IAAI,cAAc,CAAC;AAAA,EAC7G;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gCAAgC,CAAC;AAAA,EAC7E;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gCAAgC,CAAC;AAAA,EAC7E;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,oFAAoF;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,oFAAoF;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,kEAAkE,GAAG,GAAG,QAAQ,CAAC;AACxL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,eAAe,CAAC;AAC1D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAC,OAAO,KAAK,KAAK;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,MAAM,EAAE;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,MAAM,EAAE;AAAA,EACxC;AACF;AACA,IAAM,MAAM,MAAM,CAAC,MAAM,QAAQ;AACjC,IAAM,MAAM,QAAM,CAAC,MAAM,EAAE;AAC3B,IAAM,OAAO,SAAO;AAAA,EAClB,UAAU;AACZ;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAiB,gBAAgB,GAAG,GAAG,CAAC;AACtD,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,oCAAoC,GAAG,GAAG;AAAA,EAC5F;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,IAAG,mBAAmB,KAAK,iBAAiB,IAAI,GAAG;AAAA,EACrD;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,iBAAiB,WAAW,OAAO,GAAG,GAAG;AAAA,EAC3F;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,iBAAiB,WAAW,OAAO,GAAG,GAAG;AAAA,EAC3F;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,EAAE,GAAG,MAAM;AACvC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,iBAAiB,EAAE,CAAC;AAC3E,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,iBAAiB,IAAI;AAAA,EAC5C;AACF;AACA,SAAS,iFAAiF,IAAI,KAAK;AACjG,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,+BAA+B,EAAE;AAAA,EACnD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,eAAe,QAAQ,EAAE,SAAS,OAAO,KAAK;AAAA,EAC9D;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,EAAE,GAAG,MAAM;AACvC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAQ,gBAAgB,GAAG,MAAM,iBAAiB,EAAE,CAAC,CAAC;AACxG,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,6BAA6B,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,WAAW,cAAc,SAAS,yFAAyF,QAAQ;AACpI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC,EAAE,sBAAsB,SAAS,iGAAiG,QAAQ;AACzI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC,EAAE,wBAAwB,SAAS,mGAAmG,QAAQ;AAC7I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,UAAU,GAAG,wBAAwB,CAAC,EAAE,GAAG,kBAAkB,CAAC;AACjE,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,eAAe,GAAG,kBAAkB,EAAE;AACzC,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,kEAAkE,GAAG,GAAG,aAAa;AACvG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,kEAAkE,GAAG,GAAG,aAAa;AACvG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,kEAAkE,GAAG,GAAG,aAAa;AACvG,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,kEAAkE,GAAG,GAAG,aAAa;AACvG,IAAG,aAAa;AAChB,IAAG,WAAW,IAAI,kFAAkF,GAAG,GAAG,+BAA+B,EAAE;AAC3I,IAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,WAAW,IAAI,kEAAkE,GAAG,GAAG,aAAa;AACvG,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,MAAM,OAAO,eAAe,EAAE,SAAY,YAAY,GAAG,IAAI,OAAO,MAAM,CAAC,EAAE,gBAAmB,YAAY,GAAG,IAAI,OAAO,aAAa,CAAC,EAAE,cAAiB,YAAY,GAAG,IAAI,OAAO,WAAW,CAAC,EAAE,eAAkB,YAAY,GAAG,IAAI,OAAO,YAAY,CAAC,EAAE,WAAW,OAAO,OAAO,EAAE,eAAkB,YAAY,GAAG,IAAI,OAAO,YAAY,CAAC;AAChW,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,iBAAiB,MAAM,EAAE,oBAAoB,OAAO,gBAAgB;AAClF,IAAG,UAAU;AACb,IAAG,WAAW,qBAAqB,OAAO,iBAAiB,EAAE,yBAA4B,YAAY,GAAG,IAAI,uBAAuB,CAAC;AACpI,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,WAAW,CAAC,EAAE,mBAAmB,IAAI;AACrF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI,EAAE,QAAQ,OAAO,MAAM,IAAI,WAAW,CAAC;AACpI,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI,EAAE,QAAQ,OAAO,MAAM,IAAI,WAAW,CAAC;AACpI,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,aAAa,CAAC,EAAE,YAAY,KAAK,EAAE,QAAQ,OAAO,MAAM,IAAI,MAAM,CAAC;AACnH,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,YAAY;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,WAAc,YAAY,IAAI,IAAI,sBAAsB,CAAC,EAAE,YAAY,KAAK;AAAA,EAC5F;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,4FAA4F;AAC1H,YAAM,kBAAqB,cAAc,GAAG,EAAE;AAC9C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,eAAe,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,kCAAkC,EAAE;AACzD,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,WAAW,qBAAqB,SAAS,8HAA8H,QAAQ;AAChL,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,KAAK,MAAM,CAAC;AAAA,IAC9D,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,kBAAkB,IAAI;AAC5B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,mCAAsC,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC;AAC/H,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,YAAY,GAAG,GAAG,OAAO,QAAQ,CAAC,EAAE,SAAS,OAAO,KAAK,EAAE,cAAiB,YAAY,GAAG,IAAI,OAAO,aAAa,CAAC,EAAE,eAAkB,YAAY,GAAG,IAAI,OAAO,YAAY,CAAC;AAAA,EAC7M;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,IAAI,IAAI,gBAAgB,EAAE;AAC9G,IAAG,OAAO,GAAG,OAAO;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAW,YAAY,GAAG,GAAG,OAAO,YAAY,CAAC;AAAA,EACjE;AACF;AACA,IAAM,OAAO,MAAM,CAAC,UAAU;AAC9B,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAiB,gBAAgB,GAAG,IAAI,CAAC;AACvD,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,8BAA8B,GAAG,GAAG;AAAA,EACtF;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,mBAAmB,KAAK,YAAY,IAAI,GAAG;AAAA,EAChD;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,YAAY,WAAW,OAAO,GAAG,GAAG;AAAA,EACtF;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,YAAY,WAAW,OAAO,GAAG,GAAG;AAAA,EACtF;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,EAAE,GAAG,MAAM;AACvC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,YAAY,EAAE,CAAC;AACtE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,YAAY,OAAO,KAAK,YAAY,WAAW,KAAK,YAAY,UAAU,GAAG;AAAA,EAC1G;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,6BAA6B,EAAE;AAAA,EACjD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,WAAW,YAAY,WAAW;AAAA,EACvC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,mBAAmB,KAAK,YAAY,cAAc,GAAG;AAAA,EAC1D;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,+BAA+B,EAAE;AAAA,EACnD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,eAAe,QAAQ,EAAE,SAAS,OAAO,KAAK;AAAA,EAC9D;AACF;AACA,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAA8B;AAAA,IACjE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,2BAA2B,CAAC;AAAA,MACzC,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,gBAAgB,GAAG,oBAAoB,GAAG,CAAC,SAAS,gBAAgB,GAAG,sBAAsB,CAAC;AAAA,MAC9H,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,YAAY,CAAC,EAAE,GAAG,kDAAkD,GAAG,GAAG,YAAY,CAAC;AAAA,QAClK;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,SAAS,QAAQ,OAAO,OAAO,IAAI,SAAS,KAAK,EAAE;AAC7E,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,EAAE,IAAI,SAAS,QAAQ,OAAO,OAAO,IAAI,SAAS,KAAK,GAAG;AAAA,QAClF;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,MAAS,eAAkB,aAAa;AAAA,MACjF,QAAQ,CAAC,6IAA6I;AAAA,MACtJ,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,yGAAyG;AAAA,IACpH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,YAAY,QAAQ,aAAa;AAC/B,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,oBAAoB,IAAI,YAAY,EAAE;AAC3C,SAAK,mBAAmB,IAAI,iBAAiB;AAAA,MAC3C,aAAa;AAAA,MACb,eAAe,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE;AAAA,MACpC,cAAc;AAAA,IAChB,CAAC;AACD,SAAK,WAAW,IAAI,gBAAgB,IAAI;AACxC,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,sBAAsB,KAAK,MAAM,SAAS,KAAK,IAAI,SAAO,IAAI,IAAI,aAAa,CAAC,GAAG,IAAI,UAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,qBAAqB,CAAC;AAC5J,SAAK,uBAAuB,KAAK,MAAM,SAAS,KAAK,IAAI,SAAO,IAAI,IAAI,gBAAgB,CAAC,GAAG,IAAI,aAAW,CAAC,UAAU,KAAK,CAAC,OAAO,GAAG,UAAU,EAAE,GAAG,qBAAqB,CAAC;AAC3K,UAAM,cAAc,KAAK,kBAAkB,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI,MAAM,KAAK,sBAAsB,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC;AACvI,kBAAc,KAAK,qBAAqB,KAAK,sBAAsB,aAAa,KAAK,QAAQ,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,aAAa,cAAc,UAAU,MAAM;AACnL,YAAMA,QAAO;AACb,YAAM,QAAQ,cAAc,KAAK;AACjC,WAAK,kBAAkB,KAAK;AAAA,QAC1B,YAAY,cAAc;AAAA,QAC1B;AAAA,QACA,MAAAA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,iBAAiB,gBAAgB,KAAK,SAAS,OAAO,OAAK,KAAK,kBAAkB,SAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;AAC5G,SAAK,iBAAiB,kBAAkB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAC5F,WAAK,gBAAgB,KAAK,UAAU,IAAI,OAAK,EAAE,EAAE,CAAC;AAAA,IACpD,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,sBAAsB,MAAM;AAC1B,SAAK,SAAS,eAAe,IAAI;AAAA,EACnC;AAAA,EACA,wBAAwB,SAAS;AAC/B,SAAK,SAAS,kBAAkB,OAAO;AAAA,EACzC;AAAA,EACA,UAAU;AACR,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,SAAS,KAAK,OAAO;AACnB,SAAK,OAAO,SAAS,CAAC,MAAM,iCACvB,KAAK,MAAM,SAAS,SADG;AAAA,MAE1B,CAAC,GAAG,GAAG;AAAA,IACT,EAAC,GAAG;AAAA,MACF,YAAY,KAAK;AAAA,MACjB,qBAAqB;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,mBAAmB;AAC/E,aAAO,KAAK,qBAAqB,mCAAqC,kBAAqB,MAAM,GAAM,kBAAqB,WAAW,CAAC;AAAA,IAC1I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gCAAgC,CAAC;AAAA,MAC9C,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,mBAAmB;AAAA,QACnB,aAAa;AAAA,MACf;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,MACrB;AAAA,MACA,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,cAAc,sBAAsB,MAAM,SAAS,gBAAgB,cAAc,aAAa,GAAG,CAAC,GAAG,cAAc,iBAAiB,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,uBAAuB,GAAG,CAAC,MAAM,MAAM,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,QAAQ,GAAG,WAAW,UAAU,GAAG,CAAC,MAAM,UAAU,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,iBAAiB,GAAG,SAAS,GAAG,CAAC,GAAG,gBAAgB,GAAG,YAAY,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,GAAG,UAAU,CAAC;AAAA,MACzkB,UAAU,SAAS,0CAA0C,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,cAAc,SAAS,iFAAiF,QAAQ;AAC5H,mBAAO,IAAI,sBAAsB,MAAM;AAAA,UACzC,CAAC,EAAE,sBAAsB,SAAS,yFAAyF,QAAQ;AACjI,mBAAO,IAAI,wBAAwB,MAAM;AAAA,UAC3C,CAAC;AACD,UAAG,UAAU,GAAG,wBAAwB,CAAC,EAAE,GAAG,kBAAkB,CAAC;AACjE,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,aAAa;AAC7F,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,aAAa;AAC/F,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,aAAa;AAC/F,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,aAAa;AAC/F,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,aAAa;AAC/F,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,aAAa;AAC/F,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,MAAM,IAAI,UAAU,EAAE,SAAS,IAAI,OAAO,EAAE,gBAAmB,YAAY,GAAG,IAAI,IAAI,oBAAoB,CAAC,EAAE,cAAc,IAAI,UAAU,EAAE,eAAkB,YAAY,GAAG,IAAI,IAAI,mBAAmB,CAAC;AACtN,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,cAAc,IAAI,UAAU,EAAE,iBAAiB,GAAG,EAAE,oBAAoB,IAAI,gBAAgB;AAC1G,UAAG,UAAU;AACb,UAAG,WAAW,qBAAqB,IAAI,iBAAiB,EAAE,yBAA4B,YAAY,GAAG,IAAI,oCAAoC,CAAC;AAC9I,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,GAAG,IAAI,WAAW,CAAC,EAAE,mBAAmB,IAAI;AACpF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI;AAC7F,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI;AAC7F,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,eAAe,CAAC,EAAE,YAAY,KAAK;AACnF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,eAAe,CAAC,EAAE,mBAAmB,IAAI;AACzF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,wBAAwB,CAAC;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAqB,YAAe,yBAA4B,qBAAwB,2BAA8B,2BAA2B,8BAAmC,WAAc,eAAkB,cAAc;AAAA,MACpP,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oCAAN,MAAM,mCAAkC;AAAA,EACtC,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,qBAAqB,IAAI,gBAAgB;AAAA,MAC5C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,kBAAkB,KAAK,mBAAmB,KAAK,UAAU,CAAC;AAAA,MAC9D;AAAA,MACA,MAAAA;AAAA,MACA;AAAA,IACF,MAAM,KAAK,YAAY,SAAS,gBAAgBA,OAAM,MAAM,UAAU,EAAE,UAAU,SAAO,IAAI,SAAS,CAAC,CAAC;AACxG,SAAK,aAAa,gBAAgB,KAAK,IAAI,SAAO,IAAI,KAAK,CAAC;AAC5D,SAAK,kBAAkB,gBAAgB,KAAK,IAAI,SAAO,IAAI,UAAU,CAAC;AAAA,EACxE;AAAA,EACA,SAAS;AACP,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,MAAM;AACJ,SAAK,YAAY,KAAK,mBAAmB;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0CAA0C,mBAAmB;AAChF,aAAO,KAAK,qBAAqB,oCAAsC,kBAAqB,WAAW,CAAC;AAAA,IAC1G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kCAAkC,CAAC;AAAA,MAChD,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,cAAc,sCAAsC,GAAG,qBAAqB,mBAAmB,WAAW,cAAc,SAAS,mBAAmB,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,eAAe,GAAG,SAAS,UAAU,CAAC;AAAA,MAChU,UAAU,SAAS,2CAA2C,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,CAAC;AACjG,UAAG,eAAe,GAAG,kCAAkC,CAAC;AACxD,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,qBAAqB,SAAS,uGAAuG,QAAQ;AACzJ,mBAAO,IAAI,mBAAmB,KAAK,MAAM;AAAA,UAC3C,CAAC,EAAE,mBAAmB,SAAS,qGAAqG,QAAQ;AAC1I,mBAAO,IAAI,sBAAsB;AAAA,UACnC,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,0DAA0D,GAAG,IAAI,eAAe,CAAC;AAAA,QACpG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,WAAc,YAAY,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,cAAiB,YAAY,GAAG,GAAG,IAAI,eAAe,CAAC,EAAE,SAAS,IAAI,KAAK,EAAE,qBAAqB,IAAI,mBAAmB;AAAA,QAC1L;AAAA,MACF;AAAA,MACA,cAAc,CAAI,wBAA2B,sBAAsB,kCAAuC,WAAc,aAAa;AAAA,MACrI,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,qBAAqB,CAAC;AAAA,EAC7B;AAAA,EACA,WAAW;AACT,SAAK,YAAY,aAAa,UAAU,MAAM,KAAK,eAAe,aAAa,CAAC;AAAA,EAClF;AAAA,EACA,SAAS;AACP,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,YAAY,KAAK,WAAW;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,kBAAqB,iBAAiB,CAAC;AAAA,IAC3G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,2BAA2B,CAAC;AAAA,MACzC,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,aAAa,sBAAsB,cAAc,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,eAAe,GAAG,SAAS,UAAU,CAAC;AAAA,MAC7O,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,CAAC;AAC5F,UAAG,UAAU,GAAG,oBAAoB,CAAC;AACrC,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,CAAC;AAAA,QAC9F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,aAAa,IAAI,WAAW,EAAE,sBAAsB,IAAI,kBAAkB,EAAE,gBAAgB,IAAI,YAAY;AAAA,QAC5H;AAAA,MACF;AAAA,MACA,cAAc,CAAM,MAAW,sBAA2B,oBAAuB,wBAA2B,sBAAyB,sBAAyB,aAAa;AAAA,MAC3K,QAAQ,CAAC,4DAA4D;AAAA,MACrE,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,2CAA2C;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,cAAc,gBAAgB;AACxC,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,qBAAqB,CAAC;AAC3B,SAAK,WAAW;AAChB,SAAK,uBAAuB,IAAI,aAAa;AAC7C,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,4BAA4B,IAAI,gBAAgB,KAAK;AAAA,EAC5D;AAAA,EACA,WAAW;AACT,UAAM,cAAc,KAAK,YAAY,IAAI,aAAa;AAGtD,QAAI,CAAC,YAAY,OAAO;AACtB,WAAK,0BAA0B,KAAK,OAAO,WAAS,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACnF,aAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,gBAAgB,QAAQ,KAAK,sBAAsB,MAAM;AAChE,WAAK,0BAA0B,KAAK,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,eAAe,aAAa;AAC1B,QAAI,CAAC,KAAK,oBAAoB;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,mBAAmB,KAAK,OAAK,EAAE,SAAS,WAAW;AACtE,WAAO,QAAQ,MAAM,OAAO;AAAA,EAC9B;AAAA,EACA,6BAA6B;AAC3B,SAAK,oBAAoB,KAAK,KAAK,YAAY,MAAM,EAAE;AACvD,SAAK,YAAY,YAAY;AAAA,EAC/B;AAAA,EACA,8BAA8B;AAC5B,SAAK,qBAAqB,KAAK,KAAK,YAAY,MAAM,EAAE;AACxD,SAAK,YAAY,YAAY;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,SAAK,cAAc,KAAK,KAAK,YAAY,MAAM,EAAE;AACjD,SAAK,YAAY,YAAY;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,cAAc,8BAA8B;AAAA,MAC5D,QAAQ;AAAA,QACN,aAAa,KAAK;AAAA,QAClB,cAAc,KAAK;AAAA,QACnB,oBAAoB,KAAK;AAAA,MAC3B;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC,EAAE,UAAU,MAAM;AACjB,WAAK,eAAe,aAAa;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,YAAY,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IAC1I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,QACd,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,SAAS,eAAe,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,iBAAiB,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,cAAc,IAAI,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,sBAAsB,IAAI,GAAG,cAAc,GAAG,CAAC,SAAS,qBAAqB,QAAQ,IAAI,GAAG,CAAC,mBAAmB,IAAI,GAAG,SAAS,UAAU,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,QAAQ,UAAU,mBAAmB,IAAI,GAAG,OAAO,GAAG,CAAC,SAAS,SAAS,GAAG,WAAW,CAAC;AAAA,MAC9sB,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,qCAAqC,IAAI,GAAG,OAAO,CAAC;AAAA,QACvE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,YAAY,KAAK;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,MAAS,eAAkB,mBAAsB,uBAA0B,0BAA6B,uBAA0B,2BAA8B,qBAAwB,aAAa;AAAA,MAC9O,QAAQ,CAAC,qZAAqZ;AAAA,MAC9Z,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,wRAAwR;AAAA,IACnS,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qCAAN,MAAM,oCAAmC;AAAA,EACvC,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA,EACA,WAAW;AACT,SAAK,UAAU,KAAK,YAAY,SAAS,qBAAqB,EAAE,UAAU,SAAO,IAAI,eAAe,KAAK;AAAA,EAC3G;AAAA,EACA,SAAS;AACP,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,MAAM;AACJ,SAAK,YAAY,KAAK,gBAAgB;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,mBAAmB;AACjF,aAAO,KAAK,qBAAqB,qCAAuC,kBAAqB,WAAW,CAAC;AAAA,IAC3G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kCAAkC,CAAC;AAAA,MAChD,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,YAAY,QAAQ,aAAa,MAAM,GAAG,iBAAiB,SAAS,UAAU,YAAY,WAAW,aAAa,YAAY,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,iBAAiB,QAAQ,GAAG,OAAO,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,eAAe,GAAG,SAAS,UAAU,CAAC;AAAA,MAC1a,UAAU,SAAS,4CAA4C,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,eAAe,CAAC;AAClG,UAAG,eAAe,GAAG,aAAa,CAAC;AACnC,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,iBAAiB,iBAAiB,SAAS,+EAA+E,QAAQ;AACnI,YAAG,mBAAmB,IAAI,kBAAkB,MAAM,MAAM,IAAI,mBAAmB;AAC/E,mBAAO;AAAA,UACT,CAAC;AACD,UAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,2DAA2D,GAAG,GAAG,eAAe,CAAC;AACxL,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,2DAA2D,GAAG,IAAI,eAAe,CAAC;AAAA,QACrG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,SAAY,YAAY,GAAG,GAAG,IAAI,OAAO,CAAC,EAAE,UAAU,KAAK,EAAE,YAAY,IAAI;AAC3F,UAAG,iBAAiB,WAAW,IAAI,gBAAgB;AACnD,UAAG,WAAW,aAAa,IAAI,EAAE,cAAc,KAAK;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAM,iBAAsB,SAAc,mBAAwB,2BAAgC,0BAA6B,eAAkB,wBAA2B,sBAA2B,WAAc,aAAa;AAAA,MAChP,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oCAAN,MAAM,mCAAkC;AAAA,EACtC,YAAY,8BAA8B;AACxC,SAAK,+BAA+B;AACpC,SAAK,cAAc,IAAI,aAAa;AAAA,EACtC;AAAA,EACA,WAAW;AACT,UAAM,gBAAgB,KAAK,6BAA6B,aAAa,KAAK,MAAM,IAAI;AACpF,UAAM,eAAe,KAAK,UAAU,gBAAgB,aAAa;AACjE,iBAAa,SAAS,QAAQ,KAAK;AACnC,iBAAa,SAAS,WAAW,KAAK;AACtC,SAAK,WAAW,aAAa;AAC7B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0CAA0C,mBAAmB;AAChF,aAAO,KAAK,qBAAqB,oCAAsC,kBAAqB,4BAA4B,CAAC;AAAA,IAC3H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iCAAiC,CAAC;AAAA,MAC/C,WAAW,SAAS,wCAAwC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,GAAG,gBAAgB;AAAA,QACzC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,QAClE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,eAAe,eAAe,aAAa,aAAa,QAAQ,YAAY,WAAW,CAAC;AAAA,MACrH,UAAU,SAAS,2CAA2C,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,sBAAsB,CAAC;AAC5C,UAAG,WAAW,eAAe,SAAS,uFAAuF;AAC3H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,YAAY,KAAK,CAAC;AAAA,UAC9C,CAAC;AACD,UAAG,UAAU,GAAG,OAAO,MAAM,CAAC;AAC9B,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,eAAe,IAAI,SAAS,eAAe,IAAI,KAAK,CAAC,EAAE,aAAa,IAAI,SAAS,gBAAgB,IAAI,SAAS,aAAa,IAAI,KAAK,CAAC,EAAE,aAAa,IAAI,MAAM,SAAS,EAAE,QAAQ,IAAI,SAAS,WAAW,IAAI,SAAS,QAAQ,IAAI,KAAK,CAAC,EAAE,YAAY,IAAI,SAAS,WAAW,IAAI,KAAK,CAAC,EAAE,aAAa,CAAC,IAAI,YAAY,CAAC,IAAI,SAAS,WAAW,IAAI,KAAK,CAAC;AAAA,QAC3W;AAAA,MACF;AAAA,MACA,cAAc,CAAI,sBAAsB;AAAA,MACxC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,8BAA8B;AACxC,SAAK,+BAA+B;AACpC,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,mBAAmB,MAAM;AACvB,WAAO,CAAC,CAAC,KAAK,6BAA6B,aAAa,IAAI;AAAA,EAC9D;AAAA,EACA,eAAe,OAAO;AACpB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK,iBAAiB;AAAA,MACtB,KAAK,iBAAiB;AAAA,MACtB,KAAK,iBAAiB;AACpB,eAAO;AAAA,MACT,KAAK,iBAAiB;AACpB,eAAO;AAAA,MACT,KAAK,iBAAiB;AACpB,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,gBAAgB,OAAO;AACrB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK,iBAAiB;AACpB,eAAO;AAAA,MACT,KAAK,iBAAiB;AACpB,eAAO,CAAC,eAAe,UAAU;AAAA,MACnC,KAAK,iBAAiB;AACpB,eAAO;AAAA,MACT,KAAK,iBAAiB;AAAA,MACtB,KAAK,iBAAiB;AACpB,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK,iBAAiB;AAAA,MACtB,KAAK,iBAAiB;AACpB,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,eAAe;AACjB,aAAO,GAAG,cAAc,SAAS,IAAI,cAAc,QAAQ;AAAA,IAC7D,OAAO;AACL,aAAO,GAAG,KAAK,SAAS,SAAS,IAAI,KAAK,SAAS,QAAQ;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,QAAQ,KAAK;AAAA,MAChB,MAAM,KAAK;AAAA,IACb,CAAC;AACD,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,4BAA4B,CAAC;AAAA,IAClH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,MACA,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,aAAa,QAAQ,eAAe,SAAS,GAAG,YAAY,WAAW,GAAG,kBAAkB,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,aAAa,QAAQ,eAAe,SAAS,GAAG,YAAY,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,QAAQ,QAAQ,GAAG,QAAQ,GAAG,iBAAiB,SAAS,GAAG,CAAC,GAAG,OAAO,iBAAiB,GAAG,SAAS,UAAU,GAAG,CAAC,GAAG,YAAY,SAAS,YAAY,eAAe,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,eAAe,YAAY,SAAS,UAAU,GAAG,CAAC,GAAG,eAAe,aAAa,aAAa,QAAQ,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,sBAAsB,IAAI,GAAG,gBAAgB,MAAM,GAAG,CAAC,SAAS,qBAAqB,QAAQ,IAAI,GAAG,CAAC,eAAe,cAAc,GAAG,CAAC,mBAAmB,IAAI,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,SAAS,GAAG,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC;AAAA,MACnkC,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,sBAAsB,CAAC,EAAE,GAAG,kDAAkD,GAAG,GAAG,gBAAgB,CAAC;AACpL,UAAG,UAAU,GAAG,sBAAsB,CAAC;AACvC,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,gBAAgB;AAClD,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO;AACpC,UAAG,UAAU;AACb,UAAG,WAAW,UAAU,IAAI;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,SAAc,MAAW,UAAe,cAAmB,iBAAsB,sBAA2B,iBAAsB,SAAY,2BAA8B,mBAAsB,uBAA0B,0BAA6B,uBAA0B,sBAAyB,qBAAwB,wBAA2B,wBAA2B,6BAA6B,mCAAsC,eAAkB,iBAAiB;AAAA,MACzgB,QAAQ,CAAC,4fAA4f;AAAA,MACrgB,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,+UAA+U;AAAA,IAC1V,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyBxB,iBAAiB;AAAA;AAEvB,IAAM,0BAAN,MAAM,iCAAgC,yBAAyB;AAAA,EAC7D,YAAY,gBAAgB,aAAa,aAAa,cAAc,qBAAqB;AACvF,UAAM;AACN,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAC3B,SAAK,eAAe,KAAK,qBAAqB,UAAU;AACxD,SAAK,sBAAsB,KAAK,qBAAqB,SAAS;AAC9D,SAAK,aAAa,KAAK,YAAY,MAAM;AAAA,MACvC,UAAU,KAAK,YAAY,MAAM;AAAA,QAC/B,OAAO;AAAA,QACP,WAAW,CAAC,IAAI,WAAW,QAAQ;AAAA,QACnC,UAAU,CAAC,IAAI,WAAW,QAAQ;AAAA,QAClC,aAAa;AAAA,QACb,cAAc,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;AAAA,QAC1D,UAAU;AAAA,QACV,cAAc,KAAK,YAAY,MAAM,wBAAwB,KAAK,YAAY,CAAC;AAAA,MACjF,CAAC;AAAA,MACD,WAAW,IAAI,iBAAiB,CAAC,CAAC;AAAA,IACpC,CAAC;AACD,SAAK,eAAe,IAAI,QAAQ;AAChC,SAAK,uBAAuB,oBAAI,IAAI;AACpC,SAAK,yBAAyB;AAC9B,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB,IAAI,QAAQ;AAAA,EACvC;AAAA,EACA,WAAW;AACT,SAAK,KAAK;AACV,SAAK,sBAAsB,KAAK,YAAY,SAAS,sBAAsB,EAAE,UAAU,YAAU,OAAO,UAAU,KAAK,EAAE,KAAK,YAAY,CAAC,CAAC;AAC5I,UAAM,uBAAuB,KAAK,QAAQ,KAAK,MAAM,KAAK,iBAAiB,CAAC;AAC5E,SAAK,UAAU,qBAAqB,KAAK,IAAI,cAAY,SAAS,OAAO,KAAK,CAAC;AAC/E,SAAK,eAAe,KAAK,QAAQ,KAAK,IAAI,cAAY,SAAS,OAAO,UAAU,CAAC;AACjF,SAAK,WAAW,KAAK,aAAa,KAAK,UAAU,IAAI,GAAG,UAAU,MAAM,KAAK,YAAY,SAAS,mBAAmB,KAAK,IAAI;AAAA,MAC5H,MAAM;AAAA,QACJ,WAAW,UAAU;AAAA,MACvB;AAAA,IACF,CAAC,EAAE,UAAU,UAAQ,KAAK,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,EACrD;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,kBAAkB,SAAS;AAAA,EAClC;AAAA,EACA,yBAAyB;AACvB,UAAM,YAAY,KAAK,WAAW,IAAI,CAAC,WAAW,CAAC;AACnD,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,2BAA2B,IAAI;AAC7B,SAAK,0BAA0B;AAC/B,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,4BAA4B,IAAI;AAC9B,SAAK,2BAA2B;AAChC,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,oBAAoB,IAAI;AACtB,QAAI,KAAK,qBAAqB,IAAI,EAAE,GAAG;AACrC,WAAK,qBAAqB,OAAO,EAAE;AAAA,IACrC,OAAO;AACL,WAAK,qBAAqB,IAAI,EAAE;AAAA,IAClC;AAAA,EACF;AAAA,EACA,aAAa;AACX,UAAM,mBAAmB,KAAK,WAAW,IAAI,WAAW;AACxD,UAAM,aAAa,KAAK,YAAY,MAAM;AAAA,MACxC,UAAU;AAAA,MACV,SAAS;AAAA,MACT,aAAa,CAAC,IAAI,WAAW,QAAQ;AAAA,MACrC,aAAa;AAAA,MACb,MAAM;AAAA,MACN,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,aAAa,CAAC,IAAI,WAAW,QAAQ;AAAA,MACrC,aAAa;AAAA,MACb,wBAAwB;AAAA,MACxB,uBAAuB;AAAA,MACvB,cAAc,KAAK,YAAY,MAAM,KAAK,oBAAoB,OAAO,CAAC,MAAM,UAAW,iCAClF,OADkF;AAAA,QAErF,CAAC,MAAM,IAAI,GAAG;AAAA,MAChB,IAAI,CAAC,CAAC,CAAC;AAAA,IACT,CAAC;AACD,qBAAiB,KAAK,UAAU;AAAA,EAClC;AAAA,EACA,qBAAqB,cAAc;AACjC,SAAK,gBAAgB,CAAC;AACtB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,oBAAoB,MAAM;AACxB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,SAAS;AACP,UAAM,eAAe,KAAK,WAAW,IAAI,UAAU;AACnD,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,aAAa;AACjB,UAAM,eAAe,aAAa,IAAI,cAAc,GAAG;AACvD,QAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,UAAU;AAC5C;AAAA,IACF;AACA,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,SAAK,YAAY,SAAS,eAAe,UAAU,QAAQ,EAAE,UAAU,CAAC;AAAA,MACtE;AAAA,IACF,MAAM;AACJ,cAAQ,eAAe,YAAY;AAAA,QACjC,KAAK;AACH,eAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,YACvE,QAAQ;AAAA,UACV,CAAC;AACD,cAAI,eAAe,gBAAgB,CAAC,UAAU;AAC5C,iBAAK,oBAAoB,OAAO;AAAA,cAC9B,SAAS,OAAO,kCAAkC;AAAA,cAClD,iBAAiB;AAAA,gBACf;AAAA,cACF;AAAA,cACA,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AAAA,UACH;AACA,eAAK,WAAW,eAAe;AAC/B,eAAK,yBAAyB;AAC9B,eAAK,eAAe,aAAa;AACjC,eAAK,OAAO,SAAS,CAAC,OAAO,eAAe,EAAE,GAAG;AAAA,YAC/C,YAAY,KAAK;AAAA,UACnB,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,MAAM,eAAe,OAAO;AAAA,MACzD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,QAAQ,KAAK,KAAK,CAAC,GAAG,SAAS,CAAC;AAAA,MACnC;AAAA,IACF,MAAM;AACJ,YAAM,iBAAiB,CAAC;AACxB,YAAM,eAAe,KAAK,WAAW,IAAI,UAAU;AACnD,UAAI,gBAAgB,aAAa,OAAO;AACtC,cAAM,YAAY,aAAa;AAC/B,cAAM,eAAe,aAAa,IAAI,cAAc,GAAG;AACvD,cAAM,WAAW;AAAA,UACf;AAAA,UACA,OAAO,UAAU;AAAA,UACjB,cAAc,UAAU;AAAA,UACxB,WAAW,UAAU;AAAA,UACrB,UAAU,UAAU;AAAA,UACpB,aAAa,UAAU;AAAA,UACvB;AAAA,QACF;AACA,uBAAe,KAAK,KAAK,YAAY,SAAS,eAAe,QAAQ,EAAE,KAAK,IAAI,SAAO,IAAI,cAAc,CAAC,CAAC;AAAA,MAC7G;AACA,YAAM,mBAAmB,KAAK,WAAW,IAAI,WAAW;AACxD,UAAI,oBAAoB,iBAAiB,SAAS,KAAK,wBAAwB;AAC7E,mBAAW,kBAAkB,iBAAiB,UAAU;AACtD,cAAI,eAAe,SAAS,KAAK,wBAAwB;AACvD,kBAAM,UAAU,eAAe;AAC/B,kBAAM,QAAQ;AAAA,cACZ,UAAU,QAAQ;AAAA,cAClB,SAAS,QAAQ;AAAA,cACjB,aAAa,QAAQ;AAAA,cACrB,aAAa,QAAQ;AAAA,cACrB,MAAM,QAAQ;AAAA,cACd,UAAU,QAAQ;AAAA,cAClB,YAAY,QAAQ;AAAA,cACpB,aAAa,QAAQ;AAAA,cACrB,aAAa,QAAQ;AAAA,cACrB,wBAAwB,KAAK,6BAA6B,QAAQ;AAAA,cAClE,uBAAuB,KAAK,4BAA4B,QAAQ;AAAA,cAChE,cAAc,QAAQ;AAAA,YACxB;AACA,gBAAI,CAAC,QAAQ,IAAI;AACf,6BAAe,KAAK,KAAK,YAAY,SAAS,sBAAsB,IAAI,KAAK,EAAE,KAAK,IAAI,SAAO,IAAI,qBAAqB,CAAC,CAAC;AAAA,YAC5H,OAAO;AACL,kBAAI,KAAK,qBAAqB,IAAI,QAAQ,EAAE,GAAG;AAC7C,+BAAe,KAAK,KAAK,YAAY,SAAS,sBAAsB,QAAQ,EAAE,EAAE,KAAK,IAAI,SAAO,IAAI,qBAAqB,CAAC,CAAC;AAAA,cAC7H,OAAO;AACL,+BAAe,KAAK,KAAK,YAAY,SAAS,sBAAsB,iCAC/D,QAD+D;AAAA,kBAElE,IAAI,QAAQ;AAAA,gBACd,EAAC,EAAE,KAAK,IAAI,SAAO,IAAI,qBAAqB,CAAC,CAAC;AAAA,cAChD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,SAAS,cAAc;AAAA,IAChC,CAAC,CAAC,EAAE,UAAU,UAAQ;AACpB,UAAI,WAAW;AACf,iBAAW,UAAU,MAAM;AACzB,gBAAQ,OAAO,YAAY;AAAA,UACzB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,gBAAI,CAAC,UAAU;AACb,mBAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,gBACvE,QAAQ;AAAA,cACV,CAAC;AACD,yBAAW;AACX,mBAAK,WAAW,eAAe;AAC/B,mBAAK,yBAAyB;AAC9B,mBAAK,eAAe,aAAa;AACjC,mBAAK,aAAa,KAAK;AACvB,mBAAK,gBAAgB,EAAE,UAAU;AAAA,YACnC;AACA;AAAA,UACF,KAAK;AACH,iBAAK,oBAAoB,MAAM,OAAO,OAAO;AAC7C;AAAA,QACJ;AAAA,MACF;AAAA,IACF,GAAG,SAAO;AACR,WAAK,oBAAoB,MAAM,OAAO,4BAA4B,GAAG;AAAA,QACnE,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AACX,SAAK,aAAa,cAAc,oCAAoC;AAAA,MAClE,MAAM;AAAA,IACR,CAAC,EAAE,KAAK,UAAU,cAAY,WAAW,KAAK,QAAQ,IAAI,KAAK,GAAG,UAAU,aAAW,KAAK,YAAY,SAAS,oBAAoB,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU;AAAA,MACnK,MAAM,SAAO;AACX,aAAK,oBAAoB,QAAQ,OAAO,yCAAyC,GAAG;AAAA,UAClF,eAAe;AAAA,UACf,WAAW,IAAI,oBAAoB;AAAA,QACrC,CAAC;AAAA,MACH;AAAA,MACA,UAAU,MAAM;AACd,aAAK,gBAAgB,EAAE,UAAU;AACjC,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,aAAa,OAAO;AAAA,MACvB,OAAO,OAAO,6CAA6C;AAAA,MAC3D,SAAS,CAAC;AAAA,QACR,MAAM;AAAA,QACN,OAAO,OAAO,eAAe;AAAA,MAC/B,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO,OAAO,eAAe;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,UAAU,cAAY,WAAW,KAAK,YAAY,SAAS,yBAAyB,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,GAAG,UAAU,MAAM,KAAK,gBAAgB,CAAC,CAAC,EAAE,UAAU,YAAU;AACtL,WAAK,oBAAoB,QAAQ,OAAO,8CAA8C,GAAG;AAAA,QACvF,eAAe;AAAA,QACf,WAAW,MAAM;AAAA,MACnB,CAAC;AACD,WAAK,aAAa,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,EACF,GAAG;AACD,SAAK,YAAY,SAAS,kBAAkB,KAAK,IAAI,IAAI,EAAE,UAAU,MAAM;AACzE,WAAK,aAAa,KAAK;AACvB,WAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,QACvE,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,aAAa,cAAc,yBAAyB;AAAA,MACvD,UAAU;AAAA,MACV,QAAQ;AAAA,QACN,wBAAwB;AAAA,QACxB,MAAM,MAAM,KAAK;AAAA,MACnB;AAAA,IACF,CAAC,EAAE,KAAK,UAAU,YAAU;AAC1B,UAAI,QAAQ;AACV,eAAO,KAAK,YAAY,SAAS,mBAAmB;AAAA,UAClD,QAAQ,MAAM;AAAA,UACd,MAAM,OAAO;AAAA,QACf,CAAC;AAAA,MACH,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC,EAAE,UAAU,YAAU;AACtB,WAAK,aAAa,KAAK;AACvB,WAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,QACvE,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,KAAK,aAAa,OAAO;AAAA,MAC9B,OAAO,OAAO,4BAA4B;AAAA,MAC1C,MAAM,MAAM,KAAK;AAAA,MACjB,SAAS,CAAC;AAAA,QACR,MAAM;AAAA,QACN,OAAO,OAAO,eAAe;AAAA,MAC/B,GAAG;AAAA,QACD,MAAM;AAAA,QACN,OAAO,OAAO,eAAe;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC,EAAE,KAAK,UAAU,SAAO,MAAM,KAAK,YAAY,SAAS,mBAAmB,MAAM,EAAE,IAAI,KAAK,CAAC,EAAE,UAAU,MAAM;AAC9G,WAAK,aAAa,KAAK;AACvB,WAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,QACvE,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,gBAAgB,KAAK,WAAW,IAAI,UAAU;AACpD,QAAI,eAAe;AACjB,oBAAc,WAAW;AAAA,QACvB,OAAO,OAAO,SAAS;AAAA,QACvB,WAAW,OAAO;AAAA,QAClB,UAAU,OAAO;AAAA,QACjB,aAAa,OAAO,eAAe;AAAA,QACnC,cAAc,OAAO;AAAA,QACrB,UAAU;AAAA,QACV,cAAc,CAAC;AAAA,MACjB,CAAC;AAAA,IACH;AACA,QAAI,OAAO,WAAW;AACpB,YAAM,iBAAiB,IAAI,iBAAiB,CAAC,CAAC;AAC9C,iBAAW,WAAW,OAAO,WAAW;AACtC,cAGI,cAFF;AAAA;AAAA,QAv/FV,IAy/FY,IADC,iBACD,IADC;AAAA,UADH;AAAA;AAGF,cAAM,eAAe,KAAK,YAAY,MAAM,iCACvC,OADuC;AAAA,UAE1C,aAAa,QAAQ,QAAQ;AAAA,UAC7B,cAAc,KAAK,YAAY,MAAM,KAAK,oBAAoB,OAAO,CAAC,MAAM,UAAW,iCAClF,OADkF;AAAA,YAErF,CAAC,MAAM,IAAI,GAAG,QAAQ,cAAc,EAAE,MAAM,IAAI;AAAA,UAClD,IAAI,CAAC,CAAC,CAAC;AAAA,QACT,EAAC;AACD,uBAAe,KAAK,YAAY;AAChC,YAAI,QAAQ,wBAAwB;AAClC,eAAK,2BAA2B,QAAQ;AAAA,QAC1C;AACA,YAAI,QAAQ,uBAAuB;AACjC,eAAK,0BAA0B,QAAQ;AAAA,QACzC;AAAA,MACF;AACA,WAAK,WAAW,WAAW,aAAa,cAAc;AAAA,IACxD;AACA,QAAI,KAAK,aAAa,QAAQ;AAC5B,WAAK,yBAAyB,KAAK,cAAc,KAAK,WAAW,IAAI,CAAC,YAAY,cAAc,CAAC,GAAG,MAAM;AAAA,IAC5G;AACA,SAAK,eAAe,aAAa;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,SAAK,YAAY,MAAM,6BAA6B;AAAA,MAClD,IAAI,KAAK;AAAA,MACT,kBAAkB;AAAA,QAChB,MAAM,KAAK;AAAA,QACX,OAAO,KAAK,oBAAoB,KAAK,KAAK;AAAA,QAC1C,MAAM;AAAA,UACJ,eAAe,UAAU;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,CAAC,EAAE,QAAQ,KAAK,IAAI,UAAQ,KAAK,QAAQ,GAAG,OAAO,sCAAkB,CAAC,EAAE,UAAU,YAAU,KAAK,kBAAkB,KAAK,MAAM,CAAC;AAAA,EACjI;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,YAAY,MAAM,6BAA6B;AAAA,MACzD,IAAI,KAAK;AAAA,MACT,kBAAkB;AAAA,QAChB,MAAM;AAAA,MACR;AAAA,IACF,CAAC,EAAE;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,iBAAiB,GAAM,kBAAuB,WAAW,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,IACzQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,cAAc,iBAAiB,GAAG,CAAC,SAAS,mBAAmB,GAAG,YAAY,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,OAAO,SAAS,GAAG,SAAS,gBAAgB,GAAG,CAAC,MAAM,SAAS,QAAQ,QAAQ,mBAAmB,OAAO,GAAG,CAAC,OAAO,aAAa,GAAG,SAAS,gBAAgB,GAAG,CAAC,MAAM,aAAa,QAAQ,QAAQ,mBAAmB,WAAW,GAAG,CAAC,OAAO,YAAY,GAAG,SAAS,gBAAgB,GAAG,CAAC,MAAM,YAAY,QAAQ,QAAQ,mBAAmB,UAAU,GAAG,CAAC,OAAO,gBAAgB,GAAG,SAAS,gBAAgB,GAAG,CAAC,MAAM,gBAAgB,QAAQ,QAAQ,mBAAmB,cAAc,GAAG,CAAC,OAAO,eAAe,GAAG,SAAS,gBAAgB,GAAG,CAAC,MAAM,eAAe,QAAQ,QAAQ,mBAAmB,aAAa,GAAG,CAAC,OAAO,YAAY,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,iBAAiB,gBAAgB,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,cAAc,mBAAmB,GAAG,WAAW,YAAY,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,mBAAmB,GAAG,YAAY,SAAS,GAAG,kBAAkB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,SAAS,cAAc,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,OAAO,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,SAAS,qBAAqB,GAAG,SAAS,GAAG,kBAAkB,GAAG,CAAC,QAAQ,SAAS,GAAG,aAAa,aAAa,GAAG,SAAS,SAAS,GAAG,CAAC,QAAQ,SAAS,GAAG,aAAa,WAAW,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,gBAAgB,QAAQ,GAAG,OAAO,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,OAAO,YAAY,GAAG,OAAO,GAAG,CAAC,MAAM,YAAY,QAAQ,YAAY,mBAAmB,UAAU,GAAG,CAAC,iBAAiB,gBAAgB,GAAG,OAAO,GAAG,CAAC,cAAc,YAAY,GAAG,gBAAgB,uBAAuB,GAAG,CAAC,GAAG,aAAa,sBAAsB,oBAAoB,qBAAqB,eAAe,gBAAgB,YAAY,uBAAuB,wBAAwB,iBAAiB,GAAG,SAAS,SAAS,GAAG,CAAC,SAAS,0BAA0B,GAAG,SAAS,GAAG,kBAAkB,GAAG,CAAC,GAAG,SAAS,UAAU,GAAG,CAAC,MAAM,uBAAuB,GAAG,sBAAsB,cAAc,SAAS,gBAAgB,cAAc,eAAe,iBAAiB,GAAG,CAAC,MAAM,MAAM,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,QAAQ,GAAG,WAAW,UAAU,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,SAAS,GAAG,SAAS,GAAG,CAAC,MAAM,SAAS,GAAG,SAAS,GAAG,CAAC,MAAM,cAAc,GAAG,SAAS,GAAG,CAAC,MAAM,aAAa,GAAG,SAAS,GAAG,CAAC,GAAG,eAAe,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,WAAW,cAAc,cAAc,YAAY,SAAS,GAAG,CAAC,GAAG,uBAAuB,wBAAwB,iBAAiB,sBAAsB,oBAAoB,qBAAqB,eAAe,gBAAgB,UAAU,GAAG,CAAC,GAAG,OAAO,iBAAiB,QAAQ,GAAG,OAAO,GAAG,CAAC,GAAG,gBAAgB,GAAG,YAAY,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC;AAAA,MACr+F,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,gBAAgB;AAC1D,UAAG,UAAU,GAAG,aAAa;AAC7B,UAAG,eAAe,GAAG,cAAc;AACnC,UAAG,UAAU,GAAG,wBAAwB,CAAC;AACzC,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,UAAU,CAAC;AAC7E,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACxH,UAAG,UAAU,GAAG,gCAAgC,CAAC;AACjD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,wBAAwB,EAAE,IAAI,yBAAyB;AAC7E,UAAG,WAAW,IAAI,8CAA8C,GAAG,GAAG,YAAY,CAAC;AACnF,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,WAAW,IAAI,8CAA8C,GAAG,GAAG,YAAY,CAAC;AACnF,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,eAAe,IAAI,UAAU;AAChC,UAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,wBAAwB,CAAC;AAC3G,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,gBAAgB,EAAE,IAAI,QAAQ,CAAC,EAAE,IAAI,UAAU,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,kBAAkB,CAAC;AAC5G,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,aAAa;AAChB,UAAG,UAAU,IAAI,KAAK;AACtB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,aAAa;AAChB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,kBAAkB,EAAE;AAChG,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa,EAAE;AAClB,UAAG,WAAW,IAAI,8CAA8C,GAAG,GAAG,YAAY,EAAE;AACpF,UAAG,aAAa;AAChB,UAAG,UAAU,IAAI,oCAAoC,EAAE;AACvD,UAAG,WAAW,IAAI,kDAAkD,IAAI,IAAI,gBAAgB,CAAC;AAC7F,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,gBAAM,mBAAsB,YAAY,CAAC;AACzC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,GAAG,IAAI,IAAI,MAAM,CAAC,EAAE,YAAY,gBAAgB;AACrF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC;AACzD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAS,UAAa,YAAY,IAAI,IAAI,IAAI,OAAO,MAAM,OAAO,OAAO,QAAQ,MAAM;AACrG,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC;AACzD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,aAAa,IAAI,WAAW,IAAI,UAAU,CAAC;AACzD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,gBAAgB,CAAC,EAAE,kBAAkB,CAAI,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AACtH,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,qBAAqB,CAAC,EAAE,kBAAkB,CAAI,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AAC3H,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,oBAAoB,CAAC,EAAE,kBAAkB,CAAI,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AAC1H,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,wBAAwB,CAAC,EAAE,kBAAkB,CAAI,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AAC9H,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,uBAAuB,CAAC,EAAE,kBAAkB,CAAI,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AAC7H,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AACxD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,aAAa,MAAM;AAC7C,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,EAAE,cAAc,IAAI,UAAU;AAClE,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAI,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,SAAc,MAAW,eAAoB,sBAA2B,iBAAsB,sBAA2B,oBAAyB,iBAAsB,eAAkB,YAAe,oBAAuB,wBAA2B,yBAA4B,gCAAmC,eAAkB,oBAAuB,2BAA8B,0BAA6B,sBAAyB,wBAA2B,yBAA4B,6BAAgC,oCAAuC,qBAAwB,2BAA8B,qCAAwC,oBAAuB,yBAA4B,2BAA8B,4BAA+B,eAAe,8BAA8B,sBAAsB,0BAA+B,WAAc,eAAkB,mBAAsB,aAAgB,gBAAmB,kBAAkB;AAAA,MACz/B,QAAQ,CAAC,8HAA8H;AAAA,MACvI,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,0FAA0F;AAAA,IACrG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,8BAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAapC,IAAM,+BAAN,MAAM,sCAAqC,yBAAyB;AAAA,EAClE,YAAY,aAAa,aAAa,cAAc,qBAAqB;AACvE,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAC3B,SAAK,eAAe,KAAK,qBAAqB,eAAe;AAC7D,SAAK,aAAa,KAAK,YAAY,MAAM;AAAA,MACvC,MAAM;AAAA,MACN,cAAc,KAAK,YAAY,MAAM,wBAAwB,KAAK,YAAY,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,KAAK;AAAA,EACb;AAAA,EACA,SAAS;AACP,UAAM,YAAY,KAAK,WAAW;AAClC,QAAI,UAAU,MAAM;AAClB,WAAK,YAAY,SAAS,oBAAoB;AAAA,QAC5C,MAAM,UAAU;AAAA,QAChB,cAAc,UAAU;AAAA,QACxB,aAAa,CAAC;AAAA,MAChB,CAAC,EAAE,UAAU,CAAC;AAAA,QACZ;AAAA,MACF,MAAM;AACJ,aAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,UACvE,QAAQ;AAAA,QACV,CAAC;AACD,aAAK,WAAW,eAAe;AAC/B,aAAK,OAAO,SAAS,CAAC,OAAO,oBAAoB,EAAE,GAAG;AAAA,UACpD,YAAY,KAAK;AAAA,QACnB,CAAC;AAAA,MACH,GAAG,SAAO;AACR,aAAK,oBAAoB,MAAM,OAAO,4BAA4B,GAAG;AAAA,UACnE,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,UAAM,YAAY,KAAK,WAAW;AAClC,SAAK,YAAY,SAAS,oBAAoB;AAAA,MAC5C,IAAI,KAAK;AAAA,OACN,UACJ,EAAE,UAAU,MAAM;AACjB,WAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,QACvE,QAAQ;AAAA,MACV,CAAC;AACD,WAAK,WAAW,eAAe;AAAA,IACjC,GAAG,SAAO;AACR,WAAK,oBAAoB,MAAM,OAAO,4BAA4B,GAAG;AAAA,QACnE,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc,QAAQ;AACpB,SAAK,WAAW,WAAW;AAAA,MACzB,MAAM,OAAO;AAAA,IACf,CAAC;AACD,QAAI,KAAK,aAAa,QAAQ;AAC5B,YAAM,oBAAoB,KAAK,WAAW,IAAI,CAAC,cAAc,CAAC;AAC9D,WAAK,yBAAyB,KAAK,cAAc,KAAK,WAAW,IAAI,cAAc,GAAG,MAAM;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,kBAAuB,WAAW,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,IAClO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,2BAA2B,CAAC;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,cAAc,uBAAuB,GAAG,CAAC,SAAS,mBAAmB,GAAG,YAAY,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,OAAO,QAAQ,GAAG,OAAO,GAAG,CAAC,MAAM,QAAQ,QAAQ,QAAQ,mBAAmB,MAAM,GAAG,CAAC,iBAAiB,gBAAgB,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,cAAc,yBAAyB,GAAG,WAAW,YAAY,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,mBAAmB,GAAG,YAAY,SAAS,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,iBAAiB,gBAAgB,GAAG,OAAO,GAAG,CAAC,cAAc,iBAAiB,GAAG,gBAAgB,uBAAuB,CAAC;AAAA,MACjrB,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,gBAAgB;AAC1D,UAAG,UAAU,GAAG,aAAa;AAC7B,UAAG,eAAe,GAAG,cAAc;AACnC,UAAG,UAAU,GAAG,wBAAwB,CAAC;AACzC,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,UAAU,CAAC;AAClF,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC7H,UAAG,UAAU,GAAG,gCAAgC,CAAC;AACjD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,QAAQ,CAAC,EAAE,IAAI,wBAAwB,EAAE,IAAI,yBAAyB;AAC5F,UAAG,WAAW,IAAI,mDAAmD,GAAG,GAAG,YAAY,CAAC;AACxF,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,gBAAgB,EAAE,IAAI,UAAU,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,kBAAkB,CAAC;AAC7F,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,UAAU,IAAI,SAAS,CAAC;AAC3B,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,WAAW,IAAI,mDAAmD,GAAG,GAAG,YAAY,CAAC;AACxF,UAAG,UAAU,IAAI,oCAAoC,CAAC;AACtD,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,kBAAqB,YAAY,CAAC;AACxC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,GAAG,GAAG,IAAI,MAAM,CAAC,EAAE,YAAY,eAAe;AACnF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,aAAa,IAAI,UAAU;AACzC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC;AACzD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,aAAa,CAAC;AAC5D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,aAAa,MAAM;AAC7C,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,EAAE,cAAc,IAAI,UAAU;AAAA,QACpE;AAAA,MACF;AAAA,MACA,cAAc,CAAM,MAAW,eAAoB,sBAA2B,iBAAsB,sBAA2B,oBAAyB,iBAAsB,eAAkB,oBAAuB,wBAA2B,yBAA4B,gCAAmC,oBAAuB,2BAA8B,wBAA2B,yBAA4B,6BAAgC,oCAAuC,oBAAuB,yBAA4B,2BAA8B,4BAA+B,eAAoB,WAAc,aAAa;AAAA,MACnoB,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qCAAN,MAAM,oCAAmC;AAAA,EACvC,YAAY,qBAAqB,aAAa;AAC5C,SAAK,sBAAsB;AAC3B,SAAK,cAAc;AACnB,SAAK,eAAe,KAAK,oBAAoB,mBAAmB,eAAe;AAAA,EACjF;AAAA,EACA,WAAW;AACT,SAAK,OAAO,KAAK,YAAY,MAAM;AAAA,MACjC,MAAM,CAAC,KAAK,MAAM,MAAM,WAAW,QAAQ;AAAA,MAC3C,cAAc,KAAK,YAAY,MAAM,wBAAwB,KAAK,YAAY,CAAC;AAAA,IACjF,CAAC;AACD,QAAI,KAAK,aAAa,QAAQ;AAC5B,YAAM,oBAAoB,KAAK,KAAK,IAAI,cAAc;AACtD,iBAAW,YAAY,KAAK,cAAc;AACxC,cAAM,MAAM,SAAS;AACrB,cAAM,QAAQ,KAAK,MAAM,eAAe,GAAG;AAC3C,cAAM,UAAU,kBAAkB,IAAI,GAAG;AACzC,YAAI,SAAS;AACX,kBAAQ,WAAW,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,YAAY,KAAK,KAAK,KAAK;AAAA,EAClC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,mBAAmB;AACjF,aAAO,KAAK,qBAAqB,qCAAuC,kBAAqB,mBAAmB,GAAM,kBAAuB,kBAAkB,CAAC;AAAA,IAClK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kCAAkC,CAAC;AAAA,MAChD,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,OAAO,QAAQ,GAAG,OAAO,GAAG,CAAC,MAAM,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,GAAG,UAAU,GAAG,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,iBAAiB,cAAc,GAAG,CAAC,cAAc,iBAAiB,GAAG,gBAAgB,uBAAuB,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,eAAe,GAAG,SAAS,UAAU,CAAC;AAAA,MAC5c,UAAU,SAAS,4CAA4C,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,eAAe,CAAC;AAClG,UAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,kBAAkB,CAAC;AACtD,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,UAAG,OAAO,GAAG,eAAe;AAC5B,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,WAAW,CAAC;AAC1F,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,eAAe,CAAC;AAAA,QACpG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,aAAa,IAAI,IAAI;AACnC,UAAG,UAAU;AACb,UAAG,WAAW,SAAY,YAAY,GAAG,GAAG,aAAa,CAAC;AAC1D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,CAAI,YAAY,GAAG,GAAM,gBAAgB,GAAG,GAAG,CAAC,CAAC;AAC3E,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,aAAa,MAAM;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,cAAc,CAAI,UAAe,MAAW,eAAoB,sBAA2B,iBAAsB,sBAA2B,oBAAyB,iBAAsB,eAAkB,oBAAuB,2BAA8B,wBAA2B,sBAAyB,6BAAgC,eAAkB,iBAAiB;AAAA,MACzX,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,iCAAiC,uBAAuB;AAAA,EAC5D,UAAU;AAAA,EACV,oBAAoB,qBAAmB,gBAAgB,SAAS,WAAW,mBAAmB;AAAA,EAC9F,aAAa,UAAQ,KAAK;AAAA,EAC1B,YAAY,CAAC,aAAa,QAAQ,YAAY,SAAS,qBAAqB,GAAG,EAAE,KAAK,IAAI,SAAO,IAAI,oBAAoB,CAAC;AAC5H,CAAC;AACD,IAAM,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAS1B,uBAAuB;AAAA;AAE7B,IAAM,6BAAN,MAAM,oCAAmC,uBAAuB;AAAA,EAC9D,YAAY,aAAa,qBAAqB,cAAc,OAAO,QAAQ;AACzE,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,SAAK,eAAe,KAAK,qBAAqB,eAAe;AAC7D,SAAK,qBAAqB,IAAI,gBAAgB;AAAA,MAC5C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AACD,SAAK,UAAU,KAAK,uBAAuB,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU;AAAA,MACpF,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,aAAa;AAAA,MAC3B,aAAa;AAAA,IACf,CAAC,EAAE,eAAe,KAAK,KAAK;AAC5B,SAAK,QAAQ,KAAK,qBAAqB,EAAE,YAAY,aAAa,MAAM,EAAE,QAAQ;AAAA,MAChF,MAAM;AAAA,IACR,CAAC,EAAE,QAAQ;AAAA,MACT,MAAM;AAAA,IACR,CAAC,EAAE,QAAQ;AAAA,MACT,MAAM;AAAA,IACR,CAAC,EAAE,eAAe,KAAK,KAAK;AAC5B,SAAK,6BAA6B,IAAI,gBAAgB,MAAS;AAC/D,UAAM,UAAU;AAAA,MACd,UAAU;AAAA,MACV,UAAU,UAAQ,KAAK;AAAA,MACvB,cAAc,CAAC,MAAMA,WAAU;AAAA,QAC7B,SAAS;AAAA,UACP;AAAA,UACA,MAAAA;AAAA,UACA,QAAQ;AAAA,YACN,MAAM;AAAA,cACJ,UAAU,KAAK,kBAAkB;AAAA,YACnC;AAAA,aACG,KAAK,QAAQ,kBAAkB;AAAA,UAEpC,MAAM,KAAK,MAAM,gBAAgB;AAAA,QACnC;AAAA,MACF;AAAA,MACA,sBAAsB,CAAC,KAAK,QAAQ,cAAc,KAAK,MAAM,YAAY;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,UAAM,iBAAiB,KAAK,MAAM,SAAS,KAAK,IAAI,QAAM,GAAG,IAAI,UAAU,CAAC,GAAG,qBAAqB,CAAC;AACrG,SAAK,eAAe,KAAK,OAAO,KAAK,IAAI,YAAU,OAAO,WAAW,CAAC,CAAC;AACvE,SAAK,eAAe,cAAc,KAAK,QAAQ,cAAc,EAAE,KAAK,IAAI,CAAC,CAAC,QAAQ,aAAa,MAAM;AACnG,UAAI,eAAe;AACjB,eAAO,OAAO,KAAK,OAAK,EAAE,OAAO,aAAa;AAAA,MAChD;AAAA,IACF,CAAC,CAAC;AACF,SAAK,eAAe,cAAc,KAAK,QAAQ,cAAc,EAAE,KAAK,IAAI,CAAC,CAAC,QAAQ,aAAa,MAAM;AACnG,UAAI,eAAe;AACjB,eAAO,OAAO,UAAU,OAAK,EAAE,OAAO,aAAa;AAAA,MACrD,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC;AACF,UAAM,iBAAiB,cAAc,KAAK,cAAc,KAAK,oBAAoB,KAAK,0BAA0B,EAAE,KAAK,UAAU,CAAC,CAAC,aAAa;AAAA,MAC9I;AAAA,MACA,MAAAA;AAAA,MACA;AAAA,IACF,CAAC,MAAM;AACL,UAAI,aAAa;AACf,eAAO,KAAK,YAAY,SAAS,8BAA8B,YAAY,IAAI;AAAA,UAC7E;AAAA,UACA,MAAAA;AAAA,UACA,QAAQ;AAAA,YACN,cAAc;AAAA,cACZ,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,CAAC,EAAE,UAAU,SAAO,IAAI,eAAe,SAAS;AAAA,MAClD,OAAO;AACL,eAAO,GAAG,MAAS;AAAA,MACrB;AAAA,IACF,CAAC,CAAC;AACF,SAAK,WAAW,eAAe,KAAK,IAAI,SAAO,KAAK,SAAS,CAAC,CAAC,CAAC;AAChE,SAAK,gBAAgB,eAAe,KAAK,IAAI,SAAO,KAAK,cAAc,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,eAAe;AACb,UAAM,SAAS,mBACV,KAAK,MAAM,SAAS;AAEzB,WAAO,OAAO;AACd,SAAK,OAAO,SAAS,CAAC,MAAM,MAAM,GAAG;AAAA,MACnC,YAAY,KAAK;AAAA,MACjB,qBAAqB;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,aAAa,cAAc,mCAAmC;AAAA,MACjE,QAAQ;AAAA,QACN;AAAA,QACA,OAAO,KAAK;AAAA,MACd;AAAA,MACA,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,CAAC,EAAE,KAAK,UAAU,iBAAe,cAAc,KAAK,YAAY,SAAS,oBAAoB,MAAM,IAAI,WAAW,EAAE,KAAK,MAAM,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,UAAU;AAAA,MAC/J,MAAM,YAAU;AACd,aAAK,oBAAoB,QAAQ,OAAO,yCAAyC,GAAG;AAAA,UAClF,eAAe,OAAO;AAAA,UACtB,WAAW,MAAM;AAAA,QACnB,CAAC;AACD,aAAK,2BAA2B,KAAK;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,WAAW,GAAM,kBAAqB,mBAAmB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,cAAc,GAAM,kBAAqB,MAAM,CAAC;AAAA,IAClQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,MACvC,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,cAAc,qBAAqB,GAAG,CAAC,SAAS,mBAAmB,GAAG,cAAc,GAAG,kBAAkB,GAAG,CAAC,GAAG,gBAAgB,gBAAgB,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,qBAAqB,IAAI,GAAG,gBAAgB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,YAAY,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,cAAc,sBAAsB,wBAAwB,MAAM,SAAS,gBAAgB,cAAc,eAAe,WAAW,aAAa,GAAG,CAAC,cAAc,uBAAuB,GAAG,iBAAiB,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,uBAAuB,GAAG,CAAC,MAAM,MAAM,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,mBAAmB,MAAM,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,mBAAmB,MAAM,GAAG,CAAC,MAAM,QAAQ,GAAG,WAAW,YAAY,MAAM,GAAG,CAAC,GAAG,eAAe,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,MAAM,iBAAiB,GAAG,WAAW,UAAU,GAAG,CAAC,GAAG,gBAAgB,GAAG,YAAY,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,GAAG,eAAe,OAAO,GAAG,CAAC,uBAAuB,YAAY,GAAG,gBAAgB,iBAAiB,GAAG,YAAY,GAAG,CAAC,SAAS,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,QAAQ,GAAG,OAAO,GAAG,CAAC,cAAc,+BAA+B,GAAG,qBAAqB,WAAW,SAAS,cAAc,aAAa,CAAC;AAAA,MAC5wC,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,gBAAgB;AAC1D,UAAG,UAAU,GAAG,aAAa;AAC7B,UAAG,eAAe,GAAG,cAAc;AACnC,UAAG,UAAU,GAAG,wBAAwB,CAAC;AACzC,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,KAAK,CAAC;AACtE,UAAG,UAAU,GAAG,gCAAgC,CAAC;AACjD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,gBAAgB,SAAS,6EAA6E;AAClH,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC;AACD,UAAG,WAAW,GAAG,mDAAmD,IAAI,IAAI,eAAe,CAAC,EAAE,IAAI,oDAAoD,GAAG,GAAG,eAAe,CAAC;AAC5K,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,qBAAqB;AACvD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,kBAAqB,YAAY,GAAG,GAAG,IAAI,YAAY,CAAC;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,mBAAmB,UAAa,YAAY,IAAI,GAAG,IAAI,YAAY,MAAM,OAAO,OAAO,QAAQ,IAAI;AAAA,QACnH;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,SAAc,MAAS,YAAe,oBAAuB,wBAA2B,yBAA4B,gCAAmC,wBAA2B,yBAA4B,yBAA4B,qBAAwB,2BAA8B,2BAA8B,qCAAwC,oBAAuB,wBAA2B,yBAA4B,oBAAoB,kCAAuC,WAAc,eAAkB,cAAc;AAAA,MACtkB,QAAQ,CAAC,kDAAkD;AAAA,MAC3D,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,iCAAiC;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,uCAAuC;AAAA,EAC3C,UAAU;AAAA,EACV,OAAO,OAAO,4BAA4B;AAAA,EAC1C,MAAM;AAAA,EACN,WAAW;AAAA,EACX,oBAAoB,WAAW;AAAA,EAC/B,SAAS,CAAC;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,eAAe,SAAS,IAAI,YAAY;AAC9C,UAAM,cAAc,SAAS,IAAI,WAAW;AAC5C,UAAM,sBAAsB,SAAS,IAAI,mBAAmB;AAC5D,UAAM,QAAQ,cAAc;AAC5B,UAAM,cAAc,UAAU,IAAI,OAAK,EAAE,EAAE;AAC3C,gBAAY,SAAS,yBAAyB,MAAM,IAAI,WAAW,EAAE,UAAU;AAAA,MAC7E,UAAU,MAAM;AACd,4BAAoB,QAAQ,OAAO,8CAA8C,GAAG;AAAA,UAClF,eAAe,YAAY;AAAA,UAC3B,WAAW,MAAM;AAAA,QACnB,CAAC;AACD,uBAAe;AACf,sBAAc,QAAQ;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,4BAA4B,uBAAuB;AAAA,EACvD,UAAU;AAAA,EACV,oBAAoB,qBAAmB,gBAAgB,SAAS,WAAW,cAAc;AAAA,EACzF,aAAa,UAAQ,KAAK,YAAY,MAAM,KAAK;AAAA,EACjD,YAAY,CAAC,aAAa,QAAQ,YAAY,SAAS,gBAAgB,GAAG,EAAE,KAAK,IAAI,SAAO,IAAI,eAAe,CAAC;AAClH,CAAC;AACD,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwB5B,IAAM,wBAAN,MAAM,+BAA8B,uBAAuB;AAAA,EACzD,cAAc;AACZ,UAAM;AACN,SAAK,kBAAkB;AACvB,SAAK,eAAe,KAAK,qBAAqB,UAAU;AACxD,SAAK,UAAU,KAAK,uBAAuB,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU;AAAA,MACpF,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,qBAAqB;AAAA,MACnC,aAAa;AAAA,IACf,CAAC,EAAE,UAAU;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,oBAAoB;AAAA,MAClC,aAAa;AAAA,IACf,CAAC,EAAE,UAAU;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,wBAAwB;AAAA,MACtC,aAAa;AAAA,IACf,CAAC,EAAE,sBAAsB,KAAK,YAAY,EAAE,eAAe,KAAK,KAAK;AACrE,SAAK,QAAQ,KAAK,qBAAqB,EAAE,YAAY,aAAa,MAAM,EAAE,QAAQ;AAAA,MAChF,MAAM;AAAA,IACR,CAAC,EAAE,QAAQ;AAAA,MACT,MAAM;AAAA,IACR,CAAC,EAAE,QAAQ;AAAA,MACT,MAAM;AAAA,IACR,CAAC,EAAE,QAAQ;AAAA,MACT,MAAM;AAAA,IACR,CAAC,EAAE,oBAAoB,KAAK,YAAY,EAAE,eAAe,KAAK,KAAK;AACnE,SAAK,UAAU;AAAA,MACb,UAAU;AAAA,MACV,UAAU,UAAQ,KAAK;AAAA,MACvB,cAAc,CAAC,MAAMA,WAAU;AAAA,QAC7B,SAAS;AAAA,UACP;AAAA,UACA,MAAAA;AAAA,UACA,QAAQ,kCACF,KAAK,kBAAkB,QAAQ;AAAA,YACjC,cAAc;AAAA,cACZ,UAAU,KAAK,kBAAkB;AAAA,YACnC;AAAA,YACA,UAAU;AAAA,cACR,UAAU,KAAK,kBAAkB;AAAA,YACnC;AAAA,YACA,YAAY;AAAA,cACV,UAAU,KAAK,kBAAkB;AAAA,YACnC;AAAA,UACF,IAAI,CAAC,IACF,KAAK,QAAQ,kBAAkB;AAAA,UAEpC,gBAAgB,KAAK,kBAAkB,QAAQ,gBAAgB,KAAK,gBAAgB;AAAA,UACpF,MAAM,KAAK,MAAM,gBAAgB;AAAA,QACnC;AAAA,MACF;AAAA,MACA,sBAAsB,CAAC,KAAK,MAAM,cAAc,KAAK,QAAQ,YAAY;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,cAAc,eAAe,GAAG,CAAC,SAAS,mBAAmB,GAAG,cAAc,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,GAAG,cAAc,sBAAsB,wBAAwB,MAAM,SAAS,gBAAgB,cAAc,eAAe,SAAS,GAAG,CAAC,cAAc,iBAAiB,GAAG,iBAAiB,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,uBAAuB,GAAG,CAAC,MAAM,MAAM,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,mBAAmB,MAAM,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,mBAAmB,MAAM,GAAG,CAAC,MAAM,QAAQ,GAAG,WAAW,YAAY,MAAM,GAAG,CAAC,MAAM,UAAU,GAAG,SAAS,GAAG,CAAC,MAAM,iBAAiB,GAAG,WAAW,MAAM,GAAG,CAAC,GAAG,eAAe,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,YAAY,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,YAAY,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,eAAe,OAAO,CAAC;AAAA,MAC54B,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,gBAAgB;AAC1D,UAAG,UAAU,GAAG,aAAa;AAC7B,UAAG,eAAe,GAAG,cAAc;AACnC,UAAG,UAAU,GAAG,wBAAwB,CAAC;AACzC,UAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,KAAK,CAAC;AACjE,UAAG,UAAU,GAAG,gCAAgC,CAAC;AACjD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,WAAW,cAAc,SAAS,sEAAsE,QAAQ;AACjH,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC,EAAE,sBAAsB,SAAS,8EAA8E,QAAQ;AACtH,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC,EAAE,wBAAwB,SAAS,gFAAgF,QAAQ;AAC1H,mBAAO,IAAI,kBAAkB,MAAM;AAAA,UACrC,CAAC;AACD,UAAG,UAAU,IAAI,wBAAwB,CAAC,EAAE,IAAI,kBAAkB,CAAC;AACnE,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,aAAa;AACpF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,aAAa;AACpF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,aAAa;AACpF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,aAAa;AACpF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,aAAa;AACpF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,aAAa;AACpF,UAAG,aAAa;AAChB,UAAG,WAAW,IAAI,+DAA+D,GAAG,GAAG,+BAA+B,EAAE;AACxH,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,gBAAgB;AAClD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,MAAM,IAAI,eAAe,EAAE,SAAY,YAAY,GAAG,IAAI,IAAI,MAAM,CAAC,EAAE,gBAAmB,YAAY,GAAG,IAAI,IAAI,aAAa,CAAC,EAAE,cAAiB,YAAY,IAAI,IAAI,IAAI,WAAW,CAAC,EAAE,eAAkB,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,EAAE,WAAW,IAAI,OAAO;AACrR,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,iBAAiB,GAAG,EAAE,oBAAoB,IAAI,gBAAgB;AAC5E,UAAG,UAAU;AACb,UAAG,WAAW,qBAAqB,IAAI,iBAAiB,EAAE,yBAA4B,YAAY,IAAI,IAAI,0DAA0D,CAAC;AACrK,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,WAAW,CAAC,EAAE,mBAAmB,IAAI;AACrF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI,EAAE,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC;AACjI,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI,EAAE,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC;AACjI,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,eAAe,CAAC,EAAE,YAAY,KAAK,EAAE,QAAQ,IAAI,MAAM,IAAI,UAAU,CAAC;AACtH,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,eAAe,CAAC;AAChE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,wBAAwB,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;AAChH,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAW,IAAI,YAAY;AAAA,QAC3C;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,SAAY,YAAe,oBAAuB,wBAA2B,yBAA4B,gCAAmC,wBAA2B,yBAA4B,yBAA4B,qBAAwB,2BAA8B,2BAA8B,qCAAwC,oBAAoB,8BAAmC,WAAc,eAAkB,cAAc;AAAA,MACze,QAAQ,CAAC,kEAAkE;AAAA,IAC7E,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,iDAAiD;AAAA,IAC5D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,eAAe,iBAAe,CAAC;AAAA,EACnC,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY,OAAO,sBAAsB;AAAA,EAC3C;AAAA,EACA,UAAU,YAAY,iBAAiB,eAAe;AACxD,GAAG;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY;AAAA,MACV,OAAO,OAAO,sBAAsB;AAAA,MACpC,MAAM,CAAC,OAAO,WAAW;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,UAAU,YAAY,iBAAiB,iBAAiB;AAC1D,GAAG;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY,OAAO,4BAA4B;AAAA,EACjD;AAAA,EACA,UAAU,YAAY,iBAAiB,qBAAqB;AAC9D,GAAG;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY;AAAA,MACV,OAAO,OAAO,4BAA4B;AAAA,MAC1C,MAAM,CAAC,OAAO,QAAQ;AAAA,IACxB;AAAA,EACF;AAAA,EACA,UAAU,YAAY,iBAAiB,uBAAuB;AAChE,CAAC;AACD,SAAS,mBAAmB,MAAM,QAAQ;AACxC,SAAO,iBAAiB;AAAA,IACtB,QAAQ,KAAK;AAAA,IACb,IAAI,OAAO;AAAA,IACX,eAAe;AAAA,IACf,SAAS,cAAY,GAAG,SAAS,SAAS,IAAI,SAAS,QAAQ;AAAA,IAC/D,OAAO;AAAA,EACT,CAAC;AACH;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,kCAAkC;AAAA,EACzC;AAAA,EACA,YAAY,2BAA2B,aAAa;AAClD,QAAI,gBAAe,iCAAiC;AAClD;AAAA,IACF;AACA,8BAA0B,mBAAmB,yBAAyB;AACtE,8BAA0B,mBAAmB,8BAA8B;AAC3E,8BAA0B,mBAAmB,oCAAoC;AACjF,gBAAY,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK,OAAO,oBAAoB;AAAA,MAChC,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC;AACD,gBAAY,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK,OAAO,mBAAmB;AAAA,MAC/B,OAAO;AAAA,MACP,WAAW,4BAA4B;AAAA,QACrC,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,UACT,kBAAkB;AAAA,YAChB,MAAM;AAAA,cACJ,eAAe,UAAU;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,QACA,gBAAgB,YAAU,CAAC;AAAA,UACzB,OAAO,SAAS,GAAG,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,OAAO,8BAA8B;AAAA,UAClG,MAAM,CAAC,QAAQ,EAAE;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,gBAAY,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK,OAAO,0BAA0B;AAAA,MACtC,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC;AACD,gBAAY,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK,OAAO,yBAAyB;AAAA,MACrC,OAAO;AAAA,MACP,WAAW,4BAA4B;AAAA,QACrC,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,gBAAgB,YAAU,CAAC;AAAA,UACzB,OAAO,SAAS,OAAO,OAAO,OAAO,oCAAoC;AAAA,UACzE,MAAM,CAAC,QAAQ,EAAE;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,oBAAe,kCAAkC;AAAA,EACnD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,SAAY,yBAAyB,GAAM,SAAY,WAAW,CAAC;AAAA,IACzH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,uBAAuB,yBAAyB,8BAA8B,sBAAsB,4BAA4B,oCAAoC,mCAAmC,kCAAkC,oCAAoC,0BAA0B,8BAA8B,mCAAmC,4BAA4B;AAAA,MACnZ,SAAS,CAAC,cAAiB,YAAY;AAAA,MACvC,SAAS,CAAC,oBAAoB;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY,iBAAe,aAAa,WAAW;AAAA,QACnD,OAAO;AAAA,QACP,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,MACD,SAAS,CAAC,cAAc,aAAa,SAAS,CAAC,CAAC,CAAC;AAAA,IACnD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,aAAa,SAAS,CAAC,CAAC,CAAC;AAAA,MACjD,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY,iBAAe,aAAa,WAAW;AAAA,QACnD,OAAO;AAAA,QACP,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,MACD,cAAc,CAAC,uBAAuB,yBAAyB,8BAA8B,sBAAsB,4BAA4B,oCAAoC,mCAAmC,kCAAkC,oCAAoC,0BAA0B,8BAA8B,mCAAmC,4BAA4B;AAAA,MACnZ,SAAS,CAAC,oBAAoB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": ["take"]}