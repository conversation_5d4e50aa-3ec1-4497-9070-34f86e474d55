{"version": 3, "sources": ["../../../../../../../node_modules/@vendure/admin-ui/fesm2022/vendure-admin-ui-marketing.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { marker } from '@biesbjerg/ngx-translate-extract-marker';\nimport * as i1 from '@vendure/admin-ui/core';\nimport { PROMOTION_FRAGMENT, TypedBaseDetailComponent, getCustomFieldsDefaults, createUpdatedTranslatable, findTranslation, encodeConfigArgValue, getConfigArgValue, getDefaultConfigArgValue, createBulkDeleteAction, Permission, createBulkAssignToChannelAction, AssignPromotionsToChannelDocument, createBulkRemoveFromChannelAction, RemovePromotionsFromChannelDocument, ModalService, DuplicateEntityDialogComponent, TypedBaseListComponent, GetPromotionListDocument, LogicalOperator, PageComponent, detailBreadcrumb, detailComponentWithResolver, GetPromotionDetailDocument, SharedModule, PageService } from '@vendure/admin-ui/core';\nimport { gql } from 'apollo-angular';\nimport { combineLatest } from 'rxjs';\nimport { take, mergeMap, map } from 'rxjs/operators';\nimport * as i3 from '@clr/angular';\nimport * as i4 from '@angular/common';\nimport { AsyncPipe } from '@angular/common';\nimport * as i5 from '@ngx-translate/core';\nimport * as i3$1 from '@angular/router';\nimport { RouterModule, ROUTES } from '@angular/router';\nfunction PromotionDetailComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PromotionDetailComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.create());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.saveButtonEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"common.create\"), \" \");\n  }\n}\nfunction PromotionDetailComponent_ng_template_12_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PromotionDetailComponent_ng_template_12_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.saveButtonEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"common.update\"), \" \");\n  }\n}\nfunction PromotionDetailComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PromotionDetailComponent_ng_template_12_button_0_Template, 3, 4, \"button\", 28);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdatePromotion\");\n  }\n}\nfunction PromotionDetailComponent_vdr_card_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\")(1, \"clr-toggle-wrapper\");\n    i0.ɵɵelement(2, \"input\", 29);\n    i0.ɵɵelementStart(3, \"label\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, \"common.enabled\"));\n  }\n}\nfunction PromotionDetailComponent_vdr_card_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\");\n    i0.ɵɵelement(1, \"vdr-page-entity-info\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const entity_r5 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"entity\", entity_r5);\n  }\n}\nfunction PromotionDetailComponent_vdr_card_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-card\", 31);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelement(2, \"vdr-tabbed-custom-fields\", 32);\n    i0.ɵɵpipe(3, \"hasPermission\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(1, 4, \"common.custom-fields\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"customFields\", ctx_r2.customFields)(\"customFieldsFormGroup\", ctx_r2.detailForm.get(\"customFields\"))(\"readonly\", !i0.ɵɵpipeBind1(3, 6, \"UpdatePromotion\"));\n  }\n}\nfunction PromotionDetailComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"vdr-configurable-input\", 33);\n    i0.ɵɵpipe(2, \"hasPermission\");\n    i0.ɵɵlistener(\"remove\", function PromotionDetailComponent_div_55_Template_vdr_configurable_input_remove_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeCondition($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const condition_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"position\", i_r8)(\"readonly\", !i0.ɵɵpipeBind1(2, 5, \"UpdatePromotion\"))(\"operation\", condition_r7)(\"operationDefinition\", ctx_r2.getConditionDefinition(condition_r7))(\"formControlName\", i_r8);\n  }\n}\nfunction PromotionDetailComponent_vdr_dropdown_57_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PromotionDetailComponent_vdr_dropdown_57_button_6_Template_button_click_0_listener() {\n      const condition_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addCondition(condition_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const condition_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", condition_r10.description, \" \");\n  }\n}\nfunction PromotionDetailComponent_vdr_dropdown_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 34);\n    i0.ɵɵelement(2, \"clr-icon\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"vdr-dropdown-menu\", 36);\n    i0.ɵɵtemplate(6, PromotionDetailComponent_vdr_dropdown_57_button_6_Template, 2, 1, \"button\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"marketing.add-condition\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getAvailableConditions());\n  }\n}\nfunction PromotionDetailComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"vdr-configurable-input\", 39);\n    i0.ɵɵpipe(2, \"hasPermission\");\n    i0.ɵɵlistener(\"remove\", function PromotionDetailComponent_div_60_Template_vdr_configurable_input_remove_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeAction($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r12 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"position\", i_r13)(\"operation\", action_r12)(\"readonly\", !i0.ɵɵpipeBind1(2, 5, \"UpdatePromotion\"))(\"operationDefinition\", ctx_r2.getActionDefinition(action_r12))(\"formControlName\", i_r13);\n  }\n}\nfunction PromotionDetailComponent_vdr_dropdown_62_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PromotionDetailComponent_vdr_dropdown_62_button_6_Template_button_click_0_listener() {\n      const action_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addAction(action_r15));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", action_r15.description, \" \");\n  }\n}\nfunction PromotionDetailComponent_vdr_dropdown_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 34);\n    i0.ɵɵelement(2, \"clr-icon\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"vdr-dropdown-menu\", 36);\n    i0.ɵɵtemplate(6, PromotionDetailComponent_vdr_dropdown_62_button_6_Template, 2, 1, \"button\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"marketing.add-action\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getAvailableActions());\n  }\n}\nconst _c0 = () => [\"./create\"];\nconst _c1 = a0 => [\"./\", a0];\nfunction PromotionListComponent_a_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 17);\n    i0.ɵɵelement(1, \"clr-icon\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"marketing.create-new-promotion\"), \" \");\n  }\n}\nfunction PromotionListComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const promotion_r1 = ctx.item;\n    i0.ɵɵtextInterpolate(promotion_r1.id);\n  }\n}\nfunction PromotionListComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const promotion_r2 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, promotion_r2.createdAt, \"short\"), \" \");\n  }\n}\nfunction PromotionListComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const promotion_r3 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, promotion_r3.updatedAt, \"short\"), \" \");\n  }\n}\nfunction PromotionListComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 19)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"clr-icon\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const promotion_r4 = ctx.item;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c1, promotion_r4.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", promotion_r4.name, \"\");\n  }\n}\nfunction PromotionListComponent_ng_template_32_vdr_chip_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"common.enabled\"));\n  }\n}\nfunction PromotionListComponent_ng_template_32_vdr_chip_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"common.disabled\"));\n  }\n}\nfunction PromotionListComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PromotionListComponent_ng_template_32_vdr_chip_0_Template, 3, 3, \"vdr-chip\", 21)(1, PromotionListComponent_ng_template_32_vdr_chip_1_Template, 3, 3, \"vdr-chip\", 22);\n  }\n  if (rf & 2) {\n    const promotion_r5 = ctx.item;\n    i0.ɵɵproperty(\"ngIf\", promotion_r5.enabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !promotion_r5.enabled);\n  }\n}\nfunction PromotionListComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const promotion_r6 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", promotion_r6.couponCode, \" \");\n  }\n}\nfunction PromotionListComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const promotion_r7 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, promotion_r7.startsAt, \"short\"), \" \");\n  }\n}\nfunction PromotionListComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const promotion_r8 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, promotion_r8.endsAt, \"short\"), \" \");\n  }\n}\nfunction PromotionListComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const promotion_r9 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", promotion_r9.perCustomerUsageLimit, \" \");\n  }\n}\nfunction PromotionListComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const promotion_r10 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", promotion_r10.usageLimit, \" \");\n  }\n}\nfunction PromotionListComponent_vdr_dt2_custom_field_column_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-dt2-custom-field-column\", 25);\n  }\n  if (rf & 2) {\n    const customField_r11 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"customField\", customField_r11)(\"sorts\", ctx_r11.sorts);\n  }\n}\nconst GET_PROMOTION_DETAIL = gql`\n    query GetPromotionDetail($id: ID!) {\n        promotion(id: $id) {\n            ...Promotion\n        }\n    }\n    ${PROMOTION_FRAGMENT}\n`;\nclass PromotionDetailComponent extends TypedBaseDetailComponent {\n  constructor(changeDetector, dataService, formBuilder, notificationService) {\n    super();\n    this.changeDetector = changeDetector;\n    this.dataService = dataService;\n    this.formBuilder = formBuilder;\n    this.notificationService = notificationService;\n    this.customFields = this.getCustomFieldConfig('Promotion');\n    this.detailForm = this.formBuilder.group({\n      name: ['', Validators.required],\n      description: '',\n      enabled: true,\n      couponCode: null,\n      perCustomerUsageLimit: null,\n      usageLimit: null,\n      startsAt: null,\n      endsAt: null,\n      conditions: this.formBuilder.array([]),\n      actions: this.formBuilder.array([]),\n      customFields: this.formBuilder.group(getCustomFieldsDefaults(this.customFields))\n    });\n    this.conditions = [];\n    this.actions = [];\n    this.allConditions = [];\n    this.allActions = [];\n    this.customFields = this.getCustomFieldConfig('Promotion');\n  }\n  ngOnInit() {\n    this.init();\n    this.dataService.promotion.getPromotionActionsAndConditions().single$.subscribe(data => {\n      this.allActions = data.promotionActions;\n      this.allConditions = data.promotionConditions;\n      this.changeDetector.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy();\n  }\n  getAvailableConditions() {\n    return this.allConditions.filter(o => !this.conditions.find(c => c.code === o.code));\n  }\n  getConditionDefinition(condition) {\n    return this.allConditions.find(c => c.code === condition.code);\n  }\n  getAvailableActions() {\n    return this.allActions.filter(o => !this.actions.find(a => a.code === o.code));\n  }\n  getActionDefinition(action) {\n    return this.allActions.find(c => c.code === action.code);\n  }\n  saveButtonEnabled() {\n    return !!(this.detailForm.dirty && this.detailForm.valid && (this.conditions.length !== 0 || this.detailForm.value.couponCode) && this.actions.length !== 0);\n  }\n  addCondition(condition) {\n    this.addOperation('conditions', condition);\n    this.detailForm.markAsDirty();\n  }\n  addAction(action) {\n    this.addOperation('actions', action);\n    this.detailForm.markAsDirty();\n  }\n  removeCondition(condition) {\n    this.removeOperation('conditions', condition);\n    this.detailForm.markAsDirty();\n  }\n  removeAction(action) {\n    this.removeOperation('actions', action);\n    this.detailForm.markAsDirty();\n  }\n  formArrayOf(key) {\n    return this.detailForm.get(key);\n  }\n  create() {\n    if (!this.detailForm.dirty) {\n      return;\n    }\n    const input = this.getUpdatedPromotion({\n      id: '',\n      createdAt: '',\n      updatedAt: '',\n      startsAt: '',\n      endsAt: '',\n      name: '',\n      description: '',\n      couponCode: null,\n      perCustomerUsageLimit: null,\n      usageLimit: null,\n      enabled: false,\n      conditions: [],\n      actions: [],\n      translations: []\n    }, this.detailForm, this.languageCode);\n    this.dataService.promotion.createPromotion(input).subscribe(({\n      createPromotion\n    }) => {\n      switch (createPromotion.__typename) {\n        case 'Promotion':\n          this.notificationService.success(marker('common.notify-create-success'), {\n            entity: 'Promotion'\n          });\n          this.detailForm.markAsPristine();\n          this.changeDetector.markForCheck();\n          this.router.navigate(['../', createPromotion.id], {\n            relativeTo: this.route\n          });\n          break;\n        case 'MissingConditionsError':\n          this.notificationService.error(createPromotion.message);\n          break;\n      }\n    }, err => {\n      this.notificationService.error(marker('common.notify-create-error'), {\n        entity: 'Promotion'\n      });\n    });\n  }\n  save() {\n    if (!this.detailForm.dirty) {\n      return;\n    }\n    combineLatest(this.entity$, this.languageCode$).pipe(take(1), mergeMap(([paymentMethod, languageCode]) => {\n      const input = this.getUpdatedPromotion(paymentMethod, this.detailForm, languageCode);\n      return this.dataService.promotion.updatePromotion(input);\n    })).subscribe(data => {\n      this.notificationService.success(marker('common.notify-update-success'), {\n        entity: 'Promotion'\n      });\n      this.detailForm.markAsPristine();\n      this.changeDetector.markForCheck();\n    }, err => {\n      this.notificationService.error(marker('common.notify-update-error'), {\n        entity: 'Promotion'\n      });\n    });\n  }\n  /**\n   * Given a PaymentMethod and the value of the detailForm, this method creates an updated copy of it which\n   * can then be persisted to the API.\n   */\n  getUpdatedPromotion(promotion, formGroup, languageCode) {\n    const formValue = formGroup.value;\n    const input = createUpdatedTranslatable({\n      translatable: promotion,\n      updatedFields: formValue,\n      customFieldConfig: this.customFields,\n      languageCode,\n      defaultTranslation: {\n        languageCode,\n        name: promotion.name || '',\n        description: promotion.description || ''\n      }\n    });\n    return {\n      ...input,\n      conditions: this.mapOperationsToInputs(this.conditions, formValue.conditions),\n      actions: this.mapOperationsToInputs(this.actions, formValue.actions)\n    };\n  }\n  /**\n   * Update the form values when the entity changes.\n   */\n  setFormValues(entity, languageCode) {\n    const currentTranslation = findTranslation(entity, languageCode);\n    this.detailForm.patchValue({\n      name: currentTranslation?.name,\n      description: currentTranslation?.description,\n      enabled: entity.enabled,\n      couponCode: entity.couponCode,\n      perCustomerUsageLimit: entity.perCustomerUsageLimit,\n      usageLimit: entity.usageLimit,\n      startsAt: entity.startsAt,\n      endsAt: entity.endsAt\n    });\n    entity.conditions.forEach(o => {\n      this.addOperation('conditions', o);\n    });\n    entity.actions.forEach(o => this.addOperation('actions', o));\n    if (this.customFields.length) {\n      this.setCustomFieldFormValues(this.customFields, this.detailForm.get('customFields'), entity, currentTranslation);\n    }\n  }\n  /**\n   * Maps an array of conditions or actions to the input format expected by the GraphQL API.\n   */\n  mapOperationsToInputs(operations, formValueOperations) {\n    return operations.map((o, i) => ({\n      code: o.code,\n      arguments: Object.values(formValueOperations[i].args).map((value, j) => ({\n        name: o.args[j].name,\n        value: encodeConfigArgValue(value)\n      }))\n    }));\n  }\n  /**\n   * Adds a new condition or action to the promotion.\n   */\n  addOperation(key, operation) {\n    const operationsArray = this.formArrayOf(key);\n    const collection = key === 'conditions' ? this.conditions : this.actions;\n    const index = operationsArray.value.findIndex(o => o.code === operation.code);\n    if (index === -1) {\n      const argsHash = operation.args.reduce((output, arg) => ({\n        ...output,\n        [arg.name]: getConfigArgValue(arg.value) ?? this.getDefaultArgValue(key, operation, arg.name)\n      }), {});\n      operationsArray.push(this.formBuilder.control({\n        code: operation.code,\n        args: argsHash\n      }));\n      collection.push({\n        code: operation.code,\n        args: operation.args.map(a => ({\n          name: a.name,\n          value: getConfigArgValue(a.value)\n        }))\n      });\n    }\n  }\n  getDefaultArgValue(key, operation, argName) {\n    const def = key === 'conditions' ? this.allConditions.find(c => c.code === operation.code) : this.allActions.find(a => a.code === operation.code);\n    if (def) {\n      const argDef = def.args.find(a => a.name === argName);\n      if (argDef) {\n        return getDefaultConfigArgValue(argDef);\n      }\n    }\n    throw new Error(`Could not determine default value for \"argName\"`);\n  }\n  /**\n   * Removes a condition or action from the promotion.\n   */\n  removeOperation(key, operation) {\n    const operationsArray = this.formArrayOf(key);\n    const collection = key === 'conditions' ? this.conditions : this.actions;\n    const index = operationsArray.value.findIndex(o => o.code === operation.code);\n    if (index !== -1) {\n      operationsArray.removeAt(index);\n      collection.splice(index, 1);\n    }\n  }\n  static {\n    this.ɵfac = function PromotionDetailComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PromotionDetailComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.DataService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PromotionDetailComponent,\n      selectors: [[\"vdr-promotion-detail\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 63,\n      vars: 73,\n      consts: [[\"updateButton\", \"\"], [1, \"flex\", \"clr-align-items-center\"], [3, \"languageCodeChange\", \"disabled\", \"availableLanguageCodes\", \"currentLanguageCode\"], [\"locationId\", \"promotion-detail\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\", \"ngIfElse\"], [1, \"form\", 3, \"formGroup\"], [4, \"vdrIfPermissions\"], [4, \"ngIf\"], [1, \"form-grid\"], [\"for\", \"name\", 3, \"label\"], [\"id\", \"name\", \"type\", \"text\", \"formControlName\", \"name\", 3, \"readonly\"], [\"formControlName\", \"description\", 1, \"form-grid-span\", 3, \"readonly\", \"label\"], [\"for\", \"startsAt\", 3, \"label\"], [\"formControlName\", \"startsAt\"], [\"for\", \"endsAt\", 3, \"label\"], [\"formControlName\", \"endsAt\"], [\"for\", \"couponCode\", 3, \"label\"], [\"id\", \"couponCode\", \"type\", \"text\", \"formControlName\", \"couponCode\", 3, \"readonly\"], [\"for\", \"perCustomerUsageLimit\", 3, \"label\", \"tooltip\"], [\"id\", \"perCustomerUsageLimit\", \"type\", \"number\", \"min\", \"1\", \"max\", \"999\", \"formControlName\", \"perCustomerUsageLimit\", 3, \"readonly\"], [\"for\", \"usageLimit\", 3, \"label\", \"tooltip\"], [\"id\", \"usageLimit\", \"type\", \"number\", \"min\", \"1\", \"max\", \"9999999\", \"formControlName\", \"usageLimit\", 3, \"readonly\"], [\"formGroupName\", \"customFields\", 3, \"title\", 4, \"ngIf\"], [\"locationId\", \"promotion-detail\", 3, \"entity$\", \"detailForm\"], [\"formArrayName\", \"conditions\", 3, \"title\"], [4, \"ngFor\", \"ngForOf\"], [\"formArrayName\", \"actions\", 3, \"title\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"vdrIfPermissions\"], [\"type\", \"checkbox\", \"clrToggle\", \"\", \"name\", \"enabled\", \"formControlName\", \"enabled\"], [3, \"entity\"], [\"formGroupName\", \"customFields\", 3, \"title\"], [\"entityName\", \"Promotion\", 3, \"customFields\", \"customFieldsFormGroup\", \"readonly\"], [3, \"remove\", \"position\", \"readonly\", \"operation\", \"operationDefinition\", \"formControlName\"], [\"vdrDropdownTrigger\", \"\", 1, \"btn\", \"btn-outline\"], [\"shape\", \"plus\"], [\"vdrPosition\", \"bottom-left\"], [\"type\", \"button\", \"vdrDropdownItem\", \"\", \"class\", \"item-wrap\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"vdrDropdownItem\", \"\", 1, \"item-wrap\", 3, \"click\"], [3, \"remove\", \"position\", \"operation\", \"readonly\", \"operationDefinition\", \"formControlName\"]],\n      template: function PromotionDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"vdr-page-block\")(1, \"vdr-action-bar\")(2, \"vdr-ab-left\")(3, \"div\", 1)(4, \"vdr-language-selector\", 2);\n          i0.ɵɵpipe(5, \"async\");\n          i0.ɵɵpipe(6, \"async\");\n          i0.ɵɵpipe(7, \"async\");\n          i0.ɵɵlistener(\"languageCodeChange\", function PromotionDetailComponent_Template_vdr_language_selector_languageCodeChange_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setLanguage($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"vdr-ab-right\");\n          i0.ɵɵelement(9, \"vdr-action-bar-items\", 3);\n          i0.ɵɵtemplate(10, PromotionDetailComponent_button_10_Template, 3, 4, \"button\", 4);\n          i0.ɵɵpipe(11, \"async\");\n          i0.ɵɵtemplate(12, PromotionDetailComponent_ng_template_12_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelement(14, \"vdr-action-bar-dropdown-menu\", 3);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"form\", 5)(16, \"vdr-page-detail-layout\")(17, \"vdr-page-detail-sidebar\");\n          i0.ɵɵtemplate(18, PromotionDetailComponent_vdr_card_18_Template, 6, 3, \"vdr-card\", 6)(19, PromotionDetailComponent_vdr_card_19_Template, 2, 1, \"vdr-card\", 7);\n          i0.ɵɵpipe(20, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"vdr-page-block\")(22, \"vdr-card\")(23, \"div\", 8)(24, \"vdr-form-field\", 9);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵelement(26, \"input\", 10);\n          i0.ɵɵpipe(27, \"hasPermission\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"vdr-rich-text-editor\", 11);\n          i0.ɵɵpipe(29, \"hasPermission\");\n          i0.ɵɵpipe(30, \"translate\");\n          i0.ɵɵelementStart(31, \"vdr-form-field\", 12);\n          i0.ɵɵpipe(32, \"translate\");\n          i0.ɵɵelement(33, \"vdr-datetime-picker\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"vdr-form-field\", 14);\n          i0.ɵɵpipe(35, \"translate\");\n          i0.ɵɵelement(36, \"vdr-datetime-picker\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"vdr-form-field\", 16);\n          i0.ɵɵpipe(38, \"translate\");\n          i0.ɵɵelement(39, \"input\", 17);\n          i0.ɵɵpipe(40, \"hasPermission\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"vdr-form-field\", 18);\n          i0.ɵɵpipe(42, \"translate\");\n          i0.ɵɵpipe(43, \"translate\");\n          i0.ɵɵelement(44, \"input\", 19);\n          i0.ɵɵpipe(45, \"hasPermission\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"vdr-form-field\", 20);\n          i0.ɵɵpipe(47, \"translate\");\n          i0.ɵɵpipe(48, \"translate\");\n          i0.ɵɵelement(49, \"input\", 21);\n          i0.ɵɵpipe(50, \"hasPermission\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(51, PromotionDetailComponent_vdr_card_51_Template, 4, 8, \"vdr-card\", 22);\n          i0.ɵɵelement(52, \"vdr-custom-detail-component-host\", 23);\n          i0.ɵɵelementStart(53, \"vdr-card\", 24);\n          i0.ɵɵpipe(54, \"translate\");\n          i0.ɵɵtemplate(55, PromotionDetailComponent_div_55_Template, 3, 7, \"div\", 25);\n          i0.ɵɵelementStart(56, \"div\");\n          i0.ɵɵtemplate(57, PromotionDetailComponent_vdr_dropdown_57_Template, 7, 4, \"vdr-dropdown\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"vdr-card\", 26);\n          i0.ɵɵpipe(59, \"translate\");\n          i0.ɵɵtemplate(60, PromotionDetailComponent_div_60_Template, 3, 7, \"div\", 25);\n          i0.ɵɵelementStart(61, \"div\");\n          i0.ɵɵtemplate(62, PromotionDetailComponent_vdr_dropdown_62_Template, 7, 4, \"vdr-dropdown\", 6);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const updateButton_r16 = i0.ɵɵreference(13);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", i0.ɵɵpipeBind1(5, 31, ctx.isNew$))(\"availableLanguageCodes\", i0.ɵɵpipeBind1(6, 33, ctx.availableLanguages$))(\"currentLanguageCode\", i0.ɵɵpipeBind1(7, 35, ctx.languageCode$));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(11, 37, ctx.isNew$))(\"ngIfElse\", updateButton_r16);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.detailForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdatePromotion\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(20, 39, ctx.entity$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(25, 41, \"common.name\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"readonly\", !i0.ɵɵpipeBind1(27, 43, \"UpdatePromotion\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"readonly\", !i0.ɵɵpipeBind1(29, 45, \"UpdatePromotion\"))(\"label\", i0.ɵɵpipeBind1(30, 47, \"common.description\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(32, 49, \"marketing.starts-at\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(35, 51, \"marketing.ends-at\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(38, 53, \"marketing.coupon-code\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"readonly\", !i0.ɵɵpipeBind1(40, 55, \"UpdatePromotion\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(42, 57, \"marketing.per-customer-limit\"))(\"tooltip\", i0.ɵɵpipeBind1(43, 59, \"marketing.per-customer-limit-tooltip\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"readonly\", !i0.ɵɵpipeBind1(45, 61, \"UpdatePromotion\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(47, 63, \"marketing.usage-limit\"))(\"tooltip\", i0.ɵɵpipeBind1(48, 65, \"marketing.usage-limit-tooltip\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"readonly\", !i0.ɵɵpipeBind1(50, 67, \"UpdatePromotion\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.customFields.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"entity$\", ctx.entity$)(\"detailForm\", ctx.detailForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(54, 69, \"marketing.conditions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.conditions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdatePromotion\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(59, 71, \"marketing.actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.actions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"vdrIfPermissions\", \"UpdatePromotion\");\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3.ClrLabel, i3.ClrCheckbox, i3.ClrCheckboxWrapper, i4.NgForOf, i4.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MinValidator, i2.MaxValidator, i2.FormGroupDirective, i2.FormControlName, i2.FormGroupName, i2.FormArrayName, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ActionBarDropdownMenuComponent, i1.ConfigurableInputComponent, i1.FormFieldComponent, i1.FormFieldControlDirective, i1.LanguageSelectorComponent, i1.RichTextEditorComponent, i1.DropdownComponent, i1.DropdownMenuComponent, i1.DropdownTriggerDirective, i1.DropdownItemDirective, i1.IfPermissionsDirective, i1.ActionBarItemsComponent, i1.DatetimePickerComponent, i1.TabbedCustomFieldsComponent, i1.CustomDetailComponentHostComponent, i1.PageBlockComponent, i1.PageEntityInfoComponent, i1.PageDetailLayoutComponent, i1.PageDetailSidebarComponent, i1.CardComponent, i4.AsyncPipe, i5.TranslatePipe, i1.HasPermissionPipe],\n      styles: [\".item-wrap[_ngcontent-%COMP%]{white-space:normal}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PromotionDetailComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-promotion-detail',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-block>\\n    <vdr-action-bar>\\n        <vdr-ab-left>\\n            <div class=\\\"flex clr-align-items-center\\\">\\n                <vdr-language-selector\\n                    [disabled]=\\\"isNew$ | async\\\"\\n                    [availableLanguageCodes]=\\\"availableLanguages$ | async\\\"\\n                    [currentLanguageCode]=\\\"languageCode$ | async\\\"\\n                    (languageCodeChange)=\\\"setLanguage($event)\\\"\\n                ></vdr-language-selector>\\n            </div>\\n        </vdr-ab-left>\\n\\n        <vdr-ab-right>\\n            <vdr-action-bar-items locationId=\\\"promotion-detail\\\" />\\n            <button\\n                class=\\\"btn btn-primary\\\"\\n                *ngIf=\\\"isNew$ | async; else updateButton\\\"\\n                (click)=\\\"create()\\\"\\n                [disabled]=\\\"!saveButtonEnabled()\\\"\\n            >\\n                {{ 'common.create' | translate }}\\n            </button>\\n            <ng-template #updateButton>\\n                <button\\n                    class=\\\"btn btn-primary\\\"\\n                    (click)=\\\"save()\\\"\\n                    *vdrIfPermissions=\\\"'UpdatePromotion'\\\"\\n                    [disabled]=\\\"!saveButtonEnabled()\\\"\\n                >\\n                    {{ 'common.update' | translate }}\\n                </button>\\n            </ng-template>\\n            <vdr-action-bar-dropdown-menu locationId=\\\"promotion-detail\\\" />\\n        </vdr-ab-right>\\n    </vdr-action-bar>\\n</vdr-page-block>\\n\\n<form class=\\\"form\\\" [formGroup]=\\\"detailForm\\\">\\n    <vdr-page-detail-layout>\\n        <vdr-page-detail-sidebar>\\n            <vdr-card *vdrIfPermissions=\\\"'UpdatePromotion'\\\">\\n                <clr-toggle-wrapper>\\n                    <input type=\\\"checkbox\\\" clrToggle name=\\\"enabled\\\" formControlName=\\\"enabled\\\" />\\n                    <label>{{ 'common.enabled' | translate }}</label>\\n                </clr-toggle-wrapper>\\n            </vdr-card>\\n            <vdr-card *ngIf=\\\"entity$ | async as entity\\\">\\n                <vdr-page-entity-info [entity]=\\\"entity\\\" />\\n            </vdr-card>\\n        </vdr-page-detail-sidebar>\\n        <vdr-page-block>\\n            <vdr-card>\\n                <div class=\\\"form-grid\\\">\\n                    <vdr-form-field [label]=\\\"'common.name' | translate\\\" for=\\\"name\\\">\\n                        <input\\n                            id=\\\"name\\\"\\n                            [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                            type=\\\"text\\\"\\n                            formControlName=\\\"name\\\"\\n                        />\\n                    </vdr-form-field>\\n                    <vdr-rich-text-editor\\n                        class=\\\"form-grid-span\\\"\\n                        formControlName=\\\"description\\\"\\n                        [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                        [label]=\\\"'common.description' | translate\\\"\\n                    ></vdr-rich-text-editor>\\n                    <vdr-form-field [label]=\\\"'marketing.starts-at' | translate\\\" for=\\\"startsAt\\\">\\n                        <vdr-datetime-picker formControlName=\\\"startsAt\\\"></vdr-datetime-picker>\\n                    </vdr-form-field>\\n                    <vdr-form-field [label]=\\\"'marketing.ends-at' | translate\\\" for=\\\"endsAt\\\">\\n                        <vdr-datetime-picker formControlName=\\\"endsAt\\\"></vdr-datetime-picker>\\n                    </vdr-form-field>\\n                    <vdr-form-field [label]=\\\"'marketing.coupon-code' | translate\\\" for=\\\"couponCode\\\">\\n                        <input\\n                            id=\\\"couponCode\\\"\\n                            [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                            type=\\\"text\\\"\\n                            formControlName=\\\"couponCode\\\"\\n                        />\\n                    </vdr-form-field>\\n                    <vdr-form-field\\n                        [label]=\\\"'marketing.per-customer-limit' | translate\\\"\\n                        [tooltip]=\\\"'marketing.per-customer-limit-tooltip' | translate\\\"\\n                        for=\\\"perCustomerUsageLimit\\\"\\n                    >\\n                        <input\\n                            id=\\\"perCustomerUsageLimit\\\"\\n                            [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                            type=\\\"number\\\"\\n                            min=\\\"1\\\"\\n                            max=\\\"999\\\"\\n                            formControlName=\\\"perCustomerUsageLimit\\\"\\n                        />\\n                    </vdr-form-field>\\n                    <vdr-form-field\\n                        [label]=\\\"'marketing.usage-limit' | translate\\\"\\n                        [tooltip]=\\\"'marketing.usage-limit-tooltip' | translate\\\"\\n                        for=\\\"usageLimit\\\"\\n                    >\\n                        <input\\n                            id=\\\"usageLimit\\\"\\n                            [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                            type=\\\"number\\\"\\n                            min=\\\"1\\\"\\n                            max=\\\"9999999\\\"\\n                            formControlName=\\\"usageLimit\\\"\\n                        />\\n                    </vdr-form-field>\\n                </div>\\n            </vdr-card>\\n            <vdr-card\\n                [title]=\\\"'common.custom-fields' | translate\\\"\\n                formGroupName=\\\"customFields\\\"\\n                *ngIf=\\\"customFields.length\\\"\\n            >\\n                <vdr-tabbed-custom-fields\\n                    entityName=\\\"Promotion\\\"\\n                    [customFields]=\\\"customFields\\\"\\n                    [customFieldsFormGroup]=\\\"detailForm.get('customFields')\\\"\\n                    [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                ></vdr-tabbed-custom-fields>\\n            </vdr-card>\\n\\n            <vdr-custom-detail-component-host\\n                locationId=\\\"promotion-detail\\\"\\n                [entity$]=\\\"entity$\\\"\\n                [detailForm]=\\\"detailForm\\\"\\n            ></vdr-custom-detail-component-host>\\n\\n            <vdr-card [title]=\\\"'marketing.conditions' | translate\\\" formArrayName=\\\"conditions\\\">\\n                <div *ngFor=\\\"let condition of conditions; index as i\\\">\\n                    <vdr-configurable-input\\n                        (remove)=\\\"removeCondition($event)\\\"\\n                        [position]=\\\"i\\\"\\n                        [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                        [operation]=\\\"condition\\\"\\n                        [operationDefinition]=\\\"getConditionDefinition(condition)\\\"\\n                        [formControlName]=\\\"i\\\"\\n                    ></vdr-configurable-input>\\n                </div>\\n                <div>\\n                    <vdr-dropdown *vdrIfPermissions=\\\"'UpdatePromotion'\\\">\\n                        <button class=\\\"btn btn-outline\\\" vdrDropdownTrigger>\\n                            <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                            {{ 'marketing.add-condition' | translate }}\\n                        </button>\\n                        <vdr-dropdown-menu vdrPosition=\\\"bottom-left\\\">\\n                            <button\\n                                *ngFor=\\\"let condition of getAvailableConditions()\\\"\\n                                type=\\\"button\\\"\\n                                vdrDropdownItem\\n                                class=\\\"item-wrap\\\"\\n                                (click)=\\\"addCondition(condition)\\\"\\n                            >\\n                                {{ condition.description }}\\n                            </button>\\n                        </vdr-dropdown-menu>\\n                    </vdr-dropdown>\\n                </div>\\n            </vdr-card>\\n            <vdr-card [title]=\\\"'marketing.actions' | translate\\\" formArrayName=\\\"actions\\\">\\n                <div *ngFor=\\\"let action of actions; index as i\\\">\\n                    <vdr-configurable-input\\n                        (remove)=\\\"removeAction($event)\\\"\\n                        [position]=\\\"i\\\"\\n                        [operation]=\\\"action\\\"\\n                        [readonly]=\\\"!('UpdatePromotion' | hasPermission)\\\"\\n                        [operationDefinition]=\\\"getActionDefinition(action)\\\"\\n                        [formControlName]=\\\"i\\\"\\n                    ></vdr-configurable-input>\\n                </div>\\n                <div>\\n                    <vdr-dropdown *vdrIfPermissions=\\\"'UpdatePromotion'\\\">\\n                        <button class=\\\"btn btn-outline\\\" vdrDropdownTrigger>\\n                            <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                            {{ 'marketing.add-action' | translate }}\\n                        </button>\\n                        <vdr-dropdown-menu vdrPosition=\\\"bottom-left\\\">\\n                            <button\\n                                *ngFor=\\\"let action of getAvailableActions()\\\"\\n                                type=\\\"button\\\"\\n                                vdrDropdownItem\\n                                class=\\\"item-wrap\\\"\\n                                (click)=\\\"addAction(action)\\\"\\n                            >\\n                                {{ action.description }}\\n                            </button>\\n                        </vdr-dropdown-menu>\\n                    </vdr-dropdown>\\n                </div>\\n            </vdr-card>\\n        </vdr-page-block>\\n    </vdr-page-detail-layout>\\n</form>\\n\",\n      styles: [\".item-wrap{white-space:normal}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.DataService\n  }, {\n    type: i2.FormBuilder\n  }, {\n    type: i1.NotificationService\n  }], null);\n})();\nconst ASSIGN_PROMOTIONS_TO_CHANNEL = gql`\n    mutation AssignPromotionsToChannel($input: AssignPromotionsToChannelInput!) {\n        assignPromotionsToChannel(input: $input) {\n            id\n            name\n        }\n    }\n`;\nconst REMOVE_PROMOTIONS_FROM_CHANNEL = gql`\n    mutation RemovePromotionsFromChannel($input: RemovePromotionsFromChannelInput!) {\n        removePromotionsFromChannel(input: $input) {\n            id\n            name\n        }\n    }\n`;\nconst deletePromotionsBulkAction = createBulkDeleteAction({\n  location: 'promotion-list',\n  requiresPermission: Permission.DeletePromotion,\n  getItemName: item => item.name,\n  bulkDelete: (dataService, ids) => dataService.promotion.deletePromotions(ids).pipe(map(res => res.deletePromotions))\n});\nconst assignPromotionsToChannelBulkAction = createBulkAssignToChannelAction({\n  location: 'promotion-list',\n  requiresPermission: Permission.UpdatePromotion,\n  getItemName: item => item.name,\n  bulkAssignToChannel: (dataService, promotionIds, channelIds) => {\n    return channelIds.map(channelId => dataService.mutate(AssignPromotionsToChannelDocument, {\n      input: {\n        channelId,\n        promotionIds\n      }\n    }).pipe(map(res => res.assignPromotionsToChannel)));\n  }\n});\nconst removePromotionsFromChannelBulkAction = createBulkRemoveFromChannelAction({\n  location: 'promotion-list',\n  requiresPermission: Permission.DeleteCatalog,\n  getItemName: item => item.name,\n  bulkRemoveFromChannel: (dataService, promotionIds, channelId) => dataService.mutate(RemovePromotionsFromChannelDocument, {\n    input: {\n      channelId,\n      promotionIds\n    }\n  }).pipe(map(res => res.removePromotionsFromChannel))\n});\nconst duplicatePromotionsBulkAction = {\n  location: 'promotion-list',\n  label: marker('common.duplicate'),\n  icon: 'copy',\n  onClick: ({\n    injector,\n    selection,\n    hostComponent,\n    clearSelection\n  }) => {\n    const modalService = injector.get(ModalService);\n    modalService.fromComponent(DuplicateEntityDialogComponent, {\n      locals: {\n        entities: selection,\n        entityName: 'Promotion',\n        title: marker('marketing.duplicate-promotions'),\n        getEntityName: entity => entity.name\n      }\n    }).subscribe(result => {\n      if (result) {\n        clearSelection();\n        hostComponent.refresh();\n      }\n    });\n  }\n};\nconst GET_PROMOTION_LIST = gql`\n    query GetPromotionList($options: PromotionListOptions) {\n        promotions(options: $options) {\n            items {\n                ...Promotion\n            }\n            totalItems\n        }\n    }\n    ${PROMOTION_FRAGMENT}\n`;\nclass PromotionListComponent extends TypedBaseListComponent {\n  constructor() {\n    super();\n    this.dataTableListId = 'promotion-list';\n    this.customFields = this.getCustomFieldConfig('Promotion');\n    this.filters = this.createFilterCollection().addIdFilter().addDateFilters().addFilters([{\n      name: 'startsAt',\n      type: {\n        kind: 'dateRange'\n      },\n      label: marker('marketing.starts-at'),\n      filterField: 'startsAt'\n    }, {\n      name: 'endsAt',\n      type: {\n        kind: 'dateRange'\n      },\n      label: marker('marketing.ends-at'),\n      filterField: 'endsAt'\n    }, {\n      name: 'enabled',\n      type: {\n        kind: 'boolean'\n      },\n      label: marker('common.enabled'),\n      filterField: 'enabled'\n    }, {\n      name: 'name',\n      type: {\n        kind: 'text'\n      },\n      label: marker('common.name'),\n      filterField: 'name'\n    }, {\n      name: 'couponCode',\n      type: {\n        kind: 'text'\n      },\n      label: marker('marketing.coupon-code'),\n      filterField: 'couponCode'\n    }, {\n      name: 'desc',\n      type: {\n        kind: 'text'\n      },\n      label: marker('common.description'),\n      filterField: 'description'\n    }, {\n      name: 'perCustomerUsageLimit',\n      type: {\n        kind: 'number'\n      },\n      label: marker('marketing.per-customer-limit'),\n      filterField: 'perCustomerUsageLimit'\n    }, {\n      name: 'usageLimit',\n      type: {\n        kind: 'number'\n      },\n      label: marker('marketing.usage-limit'),\n      filterField: 'usageLimit'\n    }]).addCustomFieldFilters(this.customFields).connectToRoute(this.route);\n    this.sorts = this.createSortCollection().defaultSort('createdAt', 'DESC').addSorts([{\n      name: 'createdAt'\n    }, {\n      name: 'updatedAt'\n    }, {\n      name: 'startsAt'\n    }, {\n      name: 'endsAt'\n    }, {\n      name: 'name'\n    }, {\n      name: 'couponCode'\n    }, {\n      name: 'perCustomerUsageLimit'\n    }, {\n      name: 'usageLimit'\n    }]).addCustomFieldSorts(this.customFields).connectToRoute(this.route);\n    super.configure({\n      document: GetPromotionListDocument,\n      getItems: data => data.promotions,\n      setVariables: (skip, take) => this.createQueryOptions(skip, take, this.searchTermControl.value),\n      refreshListOnChanges: [this.filters.valueChanges, this.sorts.valueChanges]\n    });\n  }\n  createQueryOptions(skip, take, searchTerm) {\n    const filter = this.filters.createFilterInput();\n    const sort = this.sorts.createSortInput();\n    let filterOperator = LogicalOperator.AND;\n    if (searchTerm) {\n      filter.couponCode = {\n        contains: searchTerm\n      };\n      filter.name = {\n        contains: searchTerm\n      };\n      filterOperator = LogicalOperator.OR;\n    }\n    return {\n      options: {\n        skip,\n        take,\n        filter,\n        filterOperator,\n        sort\n      }\n    };\n  }\n  static {\n    this.ɵfac = function PromotionListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PromotionListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PromotionListComponent,\n      selectors: [[\"vdr-promotion-list\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 49,\n      vars: 72,\n      consts: [[3, \"languageCodeChange\", \"availableLanguageCodes\", \"currentLanguageCode\"], [\"locationId\", \"promotion-list\"], [\"class\", \"btn btn-primary\", 3, \"routerLink\", 4, \"vdrIfPermissions\"], [3, \"pageChange\", \"itemsPerPageChange\", \"visibleColumnsChange\", \"id\", \"items\", \"itemsPerPage\", \"totalItems\", \"currentPage\", \"filters\"], [\"locationId\", \"promotion-list\", 3, \"hostComponent\", \"selectionManager\"], [3, \"searchTermControl\", \"searchTermPlaceholder\"], [\"id\", \"id\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"created-at\", 3, \"heading\", \"hiddenByDefault\", \"sort\"], [\"id\", \"updated-at\", 3, \"heading\", \"hiddenByDefault\", \"sort\"], [\"id\", \"name\", 3, \"heading\", \"optional\", \"sort\"], [\"id\", \"enabled\", 3, \"heading\"], [\"id\", \"coupon-code\", 3, \"heading\", \"sort\"], [\"id\", \"starts-at\", 3, \"heading\", \"sort\"], [\"id\", \"ends-at\", 3, \"heading\", \"sort\"], [\"id\", \"per-customer-limit\", 3, \"heading\", \"sort\", \"hiddenByDefault\"], [\"id\", \"usage-limit\", 3, \"heading\", \"sort\", \"hiddenByDefault\"], [3, \"customField\", \"sorts\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-primary\", 3, \"routerLink\"], [\"shape\", \"plus\"], [1, \"button-ghost\", 3, \"routerLink\"], [\"shape\", \"arrow right\"], [\"colorType\", \"success\", 4, \"ngIf\"], [\"colorType\", \"warning\", 4, \"ngIf\"], [\"colorType\", \"success\"], [\"colorType\", \"warning\"], [3, \"customField\", \"sorts\"]],\n      template: function PromotionListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-block\")(1, \"vdr-action-bar\")(2, \"vdr-ab-left\")(3, \"vdr-language-selector\", 0);\n          i0.ɵɵpipe(4, \"async\");\n          i0.ɵɵpipe(5, \"async\");\n          i0.ɵɵlistener(\"languageCodeChange\", function PromotionListComponent_Template_vdr_language_selector_languageCodeChange_3_listener($event) {\n            return ctx.setLanguage($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"vdr-ab-right\");\n          i0.ɵɵelement(7, \"vdr-action-bar-items\", 1);\n          i0.ɵɵtemplate(8, PromotionListComponent_a_8_Template, 4, 5, \"a\", 2);\n          i0.ɵɵelement(9, \"vdr-action-bar-dropdown-menu\", 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"vdr-data-table-2\", 3);\n          i0.ɵɵpipe(11, \"async\");\n          i0.ɵɵpipe(12, \"async\");\n          i0.ɵɵpipe(13, \"async\");\n          i0.ɵɵpipe(14, \"async\");\n          i0.ɵɵlistener(\"pageChange\", function PromotionListComponent_Template_vdr_data_table_2_pageChange_10_listener($event) {\n            return ctx.setPageNumber($event);\n          })(\"itemsPerPageChange\", function PromotionListComponent_Template_vdr_data_table_2_itemsPerPageChange_10_listener($event) {\n            return ctx.setItemsPerPage($event);\n          })(\"visibleColumnsChange\", function PromotionListComponent_Template_vdr_data_table_2_visibleColumnsChange_10_listener($event) {\n            return ctx.setVisibleColumns($event);\n          });\n          i0.ɵɵelement(15, \"vdr-bulk-action-menu\", 4)(16, \"vdr-dt2-search\", 5);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵelementStart(18, \"vdr-dt2-column\", 6);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵtemplate(20, PromotionListComponent_ng_template_20_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"vdr-dt2-column\", 7);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵtemplate(23, PromotionListComponent_ng_template_23_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"vdr-dt2-column\", 8);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵtemplate(26, PromotionListComponent_ng_template_26_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"vdr-dt2-column\", 9);\n          i0.ɵɵpipe(28, \"translate\");\n          i0.ɵɵtemplate(29, PromotionListComponent_ng_template_29_Template, 4, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"vdr-dt2-column\", 10);\n          i0.ɵɵpipe(31, \"translate\");\n          i0.ɵɵtemplate(32, PromotionListComponent_ng_template_32_Template, 2, 2, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"vdr-dt2-column\", 11);\n          i0.ɵɵpipe(34, \"translate\");\n          i0.ɵɵtemplate(35, PromotionListComponent_ng_template_35_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"vdr-dt2-column\", 12);\n          i0.ɵɵpipe(37, \"translate\");\n          i0.ɵɵtemplate(38, PromotionListComponent_ng_template_38_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"vdr-dt2-column\", 13);\n          i0.ɵɵpipe(40, \"translate\");\n          i0.ɵɵtemplate(41, PromotionListComponent_ng_template_41_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"vdr-dt2-column\", 14);\n          i0.ɵɵpipe(43, \"translate\");\n          i0.ɵɵtemplate(44, PromotionListComponent_ng_template_44_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"vdr-dt2-column\", 15);\n          i0.ɵɵpipe(46, \"translate\");\n          i0.ɵɵtemplate(47, PromotionListComponent_ng_template_47_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, PromotionListComponent_vdr_dt2_custom_field_column_48_Template, 1, 2, \"vdr-dt2-custom-field-column\", 16);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"availableLanguageCodes\", i0.ɵɵpipeBind1(4, 38, ctx.availableLanguages$))(\"currentLanguageCode\", i0.ɵɵpipeBind1(5, 40, ctx.contentLanguage$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"vdrIfPermissions\", \"CreatePromotion\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.dataTableListId)(\"items\", i0.ɵɵpipeBind1(11, 42, ctx.items$))(\"itemsPerPage\", i0.ɵɵpipeBind1(12, 44, ctx.itemsPerPage$))(\"totalItems\", i0.ɵɵpipeBind1(13, 46, ctx.totalItems$))(\"currentPage\", i0.ɵɵpipeBind1(14, 48, ctx.currentPage$))(\"filters\", ctx.filters);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"hostComponent\", ctx)(\"selectionManager\", ctx.selectionManager);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"searchTermControl\", ctx.searchTermControl)(\"searchTermPlaceholder\", i0.ɵɵpipeBind1(17, 50, \"marketing.search-by-name-or-coupon-code\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(19, 52, \"common.id\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(22, 54, \"common.created-at\"))(\"hiddenByDefault\", true)(\"sort\", ctx.sorts.get(\"createdAt\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(25, 56, \"common.updated-at\"))(\"hiddenByDefault\", true)(\"sort\", ctx.sorts.get(\"updatedAt\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(28, 58, \"common.name\"))(\"optional\", false)(\"sort\", ctx.sorts.get(\"name\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(31, 60, \"common.enabled\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(34, 62, \"marketing.coupon-code\"))(\"sort\", ctx.sorts.get(\"couponCode\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(37, 64, \"marketing.starts-at\"))(\"sort\", ctx.sorts.get(\"startsAt\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(40, 66, \"marketing.ends-at\"))(\"sort\", ctx.sorts.get(\"endsAt\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(43, 68, \"marketing.per-customer-limit\"))(\"sort\", ctx.sorts.get(\"perCustomerUsageLimit\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(46, 70, \"marketing.usage-limit\"))(\"sort\", ctx.sorts.get(\"usageLimit\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.customFields);\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i4.NgForOf, i4.NgIf, i3$1.RouterLink, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ActionBarDropdownMenuComponent, i1.ChipComponent, i1.LanguageSelectorComponent, i1.IfPermissionsDirective, i1.ActionBarItemsComponent, i1.BulkActionMenuComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i1.DataTable2SearchComponent, i1.DataTableCustomFieldColumnComponent, i1.PageBlockComponent, i4.AsyncPipe, i5.TranslatePipe, i1.LocaleDatePipe],\n      styles: [\".search-form[_ngcontent-%COMP%]{padding:0}.search-input[_ngcontent-%COMP%]{margin:6px 8px 0 0;min-width:200px}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PromotionListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-promotion-list',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-block>\\n    <vdr-action-bar>\\n        <vdr-ab-left>\\n            <vdr-language-selector\\n                [availableLanguageCodes]=\\\"availableLanguages$ | async\\\"\\n                [currentLanguageCode]=\\\"contentLanguage$ | async\\\"\\n                (languageCodeChange)=\\\"setLanguage($event)\\\"\\n            />\\n        </vdr-ab-left>\\n        <vdr-ab-right>\\n            <vdr-action-bar-items locationId=\\\"promotion-list\\\" />\\n            <a class=\\\"btn btn-primary\\\" [routerLink]=\\\"['./create']\\\" *vdrIfPermissions=\\\"'CreatePromotion'\\\">\\n                <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                {{ 'marketing.create-new-promotion' | translate }}\\n            </a>\\n            <vdr-action-bar-dropdown-menu locationId=\\\"promotion-list\\\" />\\n        </vdr-ab-right>\\n    </vdr-action-bar>\\n</vdr-page-block>\\n\\n<vdr-data-table-2\\n    [id]=\\\"dataTableListId\\\"\\n    [items]=\\\"items$ | async\\\"\\n    [itemsPerPage]=\\\"itemsPerPage$ | async\\\"\\n    [totalItems]=\\\"totalItems$ | async\\\"\\n    [currentPage]=\\\"currentPage$ | async\\\"\\n    [filters]=\\\"filters\\\"\\n    (pageChange)=\\\"setPageNumber($event)\\\"\\n    (itemsPerPageChange)=\\\"setItemsPerPage($event)\\\"\\n    (visibleColumnsChange)=\\\"setVisibleColumns($event)\\\"\\n>\\n    <vdr-bulk-action-menu\\n        locationId=\\\"promotion-list\\\"\\n        [hostComponent]=\\\"this\\\"\\n        [selectionManager]=\\\"selectionManager\\\"\\n    />\\n    <vdr-dt2-search\\n        [searchTermControl]=\\\"searchTermControl\\\"\\n        [searchTermPlaceholder]=\\\"'marketing.search-by-name-or-coupon-code' | translate\\\"\\n    />\\n    <vdr-dt2-column [heading]=\\\"'common.id' | translate\\\" id=\\\"id\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-promotion=\\\"item\\\">{{ promotion.id }}</ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'common.created-at' | translate\\\"\\n        id=\\\"created-at\\\"\\n        [hiddenByDefault]=\\\"true\\\"\\n        [sort]=\\\"sorts.get('createdAt')\\\"\\n    >\\n        <ng-template let-promotion=\\\"item\\\">\\n            {{ promotion.createdAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'common.updated-at' | translate\\\"\\n        id=\\\"updated-at\\\"\\n        [hiddenByDefault]=\\\"true\\\"\\n        [sort]=\\\"sorts.get('updatedAt')\\\"\\n    >\\n        <ng-template let-promotion=\\\"item\\\">\\n            {{ promotion.updatedAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'common.name' | translate\\\"\\n        id=\\\"name\\\"\\n        [optional]=\\\"false\\\"\\n        [sort]=\\\"sorts.get('name')\\\"\\n    >\\n        <ng-template let-promotion=\\\"item\\\">\\n            <a class=\\\"button-ghost\\\" [routerLink]=\\\"['./', promotion.id]\\\"\\n                ><span> {{ promotion.name }}</span>\\n                <clr-icon shape=\\\"arrow right\\\"></clr-icon>\\n            </a>\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'common.enabled' | translate\\\" id=\\\"enabled\\\">\\n        <ng-template let-promotion=\\\"item\\\">\\n            <vdr-chip *ngIf=\\\"promotion.enabled\\\" colorType=\\\"success\\\">{{\\n                'common.enabled' | translate\\n            }}</vdr-chip>\\n            <vdr-chip *ngIf=\\\"!promotion.enabled\\\" colorType=\\\"warning\\\">{{\\n                'common.disabled' | translate\\n            }}</vdr-chip>\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'marketing.coupon-code' | translate\\\"\\n        id=\\\"coupon-code\\\"\\n        [sort]=\\\"sorts.get('couponCode')\\\"\\n    >\\n        <ng-template let-promotion=\\\"item\\\">\\n            {{ promotion.couponCode }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'marketing.starts-at' | translate\\\"\\n        id=\\\"starts-at\\\"\\n        [sort]=\\\"sorts.get('startsAt')\\\"\\n    >\\n        <ng-template let-promotion=\\\"item\\\">\\n            {{ promotion.startsAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'marketing.ends-at' | translate\\\" id=\\\"ends-at\\\" [sort]=\\\"sorts.get('endsAt')\\\">\\n        <ng-template let-promotion=\\\"item\\\">\\n            {{ promotion.endsAt | localeDate : 'short' }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'marketing.per-customer-limit' | translate\\\"\\n        id=\\\"per-customer-limit\\\"\\n        [sort]=\\\"sorts.get('perCustomerUsageLimit')\\\"\\n        [hiddenByDefault]=\\\"true\\\"\\n    >\\n        <ng-template let-promotion=\\\"item\\\">\\n            {{ promotion.perCustomerUsageLimit }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column\\n        [heading]=\\\"'marketing.usage-limit' | translate\\\"\\n        id=\\\"usage-limit\\\"\\n        [sort]=\\\"sorts.get('usageLimit')\\\"\\n        [hiddenByDefault]=\\\"true\\\"\\n    >\\n        <ng-template let-promotion=\\\"item\\\">\\n            {{ promotion.usageLimit }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-custom-field-column\\n        *ngFor=\\\"let customField of customFields\\\"\\n        [customField]=\\\"customField\\\"\\n        [sorts]=\\\"sorts\\\"\\n    />\\n</vdr-data-table-2>\\n\",\n      styles: [\".search-form{padding:0}.search-input{margin:6px 8px 0 0;min-width:200px}\\n\"]\n    }]\n  }], () => [], null);\n})();\nconst createRoutes = pageService => [{\n  path: 'promotions',\n  component: PageComponent,\n  data: {\n    locationId: 'promotion-list',\n    breadcrumb: marker('breadcrumb.promotions')\n  },\n  children: pageService.getPageTabRoutes('promotion-list')\n}, {\n  path: 'promotions/:id',\n  component: PageComponent,\n  data: {\n    locationId: 'promotion-detail',\n    breadcrumb: {\n      label: marker('breadcrumb.promotions'),\n      link: ['../', 'promotions']\n    }\n  },\n  children: pageService.getPageTabRoutes('promotion-detail')\n}];\nfunction promotionBreadcrumb(data, params) {\n  return detailBreadcrumb({\n    entity: data.entity,\n    id: params.id,\n    breadcrumbKey: 'breadcrumb.promotions',\n    getName: promotion => promotion.name,\n    route: 'promotions'\n  });\n}\nclass MarketingModule {\n  static {\n    this.hasRegisteredTabsAndBulkActions = false;\n  }\n  constructor(bulkActionRegistryService, pageService) {\n    if (MarketingModule.hasRegisteredTabsAndBulkActions) {\n      return;\n    }\n    bulkActionRegistryService.registerBulkAction(assignPromotionsToChannelBulkAction);\n    bulkActionRegistryService.registerBulkAction(duplicatePromotionsBulkAction);\n    bulkActionRegistryService.registerBulkAction(removePromotionsFromChannelBulkAction);\n    bulkActionRegistryService.registerBulkAction(deletePromotionsBulkAction);\n    pageService.registerPageTab({\n      priority: 0,\n      location: 'promotion-list',\n      tab: marker('breadcrumb.promotions'),\n      route: '',\n      component: PromotionListComponent\n    });\n    pageService.registerPageTab({\n      priority: 0,\n      location: 'promotion-detail',\n      tab: marker('marketing.promotion'),\n      route: '',\n      component: detailComponentWithResolver({\n        component: PromotionDetailComponent,\n        query: GetPromotionDetailDocument,\n        entityKey: 'promotion',\n        getBreadcrumbs: entity => [{\n          label: entity ? entity.name : marker('marketing.create-new-promotion'),\n          link: [entity?.id]\n        }]\n      })\n    });\n    MarketingModule.hasRegisteredTabsAndBulkActions = true;\n  }\n  static {\n    this.ɵfac = function MarketingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarketingModule)(i0.ɵɵinject(i1.BulkActionRegistryService), i0.ɵɵinject(i1.PageService));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MarketingModule,\n      declarations: [PromotionListComponent, PromotionDetailComponent],\n      imports: [SharedModule, i3$1.RouterModule, SharedModule, AsyncPipe, SharedModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: ROUTES,\n        useFactory: pageService => createRoutes(pageService),\n        multi: true,\n        deps: [PageService]\n      }],\n      imports: [SharedModule, RouterModule.forChild([]), SharedModule, SharedModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MarketingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [SharedModule, RouterModule.forChild([]), SharedModule, AsyncPipe, SharedModule],\n      providers: [{\n        provide: ROUTES,\n        useFactory: pageService => createRoutes(pageService),\n        multi: true,\n        deps: [PageService]\n      }],\n      declarations: [PromotionListComponent, PromotionDetailComponent]\n    }]\n  }], () => [{\n    type: i1.BulkActionRegistryService\n  }, {\n    type: i1.PageService\n  }], null);\n})();\n\n// This file was generated by the build-public-api.ts script\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { GET_PROMOTION_DETAIL, GET_PROMOTION_LIST, MarketingModule, PromotionDetailComponent, PromotionListComponent, assignPromotionsToChannelBulkAction, createRoutes, deletePromotionsBulkAction, duplicatePromotionsBulkAction, promotionBreadcrumb, removePromotionsFromChannelBulkAction };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,sEAAsE;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,YAAY,CAAC,OAAO,kBAAkB,CAAC;AACrD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,oFAAoF;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,CAAC,OAAO,kBAAkB,CAAC;AACrD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,UAAU,EAAE;AAAA,EAChG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,oBAAoB,iBAAiB;AAAA,EACrD;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU,EAAE,GAAG,oBAAoB;AACxD,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,eAAe,GAAG,OAAO;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC7D;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,UAAU,GAAG,wBAAwB,EAAE;AAC1C,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,SAAS;AAAA,EACnC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,UAAU,GAAG,4BAA4B,EAAE;AAC9C,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,sBAAsB,CAAC;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,gBAAgB,OAAO,YAAY,EAAE,yBAAyB,OAAO,WAAW,IAAI,cAAc,CAAC,EAAE,YAAY,CAAI,YAAY,GAAG,GAAG,iBAAiB,CAAC;AAAA,EACzK;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,0BAA0B,EAAE;AAC3D,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,WAAW,UAAU,SAAS,kFAAkF,QAAQ;AACzH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,IAAI,EAAE,YAAY,CAAI,YAAY,GAAG,GAAG,iBAAiB,CAAC,EAAE,aAAa,YAAY,EAAE,uBAAuB,OAAO,uBAAuB,YAAY,CAAC,EAAE,mBAAmB,IAAI;AAAA,EAC9M;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,qFAAqF;AACnH,YAAM,gBAAmB,cAAc,GAAG,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,aAAa,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAgB,IAAI;AAC1B,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,cAAc,aAAa,GAAG;AAAA,EAC3D;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,qBAAqB,EAAE;AAC5C,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,UAAU,EAAE;AAC/F,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,yBAAyB,GAAG,GAAG;AAC/E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,uBAAuB,CAAC;AAAA,EAC1D;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,0BAA0B,EAAE;AAC3D,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,WAAW,UAAU,SAAS,kFAAkF,QAAQ;AACzH,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,UAAM,QAAQ,IAAI;AAClB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,KAAK,EAAE,aAAa,UAAU,EAAE,YAAY,CAAI,YAAY,GAAG,GAAG,iBAAiB,CAAC,EAAE,uBAAuB,OAAO,oBAAoB,UAAU,CAAC,EAAE,mBAAmB,KAAK;AAAA,EACzM;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,qFAAqF;AACnH,YAAM,aAAgB,cAAc,IAAI,EAAE;AAC1C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,UAAU,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,WAAW,aAAa,GAAG;AAAA,EACxD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,qBAAqB,EAAE;AAC5C,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,UAAU,EAAE;AAC/F,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,sBAAsB,GAAG,GAAG;AAC5E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,oBAAoB,CAAC;AAAA,EACvD;AACF;AACA,IAAM,MAAM,MAAM,CAAC,UAAU;AAC7B,IAAM,MAAM,QAAM,CAAC,MAAM,EAAE;AAC3B,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,cAAiB,gBAAgB,GAAG,GAAG,CAAC;AACtD,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,gCAAgC,GAAG,GAAG;AAAA,EACxF;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,kBAAkB,aAAa,EAAE;AAAA,EACtC;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,aAAa,WAAW,OAAO,GAAG,GAAG;AAAA,EACvF;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,aAAa,WAAW,OAAO,GAAG,GAAG;AAAA,EACvF;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,EAAE,GAAG,MAAM;AACvC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,aAAa,EAAE,CAAC;AACvE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,aAAa,MAAM,EAAE;AAAA,EAClD;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC7D;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,iBAAiB,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,2DAA2D,GAAG,GAAG,YAAY,EAAE;AAAA,EACtL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,WAAW,QAAQ,aAAa,OAAO;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,aAAa,OAAO;AAAA,EAC7C;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,mBAAmB,KAAK,aAAa,YAAY,GAAG;AAAA,EACzD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,aAAa,UAAU,OAAO,GAAG,GAAG;AAAA,EACtF;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,aAAa,QAAQ,OAAO,GAAG,GAAG;AAAA,EACpF;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,mBAAmB,KAAK,aAAa,uBAAuB,GAAG;AAAA,EACpE;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAgB,IAAI;AAC1B,IAAG,mBAAmB,KAAK,cAAc,YAAY,GAAG;AAAA,EAC1D;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,+BAA+B,EAAE;AAAA,EACnD;AACA,MAAI,KAAK,GAAG;AACV,UAAM,kBAAkB,IAAI;AAC5B,UAAM,UAAa,cAAc;AACjC,IAAG,WAAW,eAAe,eAAe,EAAE,SAAS,QAAQ,KAAK;AAAA,EACtE;AACF;AACA,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMvB,kBAAkB;AAAA;AAExB,IAAM,2BAAN,MAAM,kCAAiC,yBAAyB;AAAA,EAC9D,YAAY,gBAAgB,aAAa,aAAa,qBAAqB;AACzE,UAAM;AACN,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAC3B,SAAK,eAAe,KAAK,qBAAqB,WAAW;AACzD,SAAK,aAAa,KAAK,YAAY,MAAM;AAAA,MACvC,MAAM,CAAC,IAAI,WAAW,QAAQ;AAAA,MAC9B,aAAa;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,YAAY,KAAK,YAAY,MAAM,CAAC,CAAC;AAAA,MACrC,SAAS,KAAK,YAAY,MAAM,CAAC,CAAC;AAAA,MAClC,cAAc,KAAK,YAAY,MAAM,wBAAwB,KAAK,YAAY,CAAC;AAAA,IACjF,CAAC;AACD,SAAK,aAAa,CAAC;AACnB,SAAK,UAAU,CAAC;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,aAAa,CAAC;AACnB,SAAK,eAAe,KAAK,qBAAqB,WAAW;AAAA,EAC3D;AAAA,EACA,WAAW;AACT,SAAK,KAAK;AACV,SAAK,YAAY,UAAU,iCAAiC,EAAE,QAAQ,UAAU,UAAQ;AACtF,WAAK,aAAa,KAAK;AACvB,WAAK,gBAAgB,KAAK;AAC1B,WAAK,eAAe,aAAa;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,yBAAyB;AACvB,WAAO,KAAK,cAAc,OAAO,OAAK,CAAC,KAAK,WAAW,KAAK,OAAK,EAAE,SAAS,EAAE,IAAI,CAAC;AAAA,EACrF;AAAA,EACA,uBAAuB,WAAW;AAChC,WAAO,KAAK,cAAc,KAAK,OAAK,EAAE,SAAS,UAAU,IAAI;AAAA,EAC/D;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,WAAW,OAAO,OAAK,CAAC,KAAK,QAAQ,KAAK,OAAK,EAAE,SAAS,EAAE,IAAI,CAAC;AAAA,EAC/E;AAAA,EACA,oBAAoB,QAAQ;AAC1B,WAAO,KAAK,WAAW,KAAK,OAAK,EAAE,SAAS,OAAO,IAAI;AAAA,EACzD;AAAA,EACA,oBAAoB;AAClB,WAAO,CAAC,EAAE,KAAK,WAAW,SAAS,KAAK,WAAW,UAAU,KAAK,WAAW,WAAW,KAAK,KAAK,WAAW,MAAM,eAAe,KAAK,QAAQ,WAAW;AAAA,EAC5J;AAAA,EACA,aAAa,WAAW;AACtB,SAAK,aAAa,cAAc,SAAS;AACzC,SAAK,WAAW,YAAY;AAAA,EAC9B;AAAA,EACA,UAAU,QAAQ;AAChB,SAAK,aAAa,WAAW,MAAM;AACnC,SAAK,WAAW,YAAY;AAAA,EAC9B;AAAA,EACA,gBAAgB,WAAW;AACzB,SAAK,gBAAgB,cAAc,SAAS;AAC5C,SAAK,WAAW,YAAY;AAAA,EAC9B;AAAA,EACA,aAAa,QAAQ;AACnB,SAAK,gBAAgB,WAAW,MAAM;AACtC,SAAK,WAAW,YAAY;AAAA,EAC9B;AAAA,EACA,YAAY,KAAK;AACf,WAAO,KAAK,WAAW,IAAI,GAAG;AAAA,EAChC;AAAA,EACA,SAAS;AACP,QAAI,CAAC,KAAK,WAAW,OAAO;AAC1B;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,oBAAoB;AAAA,MACrC,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,SAAS,CAAC;AAAA,MACV,cAAc,CAAC;AAAA,IACjB,GAAG,KAAK,YAAY,KAAK,YAAY;AACrC,SAAK,YAAY,UAAU,gBAAgB,KAAK,EAAE,UAAU,CAAC;AAAA,MAC3D;AAAA,IACF,MAAM;AACJ,cAAQ,gBAAgB,YAAY;AAAA,QAClC,KAAK;AACH,eAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,YACvE,QAAQ;AAAA,UACV,CAAC;AACD,eAAK,WAAW,eAAe;AAC/B,eAAK,eAAe,aAAa;AACjC,eAAK,OAAO,SAAS,CAAC,OAAO,gBAAgB,EAAE,GAAG;AAAA,YAChD,YAAY,KAAK;AAAA,UACnB,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,oBAAoB,MAAM,gBAAgB,OAAO;AACtD;AAAA,MACJ;AAAA,IACF,GAAG,SAAO;AACR,WAAK,oBAAoB,MAAM,OAAO,4BAA4B,GAAG;AAAA,QACnE,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW,OAAO;AAC1B;AAAA,IACF;AACA,kBAAc,KAAK,SAAS,KAAK,aAAa,EAAE,KAAK,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,eAAe,YAAY,MAAM;AACxG,YAAM,QAAQ,KAAK,oBAAoB,eAAe,KAAK,YAAY,YAAY;AACnF,aAAO,KAAK,YAAY,UAAU,gBAAgB,KAAK;AAAA,IACzD,CAAC,CAAC,EAAE,UAAU,UAAQ;AACpB,WAAK,oBAAoB,QAAQ,OAAO,8BAA8B,GAAG;AAAA,QACvE,QAAQ;AAAA,MACV,CAAC;AACD,WAAK,WAAW,eAAe;AAC/B,WAAK,eAAe,aAAa;AAAA,IACnC,GAAG,SAAO;AACR,WAAK,oBAAoB,MAAM,OAAO,4BAA4B,GAAG;AAAA,QACnE,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,WAAW,WAAW,cAAc;AACtD,UAAM,YAAY,UAAU;AAC5B,UAAM,QAAQ,0BAA0B;AAAA,MACtC,cAAc;AAAA,MACd,eAAe;AAAA,MACf,mBAAmB,KAAK;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB;AAAA,QACA,MAAM,UAAU,QAAQ;AAAA,QACxB,aAAa,UAAU,eAAe;AAAA,MACxC;AAAA,IACF,CAAC;AACD,WAAO,iCACF,QADE;AAAA,MAEL,YAAY,KAAK,sBAAsB,KAAK,YAAY,UAAU,UAAU;AAAA,MAC5E,SAAS,KAAK,sBAAsB,KAAK,SAAS,UAAU,OAAO;AAAA,IACrE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ,cAAc;AAClC,UAAM,qBAAqB,gBAAgB,QAAQ,YAAY;AAC/D,SAAK,WAAW,WAAW;AAAA,MACzB,MAAM,oBAAoB;AAAA,MAC1B,aAAa,oBAAoB;AAAA,MACjC,SAAS,OAAO;AAAA,MAChB,YAAY,OAAO;AAAA,MACnB,uBAAuB,OAAO;AAAA,MAC9B,YAAY,OAAO;AAAA,MACnB,UAAU,OAAO;AAAA,MACjB,QAAQ,OAAO;AAAA,IACjB,CAAC;AACD,WAAO,WAAW,QAAQ,OAAK;AAC7B,WAAK,aAAa,cAAc,CAAC;AAAA,IACnC,CAAC;AACD,WAAO,QAAQ,QAAQ,OAAK,KAAK,aAAa,WAAW,CAAC,CAAC;AAC3D,QAAI,KAAK,aAAa,QAAQ;AAC5B,WAAK,yBAAyB,KAAK,cAAc,KAAK,WAAW,IAAI,cAAc,GAAG,QAAQ,kBAAkB;AAAA,IAClH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,YAAY,qBAAqB;AACrD,WAAO,WAAW,IAAI,CAAC,GAAG,OAAO;AAAA,MAC/B,MAAM,EAAE;AAAA,MACR,WAAW,OAAO,OAAO,oBAAoB,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,OAAO;AAAA,QACvE,MAAM,EAAE,KAAK,CAAC,EAAE;AAAA,QAChB,OAAO,qBAAqB,KAAK;AAAA,MACnC,EAAE;AAAA,IACJ,EAAE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK,WAAW;AAC3B,UAAM,kBAAkB,KAAK,YAAY,GAAG;AAC5C,UAAM,aAAa,QAAQ,eAAe,KAAK,aAAa,KAAK;AACjE,UAAM,QAAQ,gBAAgB,MAAM,UAAU,OAAK,EAAE,SAAS,UAAU,IAAI;AAC5E,QAAI,UAAU,IAAI;AAChB,YAAM,WAAW,UAAU,KAAK,OAAO,CAAC,QAAQ,QAAS,iCACpD,SADoD;AAAA,QAEvD,CAAC,IAAI,IAAI,GAAG,kBAAkB,IAAI,KAAK,KAAK,KAAK,mBAAmB,KAAK,WAAW,IAAI,IAAI;AAAA,MAC9F,IAAI,CAAC,CAAC;AACN,sBAAgB,KAAK,KAAK,YAAY,QAAQ;AAAA,QAC5C,MAAM,UAAU;AAAA,QAChB,MAAM;AAAA,MACR,CAAC,CAAC;AACF,iBAAW,KAAK;AAAA,QACd,MAAM,UAAU;AAAA,QAChB,MAAM,UAAU,KAAK,IAAI,QAAM;AAAA,UAC7B,MAAM,EAAE;AAAA,UACR,OAAO,kBAAkB,EAAE,KAAK;AAAA,QAClC,EAAE;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,mBAAmB,KAAK,WAAW,SAAS;AAC1C,UAAM,MAAM,QAAQ,eAAe,KAAK,cAAc,KAAK,OAAK,EAAE,SAAS,UAAU,IAAI,IAAI,KAAK,WAAW,KAAK,OAAK,EAAE,SAAS,UAAU,IAAI;AAChJ,QAAI,KAAK;AACP,YAAM,SAAS,IAAI,KAAK,KAAK,OAAK,EAAE,SAAS,OAAO;AACpD,UAAI,QAAQ;AACV,eAAO,yBAAyB,MAAM;AAAA,MACxC;AAAA,IACF;AACA,UAAM,IAAI,MAAM,iDAAiD;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,KAAK,WAAW;AAC9B,UAAM,kBAAkB,KAAK,YAAY,GAAG;AAC5C,UAAM,aAAa,QAAQ,eAAe,KAAK,aAAa,KAAK;AACjE,UAAM,QAAQ,gBAAgB,MAAM,UAAU,OAAK,EAAE,SAAS,UAAU,IAAI;AAC5E,QAAI,UAAU,IAAI;AAChB,sBAAgB,SAAS,KAAK;AAC9B,iBAAW,OAAO,OAAO,CAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,iBAAiB,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,IACjO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,QAAQ,wBAAwB,GAAG,CAAC,GAAG,sBAAsB,YAAY,0BAA0B,qBAAqB,GAAG,CAAC,cAAc,kBAAkB,GAAG,CAAC,SAAS,mBAAmB,GAAG,YAAY,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,WAAW,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,OAAO,QAAQ,GAAG,OAAO,GAAG,CAAC,MAAM,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,GAAG,UAAU,GAAG,CAAC,mBAAmB,eAAe,GAAG,kBAAkB,GAAG,YAAY,OAAO,GAAG,CAAC,OAAO,YAAY,GAAG,OAAO,GAAG,CAAC,mBAAmB,UAAU,GAAG,CAAC,OAAO,UAAU,GAAG,OAAO,GAAG,CAAC,mBAAmB,QAAQ,GAAG,CAAC,OAAO,cAAc,GAAG,OAAO,GAAG,CAAC,MAAM,cAAc,QAAQ,QAAQ,mBAAmB,cAAc,GAAG,UAAU,GAAG,CAAC,OAAO,yBAAyB,GAAG,SAAS,SAAS,GAAG,CAAC,MAAM,yBAAyB,QAAQ,UAAU,OAAO,KAAK,OAAO,OAAO,mBAAmB,yBAAyB,GAAG,UAAU,GAAG,CAAC,OAAO,cAAc,GAAG,SAAS,SAAS,GAAG,CAAC,MAAM,cAAc,QAAQ,UAAU,OAAO,KAAK,OAAO,WAAW,mBAAmB,cAAc,GAAG,UAAU,GAAG,CAAC,iBAAiB,gBAAgB,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,cAAc,oBAAoB,GAAG,WAAW,YAAY,GAAG,CAAC,iBAAiB,cAAc,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,iBAAiB,WAAW,GAAG,OAAO,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,mBAAmB,GAAG,YAAY,SAAS,GAAG,kBAAkB,GAAG,CAAC,QAAQ,YAAY,aAAa,IAAI,QAAQ,WAAW,mBAAmB,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,iBAAiB,gBAAgB,GAAG,OAAO,GAAG,CAAC,cAAc,aAAa,GAAG,gBAAgB,yBAAyB,UAAU,GAAG,CAAC,GAAG,UAAU,YAAY,YAAY,aAAa,uBAAuB,iBAAiB,GAAG,CAAC,sBAAsB,IAAI,GAAG,OAAO,aAAa,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,eAAe,aAAa,GAAG,CAAC,QAAQ,UAAU,mBAAmB,IAAI,SAAS,aAAa,GAAG,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,QAAQ,UAAU,mBAAmB,IAAI,GAAG,aAAa,GAAG,OAAO,GAAG,CAAC,GAAG,UAAU,YAAY,aAAa,YAAY,uBAAuB,iBAAiB,CAAC;AAAA,MAC5pE,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,gBAAgB,EAAE,GAAG,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,yBAAyB,CAAC;AACxH,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,sBAAsB,SAAS,sFAAsF,QAAQ;AACzI,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,UAC/C,CAAC;AACD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,GAAG,cAAc;AACnC,UAAG,UAAU,GAAG,wBAAwB,CAAC;AACzC,UAAG,WAAW,IAAI,6CAA6C,GAAG,GAAG,UAAU,CAAC;AAChF,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,WAAW,IAAI,kDAAkD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC3H,UAAG,UAAU,IAAI,gCAAgC,CAAC;AAClD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,QAAQ,CAAC,EAAE,IAAI,wBAAwB,EAAE,IAAI,yBAAyB;AAC5F,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,YAAY,CAAC,EAAE,IAAI,+CAA+C,GAAG,GAAG,YAAY,CAAC;AAC5J,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,gBAAgB,EAAE,IAAI,UAAU,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,kBAAkB,CAAC;AAC7F,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,OAAO,IAAI,eAAe;AAC7B,UAAG,aAAa;AAChB,UAAG,UAAU,IAAI,wBAAwB,EAAE;AAC3C,UAAG,OAAO,IAAI,eAAe;AAC7B,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,UAAU,IAAI,uBAAuB,EAAE;AAC1C,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,UAAU,IAAI,uBAAuB,EAAE;AAC1C,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,OAAO,IAAI,eAAe;AAC7B,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,OAAO,IAAI,eAAe;AAC7B,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,UAAU,IAAI,SAAS,EAAE;AAC5B,UAAG,OAAO,IAAI,eAAe;AAC7B,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,WAAW,IAAI,+CAA+C,GAAG,GAAG,YAAY,EAAE;AACrF,UAAG,UAAU,IAAI,oCAAoC,EAAE;AACvD,UAAG,eAAe,IAAI,YAAY,EAAE;AACpC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,OAAO,EAAE;AAC3E,UAAG,eAAe,IAAI,KAAK;AAC3B,UAAG,WAAW,IAAI,mDAAmD,GAAG,GAAG,gBAAgB,CAAC;AAC5F,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,YAAY,EAAE;AACpC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,OAAO,EAAE;AAC3E,UAAG,eAAe,IAAI,KAAK;AAC3B,UAAG,WAAW,IAAI,mDAAmD,GAAG,GAAG,gBAAgB,CAAC;AAC5F,UAAG,aAAa,EAAE,EAAE,EAAE,EAAE;AAAA,QAC1B;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,mBAAsB,YAAY,EAAE;AAC1C,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAe,YAAY,GAAG,IAAI,IAAI,MAAM,CAAC,EAAE,0BAA6B,YAAY,GAAG,IAAI,IAAI,mBAAmB,CAAC,EAAE,uBAA0B,YAAY,GAAG,IAAI,IAAI,aAAa,CAAC;AACtM,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,EAAE,YAAY,gBAAgB;AACtF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,aAAa,IAAI,UAAU;AACzC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,iBAAiB;AACnD,UAAG,UAAU;AACb,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC;AACzD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,aAAa,CAAC;AAC5D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,CAAI,YAAY,IAAI,IAAI,iBAAiB,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,CAAI,YAAY,IAAI,IAAI,iBAAiB,CAAC,EAAE,SAAY,YAAY,IAAI,IAAI,oBAAoB,CAAC;AAC3H,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,qBAAqB,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,mBAAmB,CAAC;AAClE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,uBAAuB,CAAC;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,CAAI,YAAY,IAAI,IAAI,iBAAiB,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,8BAA8B,CAAC,EAAE,WAAc,YAAY,IAAI,IAAI,sCAAsC,CAAC;AACxJ,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,CAAI,YAAY,IAAI,IAAI,iBAAiB,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,uBAAuB,CAAC,EAAE,WAAc,YAAY,IAAI,IAAI,+BAA+B,CAAC;AAC1I,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,CAAI,YAAY,IAAI,IAAI,iBAAiB,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,aAAa,MAAM;AAC7C,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,EAAE,cAAc,IAAI,UAAU;AAClE,UAAG,UAAU;AACb,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,sBAAsB,CAAC;AACrE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAW,IAAI,UAAU;AACvC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,iBAAiB;AACnD,UAAG,UAAU;AACb,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,mBAAmB,CAAC;AAClE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAW,IAAI,OAAO;AACpC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,iBAAiB;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAqB,UAAa,aAAgB,oBAAuB,SAAY,MAAS,eAAkB,sBAAyB,qBAAwB,8BAAiC,iBAAoB,sBAAyB,cAAiB,cAAiB,oBAAuB,iBAAoB,eAAkB,eAAkB,oBAAuB,wBAA2B,yBAA4B,gCAAmC,4BAA+B,oBAAuB,2BAA8B,2BAA8B,yBAA4B,mBAAsB,uBAA0B,0BAA6B,uBAA0B,wBAA2B,yBAA4B,yBAA4B,6BAAgC,oCAAuC,oBAAuB,yBAA4B,2BAA8B,4BAA+B,eAAkB,WAAc,eAAkB,iBAAiB;AAAA,MACriC,QAAQ,CAAC,mDAAmD;AAAA,MAC5D,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,kCAAkC;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,+BAA+B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQrC,IAAM,iCAAiC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQvC,IAAM,6BAA6B,uBAAuB;AAAA,EACxD,UAAU;AAAA,EACV,oBAAoB,WAAW;AAAA,EAC/B,aAAa,UAAQ,KAAK;AAAA,EAC1B,YAAY,CAAC,aAAa,QAAQ,YAAY,UAAU,iBAAiB,GAAG,EAAE,KAAK,IAAI,SAAO,IAAI,gBAAgB,CAAC;AACrH,CAAC;AACD,IAAM,sCAAsC,gCAAgC;AAAA,EAC1E,UAAU;AAAA,EACV,oBAAoB,WAAW;AAAA,EAC/B,aAAa,UAAQ,KAAK;AAAA,EAC1B,qBAAqB,CAAC,aAAa,cAAc,eAAe;AAC9D,WAAO,WAAW,IAAI,eAAa,YAAY,OAAO,mCAAmC;AAAA,MACvF,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC,EAAE,KAAK,IAAI,SAAO,IAAI,yBAAyB,CAAC,CAAC;AAAA,EACpD;AACF,CAAC;AACD,IAAM,wCAAwC,kCAAkC;AAAA,EAC9E,UAAU;AAAA,EACV,oBAAoB,WAAW;AAAA,EAC/B,aAAa,UAAQ,KAAK;AAAA,EAC1B,uBAAuB,CAAC,aAAa,cAAc,cAAc,YAAY,OAAO,qCAAqC;AAAA,IACvH,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC,EAAE,KAAK,IAAI,SAAO,IAAI,2BAA2B,CAAC;AACrD,CAAC;AACD,IAAM,gCAAgC;AAAA,EACpC,UAAU;AAAA,EACV,OAAO,OAAO,kBAAkB;AAAA,EAChC,MAAM;AAAA,EACN,SAAS,CAAC;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,eAAe,SAAS,IAAI,YAAY;AAC9C,iBAAa,cAAc,gCAAgC;AAAA,MACzD,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,OAAO,OAAO,gCAAgC;AAAA,QAC9C,eAAe,YAAU,OAAO;AAAA,MAClC;AAAA,IACF,CAAC,EAAE,UAAU,YAAU;AACrB,UAAI,QAAQ;AACV,uBAAe;AACf,sBAAc,QAAQ;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASrB,kBAAkB;AAAA;AAExB,IAAM,yBAAN,MAAM,gCAA+B,uBAAuB;AAAA,EAC1D,cAAc;AACZ,UAAM;AACN,SAAK,kBAAkB;AACvB,SAAK,eAAe,KAAK,qBAAqB,WAAW;AACzD,SAAK,UAAU,KAAK,uBAAuB,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,CAAC;AAAA,MACtF,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,qBAAqB;AAAA,MACnC,aAAa;AAAA,IACf,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,mBAAmB;AAAA,MACjC,aAAa;AAAA,IACf,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,gBAAgB;AAAA,MAC9B,aAAa;AAAA,IACf,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,aAAa;AAAA,MAC3B,aAAa;AAAA,IACf,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,uBAAuB;AAAA,MACrC,aAAa;AAAA,IACf,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,oBAAoB;AAAA,MAClC,aAAa;AAAA,IACf,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,8BAA8B;AAAA,MAC5C,aAAa;AAAA,IACf,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,OAAO,OAAO,uBAAuB;AAAA,MACrC,aAAa;AAAA,IACf,CAAC,CAAC,EAAE,sBAAsB,KAAK,YAAY,EAAE,eAAe,KAAK,KAAK;AACtE,SAAK,QAAQ,KAAK,qBAAqB,EAAE,YAAY,aAAa,MAAM,EAAE,SAAS,CAAC;AAAA,MAClF,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC,CAAC,EAAE,oBAAoB,KAAK,YAAY,EAAE,eAAe,KAAK,KAAK;AACpE,UAAM,UAAU;AAAA,MACd,UAAU;AAAA,MACV,UAAU,UAAQ,KAAK;AAAA,MACvB,cAAc,CAAC,MAAMA,UAAS,KAAK,mBAAmB,MAAMA,OAAM,KAAK,kBAAkB,KAAK;AAAA,MAC9F,sBAAsB,CAAC,KAAK,QAAQ,cAAc,KAAK,MAAM,YAAY;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,MAAMA,OAAM,YAAY;AACzC,UAAM,SAAS,KAAK,QAAQ,kBAAkB;AAC9C,UAAM,OAAO,KAAK,MAAM,gBAAgB;AACxC,QAAI,iBAAiB,gBAAgB;AACrC,QAAI,YAAY;AACd,aAAO,aAAa;AAAA,QAClB,UAAU;AAAA,MACZ;AACA,aAAO,OAAO;AAAA,QACZ,UAAU;AAAA,MACZ;AACA,uBAAiB,gBAAgB;AAAA,IACnC;AACA,WAAO;AAAA,MACL,SAAS;AAAA,QACP;AAAA,QACA,MAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,sBAAsB,0BAA0B,qBAAqB,GAAG,CAAC,cAAc,gBAAgB,GAAG,CAAC,SAAS,mBAAmB,GAAG,cAAc,GAAG,kBAAkB,GAAG,CAAC,GAAG,cAAc,sBAAsB,wBAAwB,MAAM,SAAS,gBAAgB,cAAc,eAAe,SAAS,GAAG,CAAC,cAAc,kBAAkB,GAAG,iBAAiB,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,uBAAuB,GAAG,CAAC,MAAM,MAAM,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,mBAAmB,MAAM,GAAG,CAAC,MAAM,cAAc,GAAG,WAAW,mBAAmB,MAAM,GAAG,CAAC,MAAM,QAAQ,GAAG,WAAW,YAAY,MAAM,GAAG,CAAC,MAAM,WAAW,GAAG,SAAS,GAAG,CAAC,MAAM,eAAe,GAAG,WAAW,MAAM,GAAG,CAAC,MAAM,aAAa,GAAG,WAAW,MAAM,GAAG,CAAC,MAAM,WAAW,GAAG,WAAW,MAAM,GAAG,CAAC,MAAM,sBAAsB,GAAG,WAAW,QAAQ,iBAAiB,GAAG,CAAC,MAAM,eAAe,GAAG,WAAW,QAAQ,iBAAiB,GAAG,CAAC,GAAG,eAAe,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,YAAY,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,YAAY,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,aAAa,WAAW,GAAG,MAAM,GAAG,CAAC,aAAa,WAAW,GAAG,MAAM,GAAG,CAAC,aAAa,SAAS,GAAG,CAAC,aAAa,SAAS,GAAG,CAAC,GAAG,eAAe,OAAO,CAAC;AAAA,MACtxC,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,gBAAgB,EAAE,GAAG,aAAa,EAAE,GAAG,yBAAyB,CAAC;AAC3G,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,sBAAsB,SAAS,oFAAoF,QAAQ;AACvI,mBAAO,IAAI,YAAY,MAAM;AAAA,UAC/B,CAAC;AACD,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,GAAG,cAAc;AACnC,UAAG,UAAU,GAAG,wBAAwB,CAAC;AACzC,UAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,KAAK,CAAC;AAClE,UAAG,UAAU,GAAG,gCAAgC,CAAC;AACjD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,oBAAoB,CAAC;AAC3C,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,WAAW,cAAc,SAAS,wEAAwE,QAAQ;AACnH,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC,EAAE,sBAAsB,SAAS,gFAAgF,QAAQ;AACxH,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC,EAAE,wBAAwB,SAAS,kFAAkF,QAAQ;AAC5H,mBAAO,IAAI,kBAAkB,MAAM;AAAA,UACrC,CAAC;AACD,UAAG,UAAU,IAAI,wBAAwB,CAAC,EAAE,IAAI,kBAAkB,CAAC;AACnE,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,gDAAgD,GAAG,GAAG,aAAa;AACrF,UAAG,aAAa;AAChB,UAAG,WAAW,IAAI,gEAAgE,GAAG,GAAG,+BAA+B,EAAE;AACzH,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA6B,YAAY,GAAG,IAAI,IAAI,mBAAmB,CAAC,EAAE,uBAA0B,YAAY,GAAG,IAAI,IAAI,gBAAgB,CAAC;AAC1J,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,iBAAiB;AACnD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,MAAM,IAAI,eAAe,EAAE,SAAY,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,EAAE,gBAAmB,YAAY,IAAI,IAAI,IAAI,aAAa,CAAC,EAAE,cAAiB,YAAY,IAAI,IAAI,IAAI,WAAW,CAAC,EAAE,eAAkB,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,EAAE,WAAW,IAAI,OAAO;AACvR,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,iBAAiB,GAAG,EAAE,oBAAoB,IAAI,gBAAgB;AAC5E,UAAG,UAAU;AACb,UAAG,WAAW,qBAAqB,IAAI,iBAAiB,EAAE,yBAA4B,YAAY,IAAI,IAAI,yCAAyC,CAAC;AACpJ,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,WAAW,CAAC,EAAE,mBAAmB,IAAI;AACrF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI,EAAE,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC;AACjI,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI,EAAE,QAAQ,IAAI,MAAM,IAAI,WAAW,CAAC;AACjI,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,aAAa,CAAC,EAAE,YAAY,KAAK,EAAE,QAAQ,IAAI,MAAM,IAAI,MAAM,CAAC;AAChH,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,gBAAgB,CAAC;AACjE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,uBAAuB,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI,YAAY,CAAC;AAC7G,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,qBAAqB,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI,UAAU,CAAC;AACzG,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI,QAAQ,CAAC;AACrG,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,8BAA8B,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI,uBAAuB,CAAC,EAAE,mBAAmB,IAAI;AACxJ,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,uBAAuB,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI,YAAY,CAAC,EAAE,mBAAmB,IAAI;AACtI,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAW,IAAI,YAAY;AAAA,QAC3C;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAqB,SAAY,MAAW,YAAe,oBAAuB,wBAA2B,yBAA4B,gCAAmC,eAAkB,2BAA8B,wBAA2B,yBAA4B,yBAA4B,qBAAwB,2BAA8B,2BAA8B,qCAAwC,oBAAuB,WAAc,eAAkB,cAAc;AAAA,MAClgB,QAAQ,CAAC,gHAAgH;AAAA,MACzH,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,4EAA4E;AAAA,IACvF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,eAAe,iBAAe,CAAC;AAAA,EACnC,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY,OAAO,uBAAuB;AAAA,EAC5C;AAAA,EACA,UAAU,YAAY,iBAAiB,gBAAgB;AACzD,GAAG;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY;AAAA,MACV,OAAO,OAAO,uBAAuB;AAAA,MACrC,MAAM,CAAC,OAAO,YAAY;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,UAAU,YAAY,iBAAiB,kBAAkB;AAC3D,CAAC;AACD,SAAS,oBAAoB,MAAM,QAAQ;AACzC,SAAO,iBAAiB;AAAA,IACtB,QAAQ,KAAK;AAAA,IACb,IAAI,OAAO;AAAA,IACX,eAAe;AAAA,IACf,SAAS,eAAa,UAAU;AAAA,IAChC,OAAO;AAAA,EACT,CAAC;AACH;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,kCAAkC;AAAA,EACzC;AAAA,EACA,YAAY,2BAA2B,aAAa;AAClD,QAAI,iBAAgB,iCAAiC;AACnD;AAAA,IACF;AACA,8BAA0B,mBAAmB,mCAAmC;AAChF,8BAA0B,mBAAmB,6BAA6B;AAC1E,8BAA0B,mBAAmB,qCAAqC;AAClF,8BAA0B,mBAAmB,0BAA0B;AACvE,gBAAY,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK,OAAO,uBAAuB;AAAA,MACnC,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC;AACD,gBAAY,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK,OAAO,qBAAqB;AAAA,MACjC,OAAO;AAAA,MACP,WAAW,4BAA4B;AAAA,QACrC,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,gBAAgB,YAAU,CAAC;AAAA,UACzB,OAAO,SAAS,OAAO,OAAO,OAAO,gCAAgC;AAAA,UACrE,MAAM,CAAC,QAAQ,EAAE;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,qBAAgB,kCAAkC;AAAA,EACpD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAY,yBAAyB,GAAM,SAAY,WAAW,CAAC;AAAA,IAC1H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,wBAAwB,wBAAwB;AAAA,MAC/D,SAAS,CAAC,cAAmB,cAAc,cAAc,WAAW,YAAY;AAAA,IAClF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY,iBAAe,aAAa,WAAW;AAAA,QACnD,OAAO;AAAA,QACP,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,MACD,SAAS,CAAC,cAAc,aAAa,SAAS,CAAC,CAAC,GAAG,cAAc,YAAY;AAAA,IAC/E,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,aAAa,SAAS,CAAC,CAAC,GAAG,cAAc,WAAW,YAAY;AAAA,MACxF,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY,iBAAe,aAAa,WAAW;AAAA,QACnD,OAAO;AAAA,QACP,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,MACD,cAAc,CAAC,wBAAwB,wBAAwB;AAAA,IACjE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": ["take"]}