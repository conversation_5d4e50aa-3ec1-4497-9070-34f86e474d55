{"version": 3, "sources": ["../../../../../../../node_modules/@vendure/admin-ui/fesm2022/vendure-admin-ui-system.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ChangeDetectionStrategy, Component, Input, NgModule } from '@angular/core';\nimport * as i1 from '@vendure/admin-ui/core';\nimport { JobState, BaseListComponent, SortOrder, SharedModule } from '@vendure/admin-ui/core';\nimport * as i3 from '@clr/angular';\nimport * as i3$1 from '@angular/common';\nimport * as i4 from '@ngx-translate/core';\nimport { _ } from '@ngx-translate/core';\nimport * as i5 from '@angular/forms';\nimport { FormControl } from '@angular/forms';\nimport { timer } from 'rxjs';\nimport { takeUntil, filter, map } from 'rxjs/operators';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i6 from '@ng-select/ng-select';\nimport { gql } from 'apollo-angular';\nimport { marker } from '@biesbjerg/ngx-translate-extract-marker';\nconst _c0 = (a0, a1) => ({\n  \"is-success\": a0,\n  \"is-danger\": a1\n});\nfunction HealthCheckComponent_div_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"system.health-all-systems-up\"), \" \");\n  }\n}\nfunction HealthCheckComponent_div_6_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, \"system.health-error\"), \" \");\n  }\n}\nfunction HealthCheckComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n    i0.ɵɵelement(2, \"clr-icon\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 12);\n    i0.ɵɵtemplate(4, HealthCheckComponent_div_6_ng_container_4_Template, 3, 3, \"ng-container\", 13)(5, HealthCheckComponent_div_6_ng_template_5_Template, 2, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵpipe(11, \"localeDate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const status_r1 = ctx.ngIf;\n    const error_r2 = i0.ɵɵreference(6);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(13, _c0, status_r1 === \"ok\", status_r1 !== \"ok\"));\n    i0.ɵɵattribute(\"shape\", status_r1 === \"ok\" ? \"check-circle\" : \"exclamation-circle\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", status_r1 === \"ok\")(\"ngIfElse\", error_r2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(9, 6, \"system.health-last-checked\"), \": \", i0.ɵɵpipeBind2(11, 10, i0.ɵɵpipeBind1(10, 8, ctx_r2.healthCheckService.lastCheck$), \"mediumTime\"), \" \");\n  }\n}\nfunction HealthCheckComponent_tr_28_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"clr-icon\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"system.health-status-up\"), \" \");\n  }\n}\nfunction HealthCheckComponent_tr_28_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"clr-icon\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"system.health-status-down\"), \" \");\n  }\n}\nfunction HealthCheckComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 15)(4, \"vdr-chip\", 16);\n    i0.ɵɵtemplate(5, HealthCheckComponent_tr_28_ng_container_5_Template, 4, 3, \"ng-container\", 13)(6, HealthCheckComponent_tr_28_ng_template_6_Template, 3, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\", 15);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r4 = ctx.$implicit;\n    const down_r5 = i0.ɵɵreference(7);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r4.key);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"colorType\", row_r4.result.status === \"up\" ? \"success\" : \"error\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", row_r4.result.status === \"up\")(\"ngIfElse\", down_r5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(row_r4.result.message);\n  }\n}\nfunction JobStateLabelComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"percent\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, ctx_r0.job.progress / 100), \" \");\n  }\n}\nconst _c1 = () => [\"DeleteSettings\", \"DeleteSystem\"];\nfunction JobListComponent_ng_template_40_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"vdr-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"system.all-job-queues\"));\n  }\n}\nfunction JobListComponent_ng_template_40_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().item;\n    i0.ɵɵproperty(\"colorFrom\", item_r1.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.name);\n  }\n}\nfunction JobListComponent_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, JobListComponent_ng_template_40_ng_container_0_Template, 4, 3, \"ng-container\", 19)(1, JobListComponent_ng_template_40_ng_template_1_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.item;\n    const others_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngIf\", item_r1.name === \"all\")(\"ngIfElse\", others_r2);\n  }\n}\nfunction JobListComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const job_r3 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", job_r3.id, \" \");\n  }\n}\nfunction JobListComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"timeAgo\");\n  }\n  if (rf & 2) {\n    const job_r4 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, job_r4.createdAt), \" \");\n  }\n}\nfunction JobListComponent_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-chip\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r5 = ctx.item;\n    i0.ɵɵproperty(\"colorFrom\", job_r5.queueName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(job_r5.queueName);\n  }\n}\nfunction JobListComponent_ng_template_57_vdr_dropdown_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 22);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelement(3, \"clr-icon\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"vdr-dropdown-menu\")(5, \"div\", 24);\n    i0.ɵɵelement(6, \"vdr-object-tree\", 5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const job_r6 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(2, 2, \"system.job-data\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", job_r6.data);\n  }\n}\nfunction JobListComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, JobListComponent_ng_template_57_vdr_dropdown_0_Template, 7, 4, \"vdr-dropdown\", 21);\n  }\n  if (rf & 2) {\n    const job_r6 = ctx.item;\n    i0.ɵɵproperty(\"ngIf\", job_r6.data);\n  }\n}\nfunction JobListComponent_ng_template_60_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r7 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"after \", job_r7.attempts, \" attempts\");\n  }\n}\nfunction JobListComponent_ng_template_60_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const job_r7 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" attempting \", job_r7.attempts, \" of \", job_r7.retries + 1, \" \");\n  }\n}\nfunction JobListComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-job-state-label\", 25);\n    i0.ɵɵtemplate(1, JobListComponent_ng_template_60_div_1_Template, 2, 1, \"div\", 26)(2, JobListComponent_ng_template_60_div_2_Template, 2, 2, \"div\", 26);\n  }\n  if (rf & 2) {\n    const job_r7 = ctx.item;\n    i0.ɵɵproperty(\"job\", job_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r7.state === \"FAILED\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r7.state === \"RUNNING\" || job_r7.state === \"RETRYING\");\n  }\n}\nfunction JobListComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"duration\");\n  }\n  if (rf & 2) {\n    const job_r8 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, job_r8.duration), \" \");\n  }\n}\nfunction JobListComponent_ng_template_66_vdr_dropdown_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 28);\n    i0.ɵɵelement(2, \"clr-icon\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"vdr-dropdown-menu\")(6, \"div\", 24);\n    i0.ɵɵelement(7, \"vdr-object-tree\", 5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const job_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"system.job-result\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", job_r9.result);\n  }\n}\nfunction JobListComponent_ng_template_66_vdr_dropdown_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 29);\n    i0.ɵɵelement(2, \"clr-icon\", 30);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"vdr-dropdown-menu\")(6, \"div\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const job_r9 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"system.job-error\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", job_r9.error, \" \");\n  }\n}\nfunction JobListComponent_ng_template_66_vdr_dropdown_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 31);\n    i0.ɵɵelement(2, \"clr-icon\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"vdr-dropdown-menu\", 33)(4, \"button\", 34);\n    i0.ɵɵpipe(5, \"hasPermission\");\n    i0.ɵɵlistener(\"click\", function JobListComponent_ng_template_66_vdr_dropdown_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const job_r9 = i0.ɵɵnextContext().item;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.cancelJob(job_r9.id));\n    });\n    i0.ɵɵelement(6, \"clr-icon\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", !i0.ɵɵpipeBind1(5, 2, i0.ɵɵpureFunction0(6, _c1)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"common.cancel\"), \" \");\n  }\n}\nfunction JobListComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, JobListComponent_ng_template_66_vdr_dropdown_0_Template, 8, 4, \"vdr-dropdown\", 21)(1, JobListComponent_ng_template_66_vdr_dropdown_1_Template, 8, 4, \"vdr-dropdown\", 21)(2, JobListComponent_ng_template_66_vdr_dropdown_2_Template, 9, 7, \"vdr-dropdown\", 21);\n  }\n  if (rf & 2) {\n    const job_r9 = ctx.item;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.hasResult(job_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", job_r9.error);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !job_r9.isSettled && job_r9.state !== \"FAILED\");\n  }\n}\nconst _c2 = () => [\"UpdateSettings\", \"UpdateSystem\"];\nfunction ScheduledTaskListComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const task_r1 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", task_r1.id, \" \");\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const task_r2 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", task_r2.description, \" \");\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r3 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(task_r3.scheduleDescription);\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_20_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵpipe(1, \"localeDate\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"timeAgo\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r4 = i0.ɵɵnextContext().item;\n    i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(1, 2, task_r4.lastExecutedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, task_r4.lastExecutedAt), \" \");\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ScheduledTaskListComponent_ng_template_20_span_0_Template, 4, 6, \"span\", 10);\n  }\n  if (rf & 2) {\n    const task_r4 = ctx.item;\n    i0.ɵɵproperty(\"ngIf\", task_r4.lastExecutedAt);\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeDate\");\n  }\n  if (rf & 2) {\n    const task_r5 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, task_r5.nextExecutionAt), \" \");\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_26_vdr_dropdown_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 13);\n    i0.ɵɵelement(2, \"clr-icon\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"vdr-dropdown-menu\")(6, \"div\", 15);\n    i0.ɵɵelement(7, \"vdr-object-tree\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const task_r6 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"system.last-result\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", task_r6.lastResult);\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ScheduledTaskListComponent_ng_template_26_vdr_dropdown_0_Template, 8, 4, \"vdr-dropdown\", 12);\n  }\n  if (rf & 2) {\n    const task_r6 = ctx.item;\n    i0.ɵɵproperty(\"ngIf\", task_r6.lastResult);\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_29_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"vdr-chip\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"colorType\", \"success\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"common.enabled\"));\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_29_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"vdr-chip\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"colorType\", \"warning\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"common.disabled\"));\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ScheduledTaskListComponent_ng_template_29_ng_container_0_Template, 4, 4, \"ng-container\", 12)(1, ScheduledTaskListComponent_ng_template_29_ng_container_1_Template, 4, 4, \"ng-container\", 12);\n  }\n  if (rf & 2) {\n    const task_r7 = ctx.item;\n    i0.ɵɵproperty(\"ngIf\", task_r7.enabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !task_r7.enabled);\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵpipe(1, \"hasPermission\");\n    i0.ɵɵlistener(\"click\", function ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const task_r10 = i0.ɵɵnextContext(2).item;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.runTask(task_r10));\n    });\n    i0.ɵɵelement(2, \"clr-icon\", 25);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"disabled\", !i0.ɵɵpipeBind1(1, 2, i0.ɵɵpureFunction0(6, _c2)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"system.run-task\"), \" \");\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"common.disable\"), \" \");\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"common.enable\"), \" \");\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-dropdown\")(1, \"button\", 18);\n    i0.ɵɵelement(2, \"clr-icon\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"vdr-dropdown-menu\", 20);\n    i0.ɵɵtemplate(4, ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_button_4_Template, 6, 7, \"button\", 21);\n    i0.ɵɵelementStart(5, \"button\", 22);\n    i0.ɵɵpipe(6, \"hasPermission\");\n    i0.ɵɵlistener(\"click\", function ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const task_r10 = i0.ɵɵnextContext().item;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.toggleEnabled(task_r10));\n    });\n    i0.ɵɵelement(7, \"clr-icon\", 23);\n    i0.ɵɵtemplate(8, ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_div_8_Template, 3, 3, \"div\", 12)(9, ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_div_9_Template, 3, 3, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const task_r10 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", task_r10.enabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !i0.ɵɵpipeBind1(6, 4, i0.ɵɵpureFunction0(6, _c2)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", task_r10.enabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !task_r10.enabled);\n  }\n}\nfunction ScheduledTaskListComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ScheduledTaskListComponent_ng_template_31_vdr_dropdown_0_Template, 10, 7, \"vdr-dropdown\", 12);\n  }\n  if (rf & 2) {\n    const task_r10 = ctx.item;\n    i0.ɵɵproperty(\"ngIf\", !task_r10.isSettled && task_r10.state !== \"FAILED\");\n  }\n}\nconst _c3 = \".result-detail[_ngcontent-%COMP%]{margin:0 12px}.retry-info[_ngcontent-%COMP%]{margin-inline-start:6px;color:var(--color-grey-400)}\";\nclass HealthCheckComponent {\n  constructor(healthCheckService) {\n    this.healthCheckService = healthCheckService;\n  }\n  static {\n    this.ɵfac = function HealthCheckComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HealthCheckComponent)(i0.ɵɵdirectiveInject(i1.HealthCheckService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: HealthCheckComponent,\n      selectors: [[\"vdr-health-check\"]],\n      standalone: false,\n      decls: 30,\n      vars: 18,\n      consts: [[\"error\", \"\"], [\"down\", \"\"], [\"class\", \"system-status-header\", 4, \"ngIf\"], [\"locationId\", \"system-status\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"shape\", \"refresh\"], [1, \"table\"], [1, \"left\"], [4, \"ngFor\", \"ngForOf\"], [1, \"system-status-header\"], [1, \"status-icon\"], [\"size\", \"48\", 3, \"ngClass\"], [1, \"status-detail\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"last-checked\"], [1, \"align-middle\", \"left\"], [3, \"colorType\"], [\"shape\", \"check-circle\"], [\"shape\", \"exclamation-circle\"]],\n      template: function HealthCheckComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-header\");\n          i0.ɵɵelement(1, \"vdr-page-title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"vdr-page-body\")(3, \"vdr-page-block\")(4, \"vdr-action-bar\")(5, \"vdr-ab-left\");\n          i0.ɵɵtemplate(6, HealthCheckComponent_div_6_Template, 12, 16, \"div\", 2);\n          i0.ɵɵpipe(7, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"vdr-ab-right\");\n          i0.ɵɵelement(9, \"vdr-action-bar-items\", 3);\n          i0.ɵɵelementStart(10, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function HealthCheckComponent_Template_button_click_10_listener() {\n            return ctx.healthCheckService.refresh();\n          });\n          i0.ɵɵelement(11, \"clr-icon\", 5);\n          i0.ɵɵtext(12);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"vdr-page-block\")(15, \"table\", 6)(16, \"thead\")(17, \"tr\")(18, \"th\", 7);\n          i0.ɵɵtext(19);\n          i0.ɵɵpipe(20, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"th\", 7);\n          i0.ɵɵtext(22);\n          i0.ɵɵpipe(23, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\", 7);\n          i0.ɵɵtext(25);\n          i0.ɵɵpipe(26, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"tbody\");\n          i0.ɵɵtemplate(28, HealthCheckComponent_tr_28_Template, 10, 5, \"tr\", 8);\n          i0.ɵɵpipe(29, \"async\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(7, 6, ctx.healthCheckService.status$));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 8, \"system.health-refresh\"), \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 10, \"common.name\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 12, \"system.health-status\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 14, \"system.health-message\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(29, 16, ctx.healthCheckService.details$));\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgClass, i3$1.NgForOf, i3$1.NgIf, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ChipComponent, i1.ActionBarItemsComponent, i1.PageHeaderComponent, i1.PageTitleComponent, i1.PageBodyComponent, i1.PageBlockComponent, i3$1.AsyncPipe, i4.TranslatePipe, i1.LocaleDatePipe],\n      styles: [\".system-status-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start}.system-status-header[_ngcontent-%COMP%]   .status-detail[_ngcontent-%COMP%]{font-weight:700;margin-inline-end:6px}.system-status-header[_ngcontent-%COMP%]   .last-checked[_ngcontent-%COMP%]{font-weight:400;color:var(--color-grey-500)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HealthCheckComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-health-check',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-header>\\n    <vdr-page-title></vdr-page-title>\\n</vdr-page-header>\\n<vdr-page-body>\\n    <vdr-page-block>\\n        <vdr-action-bar>\\n            <vdr-ab-left>\\n                <div class=\\\"system-status-header\\\" *ngIf=\\\"healthCheckService.status$ | async as status\\\">\\n                    <div class=\\\"status-icon\\\">\\n                        <clr-icon\\n                            [attr.shape]=\\\"status === 'ok' ? 'check-circle' : 'exclamation-circle'\\\"\\n                            [ngClass]=\\\"{ 'is-success': status === 'ok', 'is-danger': status !== 'ok' }\\\"\\n                            size=\\\"48\\\"\\n                        ></clr-icon>\\n                    </div>\\n                    <div class=\\\"status-detail\\\">\\n                        <ng-container *ngIf=\\\"status === 'ok'; else error\\\">\\n                            {{ 'system.health-all-systems-up' | translate }}\\n                        </ng-container>\\n                        <ng-template #error>\\n                            {{ 'system.health-error' | translate }}\\n                        </ng-template>\\n                        <div class=\\\"last-checked\\\">\\n                            {{ 'system.health-last-checked' | translate }}:\\n                            {{ healthCheckService.lastCheck$ | async | localeDate : 'mediumTime' }}\\n                        </div>\\n                    </div>\\n                </div>\\n            </vdr-ab-left>\\n            <vdr-ab-right>\\n                <vdr-action-bar-items locationId=\\\"system-status\\\"></vdr-action-bar-items>\\n                <button class=\\\"btn btn-secondary\\\" (click)=\\\"healthCheckService.refresh()\\\">\\n                    <clr-icon shape=\\\"refresh\\\"></clr-icon> {{ 'system.health-refresh' | translate }}\\n                </button>\\n            </vdr-ab-right>\\n        </vdr-action-bar>\\n    </vdr-page-block>\\n    <vdr-page-block>\\n        <table class=\\\"table\\\">\\n            <thead>\\n                <tr>\\n                    <th class=\\\"left\\\">\\n                        {{ 'common.name' | translate }}\\n                    </th>\\n                    <th class=\\\"left\\\">\\n                        {{ 'system.health-status' | translate }}\\n                    </th>\\n                    <th class=\\\"left\\\">\\n                        {{ 'system.health-message' | translate }}\\n                    </th>\\n                </tr>\\n            </thead>\\n            <tbody>\\n                <tr *ngFor=\\\"let row of healthCheckService.details$ | async\\\">\\n                    <td class=\\\"align-middle left\\\">{{ row.key }}</td>\\n                    <td class=\\\"align-middle left\\\">\\n                        <vdr-chip [colorType]=\\\"row.result.status === 'up' ? 'success' : 'error'\\\">\\n                            <ng-container *ngIf=\\\"row.result.status === 'up'; else down\\\">\\n                                <clr-icon shape=\\\"check-circle\\\"></clr-icon>\\n                                {{ 'system.health-status-up' | translate }}\\n                            </ng-container>\\n                            <ng-template #down>\\n                                <clr-icon shape=\\\"exclamation-circle\\\"></clr-icon>\\n                                {{ 'system.health-status-down' | translate }}\\n                            </ng-template>\\n                        </vdr-chip>\\n                    </td>\\n                    <td class=\\\"align-middle left\\\">{{ row.result.message }}</td>\\n                </tr>\\n            </tbody>\\n        </table>\\n    </vdr-page-block>\\n</vdr-page-body>\\n\",\n      styles: [\".system-status-header{display:flex;justify-content:space-between;align-items:flex-start}.system-status-header .status-detail{font-weight:700;margin-inline-end:6px}.system-status-header .last-checked{font-weight:400;color:var(--color-grey-500)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.HealthCheckService\n  }], null);\n})();\nclass JobStateLabelComponent {\n  get iconShape() {\n    switch (this.job.state) {\n      case JobState.COMPLETED:\n        return 'check-circle';\n      case JobState.FAILED:\n        return 'exclamation-circle';\n      case JobState.CANCELLED:\n        return 'ban';\n      case JobState.PENDING:\n      case JobState.RETRYING:\n        return 'hourglass';\n      case JobState.RUNNING:\n        return 'sync';\n    }\n  }\n  get colorType() {\n    switch (this.job.state) {\n      case JobState.COMPLETED:\n        return 'success';\n      case JobState.FAILED:\n      case JobState.CANCELLED:\n        return 'error';\n      case JobState.PENDING:\n      case JobState.RETRYING:\n        return '';\n      case JobState.RUNNING:\n        return 'warning';\n    }\n  }\n  static {\n    this.ɵfac = function JobStateLabelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || JobStateLabelComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: JobStateLabelComponent,\n      selectors: [[\"vdr-job-state-label\"]],\n      inputs: {\n        job: \"job\"\n      },\n      standalone: false,\n      decls: 5,\n      vars: 6,\n      consts: [[3, \"colorType\"], [1, \"mr1\"], [\"class\", \"progress\", 4, \"ngIf\"], [1, \"progress\"]],\n      template: function JobStateLabelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-chip\", 0);\n          i0.ɵɵelement(1, \"clr-icon\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"titlecase\");\n          i0.ɵɵtemplate(4, JobStateLabelComponent_span_4_Template, 3, 3, \"span\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"colorType\", ctx.colorType);\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"shape\", ctx.iconShape);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, ctx.job.state), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.job.state === \"RUNNING\");\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgIf, i1.ChipComponent, i3$1.PercentPipe, i3$1.TitleCasePipe],\n      styles: [\".progress[_ngcontent-%COMP%]{margin-inline-start:3px}clr-icon[_ngcontent-%COMP%]{min-width:12px}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JobStateLabelComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-job-state-label',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-chip [colorType]=\\\"colorType\\\">\\n    <clr-icon [attr.shape]=\\\"iconShape\\\" class=\\\"mr1\\\"></clr-icon>\\n    {{ job.state | titlecase }}\\n    <span *ngIf=\\\"job.state === 'RUNNING'\\\" class=\\\"progress\\\">\\n        {{ (job.progress / 100) | percent }}\\n    </span>\\n</vdr-chip>\\n\",\n      styles: [\".progress{margin-inline-start:3px}clr-icon{min-width:12px}\\n\"]\n    }]\n  }], null, {\n    job: [{\n      type: Input\n    }]\n  });\n})();\nclass JobListComponent extends BaseListComponent {\n  constructor(dataService, router, route) {\n    super(router, route);\n    this.dataService = dataService;\n    this.liveUpdate = new FormControl(true);\n    this.queueFilter = new FormControl('all');\n    this.stateFilter = new FormControl('');\n    super.setQueryFn((...args) => this.dataService.settings.getAllJobs(...args), data => data.jobs, (skip, take) => {\n      const queueFilter = this.queueFilter.value === 'all' ? null : {\n        queueName: {\n          eq: this.queueFilter.value\n        }\n      };\n      const stateFilter = this.stateFilter.value;\n      return {\n        options: {\n          skip,\n          take,\n          filter: {\n            ...queueFilter,\n            ...(stateFilter ? {\n              state: {\n                eq: stateFilter\n              }\n            } : {})\n          },\n          sort: {\n            createdAt: SortOrder.DESC\n          }\n        }\n      };\n    });\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    timer(5000, 2000).pipe(takeUntil(this.destroy$), filter(() => !!this.liveUpdate.value)).subscribe(() => {\n      this.refresh();\n    });\n    this.queues$ = this.dataService.settings.getJobQueues().mapStream(res => res.jobQueues).pipe(map(queues => [{\n      name: 'all',\n      running: true\n    }, ...queues]));\n  }\n  hasResult(job) {\n    const result = job.result;\n    if (result == null) {\n      return false;\n    }\n    if (typeof result === 'object') {\n      return Object.keys(result).length > 0;\n    }\n    return true;\n  }\n  cancelJob(id) {\n    this.dataService.settings.cancelJob(id).subscribe(() => this.refresh());\n  }\n  static {\n    this.ɵfac = function JobListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || JobListComponent)(i0.ɵɵdirectiveInject(i1.DataService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: JobListComponent,\n      selectors: [[\"vdr-job-list\"]],\n      standalone: false,\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 67,\n      vars: 80,\n      consts: [[\"others\", \"\"], [1, \"mr-2\"], [\"type\", \"checkbox\", \"clrCheckbox\", \"\", \"name\", \"live-update\", 3, \"formControl\"], [\"locationId\", \"job-list\"], [1, \"mr-2\", 3, \"change\", \"addTag\", \"hideSelected\", \"multiple\", \"markFirst\", \"clearable\", \"searchable\", \"formControl\"], [3, \"value\"], [\"colorType\", \"warning\"], [\"colorType\", \"success\"], [\"colorType\", \"error\"], [\"bindValue\", \"name\", 3, \"change\", \"addTag\", \"items\", \"hideSelected\", \"multiple\", \"markFirst\", \"clearable\", \"searchable\", \"formControl\"], [\"ng-label-tmp\", \"\", \"ng-option-tmp\", \"\"], [\"id\", \"job-list\", 3, \"pageChange\", \"itemsPerPageChange\", \"items\", \"itemsPerPage\", \"totalItems\", \"currentPage\"], [\"id\", \"id\", 3, \"heading\"], [\"id\", \"created-at\", 3, \"heading\"], [\"id\", \"job-queue-name\", 3, \"heading\", \"optional\"], [\"id\", \"job-data\", 3, \"heading\", \"optional\"], [\"id\", \"job-state\", 3, \"heading\"], [\"id\", \"job-duration\", 3, \"heading\"], [\"id\", \"job-result\", 3, \"heading\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"colorFrom\"], [4, \"ngIf\"], [\"vdrDropdownTrigger\", \"\", 1, \"button-small\", 3, \"title\"], [\"shape\", \"details\"], [1, \"result-detail\"], [3, \"job\"], [\"class\", \"retry-info\", 4, \"ngIf\"], [1, \"retry-info\"], [\"vdrDropdownTrigger\", \"\", 1, \"button-small\", \"mr-1\"], [\"vdrDropdownTrigger\", \"\", 1, \"button-small\"], [\"shape\", \"exclamation-circle\"], [\"vdrDropdownTrigger\", \"\", 1, \"icon-button\"], [\"shape\", \"ellipsis-vertical\", \"size\", \"12\"], [\"vdrPosition\", \"bottom-right\"], [\"type\", \"button\", \"vdrDropdownItem\", \"\", 1, \"delete-button\", 3, \"click\", \"disabled\"], [\"shape\", \"ban\", 1, \"is-danger\"]],\n      template: function JobListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-header\");\n          i0.ɵɵelement(1, \"vdr-page-title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"vdr-page-body\")(3, \"vdr-page-block\")(4, \"vdr-action-bar\")(5, \"vdr-ab-left\")(6, \"clr-checkbox-wrapper\", 1);\n          i0.ɵɵelement(7, \"input\", 2);\n          i0.ɵɵelementStart(8, \"label\");\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"vdr-ab-right\");\n          i0.ɵɵelement(12, \"vdr-action-bar-items\", 3);\n          i0.ɵɵelementStart(13, \"ng-select\", 4);\n          i0.ɵɵlistener(\"change\", function JobListComponent_Template_ng_select_change_13_listener() {\n            return ctx.refresh();\n          });\n          i0.ɵɵelementStart(14, \"ng-option\", 5)(15, \"vdr-chip\");\n          i0.ɵɵtext(16);\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"ng-option\", 5)(19, \"vdr-chip\");\n          i0.ɵɵtext(20);\n          i0.ɵɵpipe(21, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"ng-option\", 5)(23, \"vdr-chip\", 6);\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"ng-option\", 5)(27, \"vdr-chip\", 7);\n          i0.ɵɵtext(28);\n          i0.ɵɵpipe(29, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"ng-option\", 5)(31, \"vdr-chip\", 8);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"ng-option\", 5)(35, \"vdr-chip\", 8);\n          i0.ɵɵtext(36);\n          i0.ɵɵpipe(37, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"ng-select\", 9);\n          i0.ɵɵpipe(39, \"async\");\n          i0.ɵɵlistener(\"change\", function JobListComponent_Template_ng_select_change_38_listener() {\n            return ctx.refresh();\n          });\n          i0.ɵɵtemplate(40, JobListComponent_ng_template_40_Template, 3, 2, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"vdr-data-table-2\", 11);\n          i0.ɵɵpipe(42, \"async\");\n          i0.ɵɵpipe(43, \"async\");\n          i0.ɵɵpipe(44, \"async\");\n          i0.ɵɵpipe(45, \"async\");\n          i0.ɵɵlistener(\"pageChange\", function JobListComponent_Template_vdr_data_table_2_pageChange_41_listener($event) {\n            return ctx.setPageNumber($event);\n          })(\"itemsPerPageChange\", function JobListComponent_Template_vdr_data_table_2_itemsPerPageChange_41_listener($event) {\n            return ctx.setItemsPerPage($event);\n          });\n          i0.ɵɵelementStart(46, \"vdr-dt2-column\", 12);\n          i0.ɵɵpipe(47, \"translate\");\n          i0.ɵɵtemplate(48, JobListComponent_ng_template_48_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"vdr-dt2-column\", 13);\n          i0.ɵɵpipe(50, \"translate\");\n          i0.ɵɵtemplate(51, JobListComponent_ng_template_51_Template, 2, 3, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"vdr-dt2-column\", 14);\n          i0.ɵɵpipe(53, \"translate\");\n          i0.ɵɵtemplate(54, JobListComponent_ng_template_54_Template, 2, 2, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"vdr-dt2-column\", 15);\n          i0.ɵɵpipe(56, \"translate\");\n          i0.ɵɵtemplate(57, JobListComponent_ng_template_57_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"vdr-dt2-column\", 16);\n          i0.ɵɵpipe(59, \"translate\");\n          i0.ɵɵtemplate(60, JobListComponent_ng_template_60_Template, 3, 3, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"vdr-dt2-column\", 17);\n          i0.ɵɵpipe(62, \"translate\");\n          i0.ɵɵtemplate(63, JobListComponent_ng_template_63_Template, 2, 3, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"vdr-dt2-column\", 18);\n          i0.ɵɵpipe(65, \"translate\");\n          i0.ɵɵtemplate(66, JobListComponent_ng_template_66_Template, 3, 3, \"ng-template\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formControl\", ctx.liveUpdate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 42, \"common.live-update\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"addTag\", false)(\"hideSelected\", true)(\"multiple\", false)(\"markFirst\", false)(\"clearable\", false)(\"searchable\", false)(\"formControl\", ctx.stateFilter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 44, \"system.job-state-all\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", \"PENDING\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(21, 46, \"system.job-state-pending\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", \"RUNNING\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(25, 48, \"system.job-state-running\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", \"COMPLETED\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(29, 50, \"system.job-state-completed\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", \"FAILED\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(33, 52, \"system.job-state-failed\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", \"CANCELLED\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(37, 54, \"system.job-state-cancelled\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"addTag\", false)(\"items\", i0.ɵɵpipeBind1(39, 56, ctx.queues$))(\"hideSelected\", true)(\"multiple\", false)(\"markFirst\", false)(\"clearable\", false)(\"searchable\", false)(\"formControl\", ctx.queueFilter);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(42, 58, ctx.items$))(\"itemsPerPage\", i0.ɵɵpipeBind1(43, 60, ctx.itemsPerPage$))(\"totalItems\", i0.ɵɵpipeBind1(44, 62, ctx.totalItems$))(\"currentPage\", i0.ɵɵpipeBind1(45, 64, ctx.currentPage$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(47, 66, \"common.id\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(50, 68, \"common.created-at\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(53, 70, \"system.job-queue-name\"))(\"optional\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(56, 72, \"system.job-data\"))(\"optional\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(59, 74, \"system.job-state\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(62, 76, \"system.job-duration\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(65, 78, \"system.job-result\"));\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3.ClrLabel, i3.ClrCheckbox, i3.ClrCheckboxWrapper, i3$1.NgIf, i5.CheckboxControlValueAccessor, i5.NgControlStatus, i5.FormControlDirective, i6.NgSelectComponent, i6.NgOptionComponent, i6.NgOptionTemplateDirective, i6.NgLabelTemplateDirective, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ChipComponent, i1.FormFieldControlDirective, i1.DropdownComponent, i1.DropdownMenuComponent, i1.DropdownTriggerDirective, i1.DropdownItemDirective, i1.ObjectTreeComponent, i1.ActionBarItemsComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i1.PageHeaderComponent, i1.PageTitleComponent, i1.PageBodyComponent, i1.PageBlockComponent, JobStateLabelComponent, i3$1.AsyncPipe, i4.TranslatePipe, i1.HasPermissionPipe, i1.TimeAgoPipe, i1.DurationPipe],\n      styles: [\".result-detail[_ngcontent-%COMP%]{margin:0 12px}.retry-info[_ngcontent-%COMP%]{margin-inline-start:6px;color:var(--color-grey-400)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JobListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-job-list',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-header>\\n    <vdr-page-title></vdr-page-title>\\n</vdr-page-header>\\n<vdr-page-body>\\n    <vdr-page-block>\\n        <vdr-action-bar>\\n            <vdr-ab-left>\\n                <clr-checkbox-wrapper class=\\\"mr-2\\\">\\n                    <input type=\\\"checkbox\\\" clrCheckbox [formControl]=\\\"liveUpdate\\\" name=\\\"live-update\\\" />\\n                    <label>{{ 'common.live-update' | translate }}</label>\\n                </clr-checkbox-wrapper>\\n            </vdr-ab-left>\\n            <vdr-ab-right>\\n                <vdr-action-bar-items locationId=\\\"job-list\\\"></vdr-action-bar-items>\\n                <ng-select\\n                    class=\\\"mr-2\\\"\\n                    [addTag]=\\\"false\\\"\\n                    [hideSelected]=\\\"true\\\"\\n                    [multiple]=\\\"false\\\"\\n                    [markFirst]=\\\"false\\\"\\n                    [clearable]=\\\"false\\\"\\n                    [searchable]=\\\"false\\\"\\n                    [formControl]=\\\"stateFilter\\\"\\n                    (change)=\\\"refresh()\\\"\\n                >\\n                    <ng-option [value]=\\\"''\\\">\\n                        <vdr-chip>{{ 'system.job-state-all' | translate }}</vdr-chip>\\n                    </ng-option>\\n                    <ng-option [value]=\\\"'PENDING'\\\">\\n                        <vdr-chip>{{ 'system.job-state-pending' | translate }}</vdr-chip>\\n                    </ng-option>\\n                    <ng-option [value]=\\\"'RUNNING'\\\">\\n                        <vdr-chip colorType=\\\"warning\\\">{{ 'system.job-state-running' | translate }}</vdr-chip>\\n                    </ng-option>\\n                    <ng-option [value]=\\\"'COMPLETED'\\\">\\n                        <vdr-chip colorType=\\\"success\\\">{{ 'system.job-state-completed' | translate }}</vdr-chip>\\n                    </ng-option>\\n                    <ng-option [value]=\\\"'FAILED'\\\">\\n                        <vdr-chip colorType=\\\"error\\\">{{ 'system.job-state-failed' | translate }}</vdr-chip>\\n                    </ng-option>\\n                    <ng-option [value]=\\\"'CANCELLED'\\\">\\n                        <vdr-chip colorType=\\\"error\\\">{{ 'system.job-state-cancelled' | translate }}</vdr-chip>\\n                    </ng-option>\\n                </ng-select>\\n                <ng-select\\n                    [addTag]=\\\"false\\\"\\n                    [items]=\\\"queues$ | async\\\"\\n                    [hideSelected]=\\\"true\\\"\\n                    [multiple]=\\\"false\\\"\\n                    [markFirst]=\\\"false\\\"\\n                    [clearable]=\\\"false\\\"\\n                    [searchable]=\\\"false\\\"\\n                    bindValue=\\\"name\\\"\\n                    [formControl]=\\\"queueFilter\\\"\\n                    (change)=\\\"refresh()\\\"\\n                >\\n                    <ng-template ng-label-tmp ng-option-tmp let-item=\\\"item\\\">\\n                        <ng-container *ngIf=\\\"item.name === 'all'; else others\\\">\\n                            <vdr-chip>{{ 'system.all-job-queues' | translate }}</vdr-chip>\\n                        </ng-container>\\n                        <ng-template #others>\\n                            <vdr-chip [colorFrom]=\\\"item.name\\\">{{ item.name }}</vdr-chip>\\n                        </ng-template>\\n                    </ng-template>\\n                </ng-select>\\n            </vdr-ab-right>\\n        </vdr-action-bar>\\n    </vdr-page-block>\\n\\n    <vdr-data-table-2\\n        id=\\\"job-list\\\"\\n        [items]=\\\"items$ | async\\\"\\n        [itemsPerPage]=\\\"itemsPerPage$ | async\\\"\\n        [totalItems]=\\\"totalItems$ | async\\\"\\n        [currentPage]=\\\"currentPage$ | async\\\"\\n        (pageChange)=\\\"setPageNumber($event)\\\"\\n        (itemsPerPageChange)=\\\"setItemsPerPage($event)\\\"\\n    >\\n        <vdr-dt2-column [heading]=\\\"'common.id' | translate\\\" id=\\\"id\\\">\\n            <ng-template let-job=\\\"item\\\">\\n                {{ job.id }}\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'common.created-at' | translate\\\" id=\\\"created-at\\\">\\n            <ng-template let-job=\\\"item\\\">\\n                {{ job.createdAt | timeAgo }}\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.job-queue-name' | translate\\\" id=\\\"job-queue-name\\\" [optional]=\\\"false\\\">\\n            <ng-template let-job=\\\"item\\\">\\n                <vdr-chip [colorFrom]=\\\"job.queueName\\\">{{ job.queueName }}</vdr-chip>\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.job-data' | translate\\\" id=\\\"job-data\\\" [optional]=\\\"false\\\">\\n            <ng-template let-job=\\\"item\\\">\\n                <vdr-dropdown *ngIf=\\\"job.data\\\">\\n                    <button\\n                        class=\\\"button-small\\\"\\n                        vdrDropdownTrigger\\n                        [title]=\\\"'system.job-data' | translate\\\"\\n                    >\\n                        <clr-icon shape=\\\"details\\\"></clr-icon>\\n                    </button>\\n                    <vdr-dropdown-menu>\\n                        <div class=\\\"result-detail\\\">\\n                            <vdr-object-tree [value]=\\\"job.data\\\"></vdr-object-tree>\\n                        </div>\\n                    </vdr-dropdown-menu>\\n                </vdr-dropdown>\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.job-state' | translate\\\" id=\\\"job-state\\\">\\n            <ng-template let-job=\\\"item\\\">\\n                <vdr-job-state-label [job]=\\\"job\\\"></vdr-job-state-label>\\n                <div *ngIf=\\\"job.state === 'FAILED'\\\" class=\\\"retry-info\\\">after {{ job.attempts }} attempts</div>\\n                <div *ngIf=\\\"job.state === 'RUNNING' || job.state === 'RETRYING'\\\" class=\\\"retry-info\\\">\\n                    attempting {{ job.attempts }} of {{ job.retries + 1 }}\\n                </div>\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.job-duration' | translate\\\" id=\\\"job-duration\\\">\\n            <ng-template let-job=\\\"item\\\">\\n                {{ job.duration | duration }}\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.job-result' | translate\\\" id=\\\"job-result\\\">\\n            <ng-template let-job=\\\"item\\\">\\n                <vdr-dropdown *ngIf=\\\"hasResult(job)\\\">\\n                    <button class=\\\"button-small mr-1\\\" vdrDropdownTrigger>\\n                        <clr-icon shape=\\\"details\\\"></clr-icon>\\n                        {{ 'system.job-result' | translate }}\\n                    </button>\\n                    <vdr-dropdown-menu>\\n                        <div class=\\\"result-detail\\\">\\n                            <vdr-object-tree [value]=\\\"job.result\\\"></vdr-object-tree>\\n                        </div>\\n                    </vdr-dropdown-menu>\\n                </vdr-dropdown>\\n                <vdr-dropdown *ngIf=\\\"job.error\\\">\\n                    <button class=\\\"button-small\\\" vdrDropdownTrigger>\\n                        <clr-icon shape=\\\"exclamation-circle\\\"></clr-icon>\\n                        {{ 'system.job-error' | translate }}\\n                    </button>\\n                    <vdr-dropdown-menu>\\n                        <div class=\\\"result-detail\\\">\\n                            {{ job.error }}\\n                        </div>\\n                    </vdr-dropdown-menu>\\n                </vdr-dropdown>\\n                <vdr-dropdown *ngIf=\\\"!job.isSettled && job.state !== 'FAILED'\\\">\\n                    <button class=\\\"icon-button\\\" vdrDropdownTrigger>\\n                        <clr-icon shape=\\\"ellipsis-vertical\\\" size=\\\"12\\\"></clr-icon>\\n                    </button>\\n                    <vdr-dropdown-menu vdrPosition=\\\"bottom-right\\\">\\n                        <button\\n                            type=\\\"button\\\"\\n                            class=\\\"delete-button\\\"\\n                            (click)=\\\"cancelJob(job.id)\\\"\\n                            [disabled]=\\\"!(['DeleteSettings', 'DeleteSystem'] | hasPermission)\\\"\\n                            vdrDropdownItem\\n                        >\\n                            <clr-icon shape=\\\"ban\\\" class=\\\"is-danger\\\"></clr-icon>\\n                            {{ 'common.cancel' | translate }}\\n                        </button>\\n                    </vdr-dropdown-menu>\\n                </vdr-dropdown>\\n            </ng-template>\\n        </vdr-dt2-column>\\n    </vdr-data-table-2>\\n</vdr-page-body>\\n\",\n      styles: [\".result-detail{margin:0 12px}.retry-info{margin-inline-start:6px;color:var(--color-grey-400)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DataService\n  }, {\n    type: i2.Router\n  }, {\n    type: i2.ActivatedRoute\n  }], null);\n})();\nconst GET_SCHEDULED_TASKS_LIST = gql`\n    query GetAllScheduledTasks {\n        scheduledTasks {\n            id\n            description\n            schedule\n            scheduleDescription\n            lastExecutedAt\n            nextExecutionAt\n            isRunning\n            lastResult\n            enabled\n        }\n    }\n`;\nconst TOGGLE_SCHEDULED_TASK_ENABLED = gql`\n    mutation ToggleScheduledTaskEnabled($input: UpdateScheduledTaskInput!) {\n        updateScheduledTask(input: $input) {\n            id\n            enabled\n        }\n    }\n`;\nconst RUN_TASK = gql`\n    mutation RunTask($id: String!) {\n        runScheduledTask(id: $id) {\n            success\n        }\n    }\n`;\nclass ScheduledTaskListComponent {\n  constructor(dataService, notificationService) {\n    this.dataService = dataService;\n    this.notificationService = notificationService;\n    this.liveUpdate = new FormControl(true);\n  }\n  ngOnInit() {\n    this.tasks$ = this.dataService.query(GET_SCHEDULED_TASKS_LIST).mapStream(res => {\n      return res.scheduledTasks;\n    });\n  }\n  toggleEnabled(task) {\n    this.dataService.mutate(TOGGLE_SCHEDULED_TASK_ENABLED, {\n      input: {\n        id: task.id,\n        enabled: !task.enabled\n      }\n    }).subscribe();\n  }\n  runTask(task) {\n    this.dataService.mutate(RUN_TASK, {\n      id: task.id\n    }).subscribe(result => {\n      if (result.runScheduledTask.success) {\n        this.notificationService.success(_('system.task-will-be-triggered'));\n      } else {\n        this.notificationService.error(_('system.could-not-trigger-task'));\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ScheduledTaskListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScheduledTaskListComponent)(i0.ɵɵdirectiveInject(i1.DataService), i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ScheduledTaskListComponent,\n      selectors: [[\"vdr-scheduled-task-list\"]],\n      standalone: false,\n      decls: 32,\n      vars: 26,\n      consts: [[\"id\", \"scheduled-task-list\", 3, \"items\"], [\"id\", \"task-id\", 3, \"heading\", \"optional\"], [\"id\", \"description\", 3, \"heading\"], [\"id\", \"schedule\", 3, \"heading\"], [\"id\", \"last-executed-at\", 3, \"heading\"], [\"id\", \"next-execution-at\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"last-result\", 3, \"heading\"], [\"id\", \"enabled\", 3, \"heading\"], [\"id\", \"actions\"], [1, \"\"], [3, \"title\", 4, \"ngIf\"], [3, \"title\"], [4, \"ngIf\"], [\"vdrDropdownTrigger\", \"\", 1, \"button-small\", \"mr-1\"], [\"shape\", \"details\"], [1, \"result-detail\"], [3, \"value\"], [3, \"colorType\"], [\"vdrDropdownTrigger\", \"\", 1, \"icon-button\"], [\"shape\", \"ellipsis-vertical\", \"size\", \"12\"], [\"vdrPosition\", \"bottom-right\"], [\"type\", \"button\", \"class\", \"\", \"vdrDropdownItem\", \"\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"vdrDropdownItem\", \"\", 1, \"delete-button\", 3, \"click\", \"disabled\"], [\"shape\", \"ban\", 1, \"is-danger\"], [\"type\", \"button\", \"vdrDropdownItem\", \"\", 1, \"\", 3, \"click\", \"disabled\"], [\"shape\", \"play\", 1, \"\"]],\n      template: function ScheduledTaskListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-header\");\n          i0.ɵɵelement(1, \"vdr-page-title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"vdr-page-body\")(3, \"vdr-page-block\")(4, \"vdr-action-bar\");\n          i0.ɵɵelement(5, \"vdr-ab-left\")(6, \"vdr-ab-right\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"vdr-data-table-2\", 0);\n          i0.ɵɵpipe(8, \"async\");\n          i0.ɵɵelementStart(9, \"vdr-dt2-column\", 1);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵtemplate(11, ScheduledTaskListComponent_ng_template_11_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"vdr-dt2-column\", 2);\n          i0.ɵɵpipe(13, \"translate\");\n          i0.ɵɵtemplate(14, ScheduledTaskListComponent_ng_template_14_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"vdr-dt2-column\", 3);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵtemplate(17, ScheduledTaskListComponent_ng_template_17_Template, 2, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"vdr-dt2-column\", 4);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵtemplate(20, ScheduledTaskListComponent_ng_template_20_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"vdr-dt2-column\", 5);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵtemplate(23, ScheduledTaskListComponent_ng_template_23_Template, 2, 3, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"vdr-dt2-column\", 6);\n          i0.ɵɵpipe(25, \"translate\");\n          i0.ɵɵtemplate(26, ScheduledTaskListComponent_ng_template_26_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"vdr-dt2-column\", 7);\n          i0.ɵɵpipe(28, \"translate\");\n          i0.ɵɵtemplate(29, ScheduledTaskListComponent_ng_template_29_Template, 2, 2, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"vdr-dt2-column\", 8);\n          i0.ɵɵtemplate(31, ScheduledTaskListComponent_ng_template_31_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(8, 10, ctx.tasks$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(10, 12, \"system.task-id\"))(\"optional\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(13, 14, \"common.description\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(16, 16, \"system.schedule\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(19, 18, \"system.last-executed-at\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(22, 20, \"system.next-execution-at\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(25, 22, \"system.last-result\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(28, 24, \"common.enabled\"));\n        }\n      },\n      dependencies: [i3.ClrIconCustomTag, i3$1.NgIf, i1.ActionBarComponent, i1.ActionBarLeftComponent, i1.ActionBarRightComponent, i1.ChipComponent, i1.DropdownComponent, i1.DropdownMenuComponent, i1.DropdownTriggerDirective, i1.DropdownItemDirective, i1.ObjectTreeComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i1.PageHeaderComponent, i1.PageTitleComponent, i1.PageBodyComponent, i1.PageBlockComponent, i3$1.AsyncPipe, i4.TranslatePipe, i1.HasPermissionPipe, i1.TimeAgoPipe, i1.LocaleDatePipe],\n      styles: [_c3],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScheduledTaskListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-scheduled-task-list',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-header>\\n    <vdr-page-title></vdr-page-title>\\n</vdr-page-header>\\n<vdr-page-body>\\n    <vdr-page-block>\\n        <vdr-action-bar>\\n            <vdr-ab-left> </vdr-ab-left>\\n            <vdr-ab-right> </vdr-ab-right>\\n        </vdr-action-bar>\\n    </vdr-page-block>\\n\\n    <vdr-data-table-2 id=\\\"scheduled-task-list\\\" [items]=\\\"tasks$ | async\\\">\\n        <vdr-dt2-column [heading]=\\\"'system.task-id' | translate\\\" id=\\\"task-id\\\" [optional]=\\\"false\\\">\\n            <ng-template let-task=\\\"item\\\">\\n                {{ task.id }}\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'common.description' | translate\\\" id=\\\"description\\\">\\n            <ng-template let-task=\\\"item\\\">\\n                {{ task.description }}\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.schedule' | translate\\\" id=\\\"schedule\\\">\\n            <ng-template let-task=\\\"item\\\">\\n                <div class=\\\"\\\">{{ task.scheduleDescription }}</div>\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.last-executed-at' | translate\\\" id=\\\"last-executed-at\\\">\\n            <ng-template let-task=\\\"item\\\">\\n                <span *ngIf=\\\"task.lastExecutedAt\\\" [title]=\\\"task.lastExecutedAt | localeDate\\\">\\n                    {{ task.lastExecutedAt | timeAgo }}\\n                </span>\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column\\n            [heading]=\\\"'system.next-execution-at' | translate\\\"\\n            id=\\\"next-execution-at\\\"\\n            [hiddenByDefault]=\\\"true\\\"\\n        >\\n            <ng-template let-task=\\\"item\\\">\\n                {{ task.nextExecutionAt | localeDate }}\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'system.last-result' | translate\\\" id=\\\"last-result\\\">\\n            <ng-template let-task=\\\"item\\\">\\n                <vdr-dropdown *ngIf=\\\"task.lastResult\\\">\\n                    <button class=\\\"button-small mr-1\\\" vdrDropdownTrigger>\\n                        <clr-icon shape=\\\"details\\\"></clr-icon>\\n                        {{ 'system.last-result' | translate }}\\n                    </button>\\n                    <vdr-dropdown-menu>\\n                        <div class=\\\"result-detail\\\">\\n                            <vdr-object-tree [value]=\\\"task.lastResult\\\"></vdr-object-tree>\\n                        </div>\\n                    </vdr-dropdown-menu>\\n                </vdr-dropdown>\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column [heading]=\\\"'common.enabled' | translate\\\" id=\\\"enabled\\\">\\n            <ng-template let-task=\\\"item\\\">\\n                <ng-container *ngIf=\\\"task.enabled\\\">\\n                    <vdr-chip [colorType]=\\\"'success'\\\">{{ 'common.enabled' | translate }}</vdr-chip>\\n                </ng-container>\\n                <ng-container *ngIf=\\\"!task.enabled\\\">\\n                    <vdr-chip [colorType]=\\\"'warning'\\\">{{ 'common.disabled' | translate }}</vdr-chip>\\n                </ng-container>\\n            </ng-template>\\n        </vdr-dt2-column>\\n        <vdr-dt2-column id=\\\"actions\\\">\\n            <ng-template let-task=\\\"item\\\">\\n                <vdr-dropdown *ngIf=\\\"!task.isSettled && task.state !== 'FAILED'\\\">\\n                    <button class=\\\"icon-button\\\" vdrDropdownTrigger>\\n                        <clr-icon shape=\\\"ellipsis-vertical\\\" size=\\\"12\\\"></clr-icon>\\n                    </button>\\n                    <vdr-dropdown-menu vdrPosition=\\\"bottom-right\\\">\\n                        <button\\n                            type=\\\"button\\\"\\n                            *ngIf=\\\"task.enabled\\\"\\n                            class=\\\"\\\"\\n                            (click)=\\\"runTask(task)\\\"\\n                            [disabled]=\\\"!(['UpdateSettings', 'UpdateSystem'] | hasPermission)\\\"\\n                            vdrDropdownItem\\n                        >\\n                            <clr-icon shape=\\\"play\\\" class=\\\"\\\"></clr-icon>\\n                            <div>\\n                                {{ 'system.run-task' | translate }}\\n                            </div>\\n                        </button>\\n                        <button\\n                            type=\\\"button\\\"\\n                            class=\\\"delete-button\\\"\\n                            (click)=\\\"toggleEnabled(task)\\\"\\n                            [disabled]=\\\"!(['UpdateSettings', 'UpdateSystem'] | hasPermission)\\\"\\n                            vdrDropdownItem\\n                        >\\n                            <clr-icon shape=\\\"ban\\\" class=\\\"is-danger\\\"></clr-icon>\\n                            <div *ngIf=\\\"task.enabled\\\">\\n                                {{ 'common.disable' | translate }}\\n                            </div>\\n                            <div *ngIf=\\\"!task.enabled\\\">\\n                                {{ 'common.enable' | translate }}\\n                            </div>\\n                        </button>\\n                    </vdr-dropdown-menu>\\n                </vdr-dropdown>\\n            </ng-template>\\n        </vdr-dt2-column>\\n    </vdr-data-table-2>\\n</vdr-page-body>\\n\",\n      styles: [\".result-detail{margin:0 12px}.retry-info{margin-inline-start:6px;color:var(--color-grey-400)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DataService\n  }, {\n    type: i1.NotificationService\n  }], null);\n})();\nconst systemRoutes = [{\n  path: 'jobs',\n  component: JobListComponent,\n  data: {\n    breadcrumb: marker('breadcrumb.job-queue')\n  }\n}, {\n  path: 'system-status',\n  component: HealthCheckComponent,\n  data: {\n    breadcrumb: marker('breadcrumb.system-status')\n  }\n}, {\n  path: 'scheduled-tasks',\n  component: ScheduledTaskListComponent,\n  data: {\n    breadcrumb: marker('breadcrumb.scheduled-tasks')\n  }\n}];\nclass SystemModule {\n  static {\n    this.ɵfac = function SystemModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SystemModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: SystemModule,\n      declarations: [HealthCheckComponent, JobListComponent, JobStateLabelComponent, ScheduledTaskListComponent],\n      imports: [SharedModule, i2.RouterModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(systemRoutes)]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SystemModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [HealthCheckComponent, JobListComponent, JobStateLabelComponent, ScheduledTaskListComponent],\n      imports: [SharedModule, RouterModule.forChild(systemRoutes)]\n    }]\n  }], null, null);\n})();\n\n// This file was generated by the build-public-api.ts script\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { GET_SCHEDULED_TASKS_LIST, HealthCheckComponent, JobListComponent, JobStateLabelComponent, ScheduledTaskListComponent, SystemModule, systemRoutes };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,cAAc;AAAA,EACd,aAAa;AACf;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,8BAA8B,GAAG,GAAG;AAAA,EACtF;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,qBAAqB,GAAG,GAAG;AAAA,EAC7E;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,EAAE;AAC3C,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,mDAAmD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC5M,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,OAAO,IAAI,YAAY;AAC1B,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,WAAc,YAAY,CAAC;AACjC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,cAAc,MAAM,cAAc,IAAI,CAAC;AAC5F,IAAG,YAAY,SAAS,cAAc,OAAO,iBAAiB,oBAAoB;AAClF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,cAAc,IAAI,EAAE,YAAY,QAAQ;AAC9D,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,4BAA4B,GAAG,MAAS,YAAY,IAAI,IAAO,YAAY,IAAI,GAAG,OAAO,mBAAmB,UAAU,GAAG,YAAY,GAAG,GAAG;AAAA,EAC7L;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,yBAAyB,GAAG,GAAG;AAAA,EACjF;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,2BAA2B,GAAG,GAAG;AAAA,EACnF;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI,EAAE,GAAG,MAAM,EAAE;AACtC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,MAAM,EAAE,EAAE,GAAG,YAAY,EAAE;AAChD,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,mDAAmD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC5M,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,UAAa,YAAY,CAAC;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,GAAG;AAC/B,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,aAAa,OAAO,OAAO,WAAW,OAAO,YAAY,OAAO;AAC9E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,OAAO,WAAW,IAAI,EAAE,YAAY,OAAO;AACxE,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO,OAAO;AAAA,EAC5C;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,SAAS;AACtB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,OAAO,IAAI,WAAW,GAAG,GAAG,GAAG;AAAA,EACjF;AACF;AACA,IAAM,MAAM,MAAM,CAAC,kBAAkB,cAAc;AACnD,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,uBAAuB,CAAC;AAAA,EACpE;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,aAAa,QAAQ,IAAI;AACvC,IAAG,UAAU;AACb,IAAG,kBAAkB,QAAQ,IAAI;AAAA,EACnC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,wDAAwD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,EACxN;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,YAAe,YAAY,CAAC;AAClC,IAAG,WAAW,QAAQ,QAAQ,SAAS,KAAK,EAAE,YAAY,SAAS;AAAA,EACrE;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,IAAG,mBAAmB,KAAK,OAAO,IAAI,GAAG;AAAA,EAC3C;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,SAAS;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,OAAO,SAAS,GAAG,GAAG;AAAA,EACxE;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,IAAG,WAAW,aAAa,OAAO,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,SAAS;AAAA,EACvC;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,mBAAmB,EAAE,GAAG,OAAO,EAAE;AACtD,IAAG,UAAU,GAAG,mBAAmB,CAAC;AACpC,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,iBAAiB,CAAC;AAC9D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,OAAO,IAAI;AAAA,EACpC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE;AAAA,EACpG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,IAAG,WAAW,QAAQ,OAAO,IAAI;AAAA,EACnC;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,UAAU;AACb,IAAG,mBAAmB,UAAU,OAAO,UAAU,WAAW;AAAA,EAC9D;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,UAAU;AACb,IAAG,mBAAmB,gBAAgB,OAAO,UAAU,QAAQ,OAAO,UAAU,GAAG,GAAG;AAAA,EACxF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,uBAAuB,EAAE;AACzC,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,gDAAgD,GAAG,GAAG,OAAO,EAAE;AAAA,EACtJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,IAAG,WAAW,OAAO,MAAM;AAC3B,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU,QAAQ;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU,aAAa,OAAO,UAAU,UAAU;AAAA,EACjF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,UAAU;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,OAAO,QAAQ,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,mBAAmB,EAAE,GAAG,OAAO,EAAE;AACtD,IAAG,UAAU,GAAG,mBAAmB,CAAC;AACpC,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,mBAAmB,GAAG,GAAG;AACzE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,OAAO,MAAM;AAAA,EACtC;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,mBAAmB,EAAE,GAAG,OAAO,EAAE;AACtD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,EAAE;AAClC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,kBAAkB,GAAG,GAAG;AACxE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,OAAO,OAAO,GAAG;AAAA,EAC9C;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,qBAAqB,EAAE,EAAE,GAAG,UAAU,EAAE;AAC7D,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,WAAW,SAAS,SAAS,kFAAkF;AAChH,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,EAAE;AAClC,YAAM,UAAa,cAAc;AACjC,aAAU,YAAY,QAAQ,UAAU,OAAO,EAAE,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAI,YAAY,GAAG,GAAM,gBAAgB,GAAG,GAAG,CAAC,CAAC;AAC3E,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE;AAAA,EAChR;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,UAAa,cAAc;AACjC,IAAG,WAAW,QAAQ,QAAQ,UAAU,MAAM,CAAC;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK;AAClC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,aAAa,OAAO,UAAU,QAAQ;AAAA,EACtE;AACF;AACA,IAAM,MAAM,MAAM,CAAC,kBAAkB,cAAc;AACnD,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,mBAAmB,KAAK,QAAQ,IAAI,GAAG;AAAA,EAC5C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,mBAAmB,KAAK,QAAQ,aAAa,GAAG;AAAA,EACrD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,UAAU;AACb,IAAG,kBAAkB,QAAQ,mBAAmB;AAAA,EAClD;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,GAAG,YAAY;AACzB,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,SAAS;AACtB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,WAAW,SAAY,YAAY,GAAG,GAAG,QAAQ,cAAc,CAAC;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,QAAQ,cAAc,GAAG,GAAG;AAAA,EAC9E;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,QAAQ,EAAE;AAAA,EAC9F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,QAAQ,QAAQ,cAAc;AAAA,EAC9C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,QAAQ,eAAe,GAAG,GAAG;AAAA,EAC/E;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,mBAAmB,EAAE,GAAG,OAAO,EAAE;AACtD,IAAG,UAAU,GAAG,mBAAmB,EAAE;AACrC,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,oBAAoB,GAAG,GAAG;AAC1E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,SAAS,QAAQ,UAAU;AAAA,EAC3C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,gBAAgB,EAAE;AAAA,EAC9G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,QAAQ,QAAQ,UAAU;AAAA,EAC1C;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,SAAS;AACpC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC7D;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,YAAY,EAAE;AACnC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,SAAS;AACpC,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,iBAAiB,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,mEAAmE,GAAG,GAAG,gBAAgB,EAAE;AAAA,EAC9M;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,QAAQ,QAAQ,OAAO;AACrC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,QAAQ,OAAO;AAAA,EACxC;AACF;AACA,SAAS,2EAA2E,IAAI,KAAK;AAC3F,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,WAAW,SAAS,SAAS,qGAAqG;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,CAAC,EAAE;AACrC,YAAM,UAAa,cAAc;AACjC,aAAU,YAAY,QAAQ,QAAQ,QAAQ,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,YAAY,CAAI,YAAY,GAAG,GAAM,gBAAgB,GAAG,GAAG,CAAC,CAAC;AAC3E,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,iBAAiB,GAAG,GAAG;AAAA,EACzE;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,gBAAgB,GAAG,GAAG;AAAA,EACxE;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,eAAe,GAAG,GAAG;AAAA,EACvE;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,qBAAqB,EAAE;AAC5C,IAAG,WAAW,GAAG,4EAA4E,GAAG,GAAG,UAAU,EAAE;AAC/G,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,WAAW,SAAS,SAAS,4FAA4F;AAC1H,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,EAAE;AACpC,YAAM,UAAa,cAAc;AACjC,aAAU,YAAY,QAAQ,cAAc,QAAQ,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,yEAAyE,GAAG,GAAG,OAAO,EAAE;AACtM,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,SAAS,OAAO;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,CAAI,YAAY,GAAG,GAAM,gBAAgB,GAAG,GAAG,CAAC,CAAC;AAC3E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,SAAS,OAAO;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,SAAS,OAAO;AAAA,EACzC;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,IAAI,GAAG,gBAAgB,EAAE;AAAA,EAC/G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,QAAQ,CAAC,SAAS,aAAa,SAAS,UAAU,QAAQ;AAAA,EAC1E;AACF;AACA,IAAM,MAAM;AACZ,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,oBAAoB;AAC9B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,kBAAkB,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,wBAAwB,GAAG,MAAM,GAAG,CAAC,cAAc,eAAe,GAAG,CAAC,GAAG,OAAO,iBAAiB,GAAG,OAAO,GAAG,CAAC,SAAS,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,QAAQ,MAAM,GAAG,SAAS,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,gBAAgB,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,SAAS,cAAc,GAAG,CAAC,SAAS,oBAAoB,CAAC;AAAA,MACpe,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,iBAAiB;AACtC,UAAG,UAAU,GAAG,gBAAgB;AAChC,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,eAAe,EAAE,GAAG,gBAAgB,EAAE,GAAG,gBAAgB,EAAE,GAAG,aAAa;AAChG,UAAG,WAAW,GAAG,qCAAqC,IAAI,IAAI,OAAO,CAAC;AACtE,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,cAAc;AACnC,UAAG,UAAU,GAAG,wBAAwB,CAAC;AACzC,UAAG,eAAe,IAAI,UAAU,CAAC;AACjC,UAAG,WAAW,SAAS,SAAS,yDAAyD;AACvF,mBAAO,IAAI,mBAAmB,QAAQ;AAAA,UACxC,CAAC;AACD,UAAG,UAAU,IAAI,YAAY,CAAC;AAC9B,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE,EAAE,EAAE;AACtB,UAAG,eAAe,IAAI,gBAAgB,EAAE,IAAI,SAAS,CAAC,EAAE,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC;AAC1F,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,MAAM,CAAC;AAC7B,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,MAAM,CAAC;AAC7B,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,OAAO;AAC7B,UAAG,WAAW,IAAI,qCAAqC,IAAI,GAAG,MAAM,CAAC;AACrE,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa,EAAE,EAAE,EAAE;AAAA,QACxB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,GAAG,GAAG,IAAI,mBAAmB,OAAO,CAAC;AAC1E,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,IAAI,GAAG,uBAAuB,GAAG,GAAG;AAC9E,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,aAAa,GAAG,GAAG;AACrE,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,sBAAsB,GAAG,GAAG;AAC9E,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,uBAAuB,GAAG,GAAG;AAC/E,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,IAAI,mBAAmB,QAAQ,CAAC;AAAA,QAClF;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,SAAc,SAAc,MAAS,oBAAuB,wBAA2B,yBAA4B,eAAkB,yBAA4B,qBAAwB,oBAAuB,mBAAsB,oBAAyB,WAAc,eAAkB,cAAc;AAAA,MACtV,QAAQ,CAAC,wVAAwV;AAAA,MACjW,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,uPAAuP;AAAA,IAClQ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,IAAI,YAAY;AACd,YAAQ,KAAK,IAAI,OAAO;AAAA,MACtB,KAAK,SAAS;AACZ,eAAO;AAAA,MACT,KAAK,SAAS;AACZ,eAAO;AAAA,MACT,KAAK,SAAS;AACZ,eAAO;AAAA,MACT,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AACZ,eAAO;AAAA,MACT,KAAK,SAAS;AACZ,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AACd,YAAQ,KAAK,IAAI,OAAO;AAAA,MACtB,KAAK,SAAS;AACZ,eAAO;AAAA,MACT,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AACZ,eAAO;AAAA,MACT,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AACZ,eAAO;AAAA,MACT,KAAK,SAAS;AACZ,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,KAAK;AAAA,MACP;AAAA,MACA,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,YAAY,GAAG,MAAM,GAAG,CAAC,GAAG,UAAU,CAAC;AAAA,MACxF,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,YAAY,CAAC;AAClC,UAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,QAAQ,CAAC;AACxE,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,aAAa,IAAI,SAAS;AACxC,UAAG,UAAU;AACb,UAAG,YAAY,SAAS,IAAI,SAAS;AACrC,UAAG,UAAU;AACb,UAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG;AACnE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,IAAI,UAAU,SAAS;AAAA,QACnD;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,MAAS,eAAoB,aAAkB,aAAa;AAAA,MACrG,QAAQ,CAAC,kGAAkG;AAAA,MAC3G,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,8DAA8D;AAAA,IACzE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,0BAAyB,kBAAkB;AAAA,EAC/C,YAAY,aAAa,QAAQ,OAAO;AACtC,UAAM,QAAQ,KAAK;AACnB,SAAK,cAAc;AACnB,SAAK,aAAa,IAAI,YAAY,IAAI;AACtC,SAAK,cAAc,IAAI,YAAY,KAAK;AACxC,SAAK,cAAc,IAAI,YAAY,EAAE;AACrC,UAAM,WAAW,IAAI,SAAS,KAAK,YAAY,SAAS,WAAW,GAAG,IAAI,GAAG,UAAQ,KAAK,MAAM,CAAC,MAAM,SAAS;AAC9G,YAAM,cAAc,KAAK,YAAY,UAAU,QAAQ,OAAO;AAAA,QAC5D,WAAW;AAAA,UACT,IAAI,KAAK,YAAY;AAAA,QACvB;AAAA,MACF;AACA,YAAM,cAAc,KAAK,YAAY;AACrC,aAAO;AAAA,QACL,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA,QAAQ,kCACH,cACC,cAAc;AAAA,YAChB,OAAO;AAAA,cACL,IAAI;AAAA,YACN;AAAA,UACF,IAAI,CAAC;AAAA,UAEP,MAAM;AAAA,YACJ,WAAW,UAAU;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,UAAM,KAAM,GAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,MAAM,CAAC,CAAC,KAAK,WAAW,KAAK,CAAC,EAAE,UAAU,MAAM;AACtG,WAAK,QAAQ;AAAA,IACf,CAAC;AACD,SAAK,UAAU,KAAK,YAAY,SAAS,aAAa,EAAE,UAAU,SAAO,IAAI,SAAS,EAAE,KAAK,IAAI,YAAU,CAAC;AAAA,MAC1G,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG,MAAM,CAAC,CAAC;AAAA,EAChB;AAAA,EACA,UAAU,KAAK;AACb,UAAM,SAAS,IAAI;AACnB,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,aAAO,OAAO,KAAK,MAAM,EAAE,SAAS;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,IAAI;AACZ,SAAK,YAAY,SAAS,UAAU,EAAE,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AAAA,EACxE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,WAAW,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,cAAc,CAAC;AAAA,IACnK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,QAAQ,YAAY,eAAe,IAAI,QAAQ,eAAe,GAAG,aAAa,GAAG,CAAC,cAAc,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,UAAU,UAAU,gBAAgB,YAAY,aAAa,aAAa,cAAc,aAAa,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,aAAa,SAAS,GAAG,CAAC,aAAa,SAAS,GAAG,CAAC,aAAa,OAAO,GAAG,CAAC,aAAa,QAAQ,GAAG,UAAU,UAAU,SAAS,gBAAgB,YAAY,aAAa,aAAa,cAAc,aAAa,GAAG,CAAC,gBAAgB,IAAI,iBAAiB,EAAE,GAAG,CAAC,MAAM,YAAY,GAAG,cAAc,sBAAsB,SAAS,gBAAgB,cAAc,aAAa,GAAG,CAAC,MAAM,MAAM,GAAG,SAAS,GAAG,CAAC,MAAM,cAAc,GAAG,SAAS,GAAG,CAAC,MAAM,kBAAkB,GAAG,WAAW,UAAU,GAAG,CAAC,MAAM,YAAY,GAAG,WAAW,UAAU,GAAG,CAAC,MAAM,aAAa,GAAG,SAAS,GAAG,CAAC,MAAM,gBAAgB,GAAG,SAAS,GAAG,CAAC,MAAM,cAAc,GAAG,SAAS,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,sBAAsB,IAAI,GAAG,gBAAgB,GAAG,OAAO,GAAG,CAAC,SAAS,SAAS,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,sBAAsB,IAAI,GAAG,gBAAgB,MAAM,GAAG,CAAC,sBAAsB,IAAI,GAAG,cAAc,GAAG,CAAC,SAAS,oBAAoB,GAAG,CAAC,sBAAsB,IAAI,GAAG,aAAa,GAAG,CAAC,SAAS,qBAAqB,QAAQ,IAAI,GAAG,CAAC,eAAe,cAAc,GAAG,CAAC,QAAQ,UAAU,mBAAmB,IAAI,GAAG,iBAAiB,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,OAAO,GAAG,WAAW,CAAC;AAAA,MACv/C,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,iBAAiB;AACtC,UAAG,UAAU,GAAG,gBAAgB;AAChC,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,eAAe,EAAE,GAAG,gBAAgB,EAAE,GAAG,gBAAgB,EAAE,GAAG,aAAa,EAAE,GAAG,wBAAwB,CAAC;AAC9H,UAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,UAAG,eAAe,GAAG,OAAO;AAC5B,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,cAAc;AACpC,UAAG,UAAU,IAAI,wBAAwB,CAAC;AAC1C,UAAG,eAAe,IAAI,aAAa,CAAC;AACpC,UAAG,WAAW,UAAU,SAAS,yDAAyD;AACxF,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC;AACD,UAAG,eAAe,IAAI,aAAa,CAAC,EAAE,IAAI,UAAU;AACpD,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,aAAa,CAAC,EAAE,IAAI,UAAU;AACpD,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,aAAa,CAAC,EAAE,IAAI,YAAY,CAAC;AACvD,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,aAAa,CAAC,EAAE,IAAI,YAAY,CAAC;AACvD,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,aAAa,CAAC,EAAE,IAAI,YAAY,CAAC;AACvD,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,aAAa,CAAC,EAAE,IAAI,YAAY,CAAC;AACvD,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,aAAa,CAAC;AACpC,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,WAAW,UAAU,SAAS,yDAAyD;AACxF,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC;AACD,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,eAAe,EAAE;AACnF,UAAG,aAAa,EAAE,EAAE,EAAE;AACtB,UAAG,eAAe,IAAI,oBAAoB,EAAE;AAC5C,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,WAAW,cAAc,SAAS,kEAAkE,QAAQ;AAC7G,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC,EAAE,sBAAsB,SAAS,0EAA0E,QAAQ;AAClH,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AACD,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,aAAa;AAC/E,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,aAAa;AAC/E,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,aAAa;AAC/E,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,aAAa;AAC/E,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,aAAa;AAC/E,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,aAAa;AAC/E,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,EAAE;AAC1C,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,aAAa;AAC/E,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,eAAe,IAAI,UAAU;AAC3C,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,oBAAoB,CAAC;AACjE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,UAAU,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,KAAK,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,cAAc,KAAK,EAAE,eAAe,IAAI,WAAW;AACnK,UAAG,UAAU;AACb,UAAG,WAAW,SAAS,EAAE;AACzB,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,sBAAsB,CAAC;AACnE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAS,SAAS;AAChC,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,0BAA0B,CAAC;AACvE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAS,SAAS;AAChC,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,0BAA0B,CAAC;AACvE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAS,WAAW;AAClC,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,4BAA4B,CAAC;AACzE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAS,QAAQ;AAC/B,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,yBAAyB,CAAC;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAS,WAAW;AAClC,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,4BAA4B,CAAC;AACzE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,UAAU,KAAK,EAAE,SAAY,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC,EAAE,gBAAgB,IAAI,EAAE,YAAY,KAAK,EAAE,aAAa,KAAK,EAAE,aAAa,KAAK,EAAE,cAAc,KAAK,EAAE,eAAe,IAAI,WAAW;AACjN,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,IAAI,IAAI,IAAI,MAAM,CAAC,EAAE,gBAAmB,YAAY,IAAI,IAAI,IAAI,aAAa,CAAC,EAAE,cAAiB,YAAY,IAAI,IAAI,IAAI,WAAW,CAAC,EAAE,eAAkB,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC;AACpO,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,WAAW,CAAC;AAC5D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,uBAAuB,CAAC,EAAE,YAAY,KAAK;AAC3F,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,iBAAiB,CAAC,EAAE,YAAY,KAAK;AACrF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,kBAAkB,CAAC;AACnE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,qBAAqB,CAAC;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,mBAAmB,CAAC;AAAA,QACtE;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAqB,UAAa,aAAgB,oBAAyB,MAAS,8BAAiC,iBAAoB,sBAAyB,mBAAsB,mBAAsB,2BAA8B,0BAA6B,oBAAuB,wBAA2B,yBAA4B,eAAkB,2BAA8B,mBAAsB,uBAA0B,0BAA6B,uBAA0B,qBAAwB,yBAA4B,qBAAwB,2BAA8B,qBAAwB,oBAAuB,mBAAsB,oBAAoB,wBAA6B,WAAc,eAAkB,mBAAsB,aAAgB,YAAY;AAAA,MAClzB,QAAQ,CAAC,qIAAqI;AAAA,MAC9I,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,iGAAiG;AAAA,IAC5G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAejC,IAAM,gCAAgC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQtC,IAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOjB,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,aAAa,qBAAqB;AAC5C,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAC3B,SAAK,aAAa,IAAI,YAAY,IAAI;AAAA,EACxC;AAAA,EACA,WAAW;AACT,SAAK,SAAS,KAAK,YAAY,MAAM,wBAAwB,EAAE,UAAU,SAAO;AAC9E,aAAO,IAAI;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,cAAc,MAAM;AAClB,SAAK,YAAY,OAAO,+BAA+B;AAAA,MACrD,OAAO;AAAA,QACL,IAAI,KAAK;AAAA,QACT,SAAS,CAAC,KAAK;AAAA,MACjB;AAAA,IACF,CAAC,EAAE,UAAU;AAAA,EACf;AAAA,EACA,QAAQ,MAAM;AACZ,SAAK,YAAY,OAAO,UAAU;AAAA,MAChC,IAAI,KAAK;AAAA,IACX,CAAC,EAAE,UAAU,YAAU;AACrB,UAAI,OAAO,iBAAiB,SAAS;AACnC,aAAK,oBAAoB,QAAQ,EAAE,+BAA+B,CAAC;AAAA,MACrE,OAAO;AACL,aAAK,oBAAoB,MAAM,EAAE,+BAA+B,CAAC;AAAA,MACnE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,WAAW,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,IACjJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,MACvC,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,MAAM,uBAAuB,GAAG,OAAO,GAAG,CAAC,MAAM,WAAW,GAAG,WAAW,UAAU,GAAG,CAAC,MAAM,eAAe,GAAG,SAAS,GAAG,CAAC,MAAM,YAAY,GAAG,SAAS,GAAG,CAAC,MAAM,oBAAoB,GAAG,SAAS,GAAG,CAAC,MAAM,qBAAqB,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,eAAe,GAAG,SAAS,GAAG,CAAC,MAAM,WAAW,GAAG,SAAS,GAAG,CAAC,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,sBAAsB,IAAI,GAAG,gBAAgB,MAAM,GAAG,CAAC,SAAS,SAAS,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,sBAAsB,IAAI,GAAG,aAAa,GAAG,CAAC,SAAS,qBAAqB,QAAQ,IAAI,GAAG,CAAC,eAAe,cAAc,GAAG,CAAC,QAAQ,UAAU,SAAS,IAAI,mBAAmB,IAAI,GAAG,YAAY,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,UAAU,mBAAmB,IAAI,GAAG,iBAAiB,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,OAAO,GAAG,WAAW,GAAG,CAAC,QAAQ,UAAU,mBAAmB,IAAI,GAAG,IAAI,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,QAAQ,GAAG,EAAE,CAAC;AAAA,MAC59B,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,iBAAiB;AACtC,UAAG,UAAU,GAAG,gBAAgB;AAChC,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,eAAe,EAAE,GAAG,gBAAgB,EAAE,GAAG,gBAAgB;AAC9E,UAAG,UAAU,GAAG,aAAa,EAAE,GAAG,cAAc;AAChD,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,aAAa;AACzF,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,SAAY,YAAY,GAAG,IAAI,IAAI,MAAM,CAAC;AACxD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,gBAAgB,CAAC,EAAE,YAAY,KAAK;AACpF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,oBAAoB,CAAC;AACrE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,iBAAiB,CAAC;AAClE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,yBAAyB,CAAC;AAC1E,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,0BAA0B,CAAC,EAAE,mBAAmB,IAAI;AACpG,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,oBAAoB,CAAC;AACrE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,gBAAgB,CAAC;AAAA,QACnE;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAuB,MAAS,oBAAuB,wBAA2B,yBAA4B,eAAkB,mBAAsB,uBAA0B,0BAA6B,uBAA0B,qBAAwB,qBAAwB,2BAA8B,qBAAwB,oBAAuB,mBAAsB,oBAAyB,WAAc,eAAkB,mBAAsB,aAAgB,cAAc;AAAA,MACzf,QAAQ,CAAC,GAAG;AAAA,MACZ,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,iGAAiG;AAAA,IAC5G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,eAAe,CAAC;AAAA,EACpB,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY,OAAO,sBAAsB;AAAA,EAC3C;AACF,GAAG;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY,OAAO,0BAA0B;AAAA,EAC/C;AACF,GAAG;AAAA,EACD,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,YAAY,OAAO,4BAA4B;AAAA,EACjD;AACF,CAAC;AACD,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,sBAAsB,kBAAkB,wBAAwB,0BAA0B;AAAA,MACzG,SAAS,CAAC,cAAiB,YAAY;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,cAAc,aAAa,SAAS,YAAY,CAAC;AAAA,IAC7D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,sBAAsB,kBAAkB,wBAAwB,0BAA0B;AAAA,MACzG,SAAS,CAAC,cAAc,aAAa,SAAS,YAAY,CAAC;AAAA,IAC7D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}