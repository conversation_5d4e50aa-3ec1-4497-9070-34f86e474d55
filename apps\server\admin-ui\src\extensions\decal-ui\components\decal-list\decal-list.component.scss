.asset-preview {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
}

.asset-thumbnail {
	max-width: 100%;
	max-height: 100%;
	border-radius: 4px;
	border: 1px solid var(--color-grey-300);
}

.no-image {
	font-size: 12px;
	color: var(--color-grey-500);
	text-align: center;
}

.category-badge {
	display: inline-block;
	padding: 2px 8px;
	background-color: var(--color-grey-100);
	border-radius: 12px;
	font-size: 12px;
	color: var(--color-grey-700);
}

.action-buttons {
	display: flex;
	gap: 4px;
	align-items: center;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	padding: 24px;
	background: white;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	color: var(--color-grey-600);
}

.loading-disabled {
	opacity: 0.6;
	pointer-events: none;
}

.empty-state {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 400px;
	padding: 40px;
}

.empty-state-content {
	text-align: center;
	max-width: 400px;
}

.empty-state-content clr-icon {
	color: var(--color-grey-400);
	margin-bottom: 16px;
}

.empty-state-content h3 {
	margin: 16px 0 8px 0;
	color: var(--color-grey-700);
	font-size: 18px;
	font-weight: 500;
}

.empty-state-content p {
	margin: 0 0 24px 0;
	color: var(--color-grey-500);
	font-size: 14px;
}

.spinning {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.flex {
	display: flex;
}

.items-center {
	align-items: center;
}

.gap-2 {
	gap: 8px;
}
