import {
  ConsoleService,
  DefaultSelectionModel,
  DefaultSelectionModelFactory,
  NgClearButtonTemplateDirective,
  NgDropdownPanelComponent,
  NgDropdownPanelService,
  Ng<PERSON>ooterTemplateDirective,
  NgHeaderTemplateDirective,
  NgItemLabelDirective,
  NgLabelTemplateDirective,
  NgLoadingSpinnerTemplateDirective,
  NgLoadingTextTemplateDirective,
  NgMultiLabelTemplateDirective,
  NgNotFoundTemplateDirective,
  NgOptgroupTemplateDirective,
  NgOptionComponent,
  NgOptionTemplateDirective,
  NgPlaceholderTemplateDirective,
  NgSelectComponent,
  NgSelectConfig,
  NgSelectModule,
  NgTagTemplateDirective,
  NgTypeToSearchTemplateDirective,
  SELECTION_MODEL_FACTORY
} from "./chunk-FPK6BPCO.js";
import "./chunk-ABXAE3SJ.js";
import "./chunk-F7BQOD5X.js";
import "./chunk-JNMUMDNO.js";
import "./chunk-OKFFUXD2.js";
import "./chunk-755OWGIU.js";
import "./chunk-WKNUSL3B.js";
import "./chunk-TXDUYLVM.js";
export {
  ConsoleService,
  DefaultSelectionModel,
  DefaultSelectionModelFactory,
  NgClearButtonTemplateDirective,
  NgDropdownPanelComponent,
  NgDropdownPanelService,
  NgFooterTemplateDirective,
  NgHeaderTemplateDirective,
  NgItemLabelDirective,
  NgLabelTemplateDirective,
  NgLoadingSpinnerTemplateDirective,
  NgLoadingTextTemplateDirective,
  NgMultiLabelTemplateDirective,
  NgNotFoundTemplateDirective,
  NgOptgroupTemplateDirective,
  NgOptionComponent,
  NgOptionTemplateDirective,
  NgPlaceholderTemplateDirective,
  NgSelectComponent,
  NgSelectConfig,
  NgSelectModule,
  NgTagTemplateDirective,
  NgTypeToSearchTemplateDirective,
  SELECTION_MODEL_FACTORY
};
