import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import {
	DataService,
	ModalService,
	NotificationService,
	SharedModule,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

const GET_DECAL_LIST = gql`
	query GetDecalList {
		decals {
			items {
				id
				createdAt
				updatedAt
				name
				description
				category
				isActive
				maxWidth
				maxHeight
				minScale
				maxScale
				asset {
					id
					name
					preview
				}
			}
			totalItems
		}
	}
`;

const DELETE_DECAL = gql`
	mutation DeleteDecal($id: ID!) {
		deleteDecal(id: $id) {
			result
			message
		}
	}
`;

@Component({
	selector: 'decal-list',
	templateUrl: './decal-list.component.html',
	styleUrls: ['./decal-list.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	standalone: true,
	imports: [SharedModule],
})
export class DecalListComponent implements OnInit {
	decals$: Observable<any>;
	loading$ = new BehaviorSubject<boolean>(false);
	private refreshTrigger$ = new BehaviorSubject<void>(undefined);

	constructor(
		private dataService: DataService,
		private modalService: ModalService,
		private notificationService: NotificationService
	) {}

	ngOnInit() {
		this.decals$ = this.refreshTrigger$.pipe(
			switchMap(() => {
				this.loading$.next(true);
				return this.dataService.query(GET_DECAL_LIST).stream$.pipe(
					map((result) => {
						this.loading$.next(false);
						return result;
					})
				);
			})
		);
	}

	async deleteDecal(decal: any) {
		const result = await this.modalService
			.dialog({
				title: 'Delete Decal',
				body: `Are you sure you want to delete "${decal.name}"? This action cannot be undone.`,
				buttons: [
					{ type: 'secondary', label: 'Cancel' },
					{ type: 'danger', label: 'Delete', returnValue: true },
				],
			})
			.pipe(map((res) => res))
			.toPromise();

		if (result) {
			this.loading$.next(true);
			this.dataService.mutate(DELETE_DECAL, { id: decal.id }).subscribe({
				next: (deleteResult: any) => {
					this.loading$.next(false);
					if (deleteResult.deleteDecal?.result === 'DELETED') {
						this.notificationService.success('Decal deleted successfully');
						this.refreshList();
					} else {
						this.notificationService.error('Failed to delete decal');
					}
				},
				error: (error: any) => {
					this.loading$.next(false);
					this.notificationService.error(
						'Error deleting decal: ' + (error.message || 'Unknown error')
					);
				},
			});
		}
	}

	refreshList() {
		this.refreshTrigger$.next();
	}
}
