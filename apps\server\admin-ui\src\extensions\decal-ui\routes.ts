import { registerRouteComponent } from '@vendure/admin-ui/core';
import { DecalDetailComponent } from './components/decal-detail/decal-detail.component';
import { DecalListComponent } from './components/decal-list/decal-list.component';

export default [
	// List view
	registerRouteComponent({
		path: '',
		component: DecalListComponent,
		breadcrumb: 'Decals',
	}),
	// Create view
	registerRouteComponent({
		path: 'create',
		component: DecalDetailComponent,
		breadcrumb: [
			{ label: 'Decals', link: ['/extensions', 'decal'] },
			{ label: 'Create decal', link: [] },
		],
	}),
	// Detail view
	registerRouteComponent({
		path: ':id',
		component: DecalDetailComponent,
		breadcrumb: [
			{ label: 'Decals', link: ['/extensions', 'decal'] },
			{ label: 'Decal Details', link: [] },
		],
	}),
];
