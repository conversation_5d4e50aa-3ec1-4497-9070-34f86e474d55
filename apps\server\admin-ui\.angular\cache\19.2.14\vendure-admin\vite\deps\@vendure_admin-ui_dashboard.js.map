{"version": 3, "sources": ["../../../../../../../node_modules/@vendure/admin-ui/fesm2022/vendure-admin-ui-dashboard.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ViewContainerRef, ViewChild, Input, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport * as i1 from '@vendure/admin-ui/core';\nimport { titleSetter, GetLatestOrdersDocument, SortOrder, CoreModule, SharedModule, MetricType, GetOrderChartDataDocument, GetOrderSummaryDocument, Permission, ADMIN_UI_VERSION, getAppConfig } from '@vendure/admin-ui/core';\nimport { assertNever } from '@vendure/common/lib/shared-utils';\nimport { map, tap, distinctUntilChanged, startWith, shareReplay, switchMap as switchMap$1 } from 'rxjs/operators';\nimport * as i2$1 from '@clr/angular';\nimport * as i3 from '@angular/common';\nimport * as i4 from '@angular/cdk/drag-drop';\nimport * as i2 from '@ngx-translate/core';\nimport * as i3$1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { marker } from '@biesbjerg/ngx-translate-extract-marker';\nimport { gql } from 'apollo-angular';\nimport { Subject, BehaviorSubject, combineLatest, switchMap } from 'rxjs';\nimport dayjs from 'dayjs';\nconst _c0 = [\"portal\"];\nconst _c1 = [\"*\"];\nfunction DashboardWidgetComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction DashboardWidgetComponent_ng_template_3_Template(rf, ctx) {}\nconst _c2 = a0 => ({\n  index: a0\n});\nconst _c3 = a0 => ({\n  width: a0\n});\nfunction DashboardComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_button_9_Template_button_click_0_listener() {\n      const widget_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addWidget(widget_r2.id));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const widget_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, (tmp_2_0 = widget_r2.config.title) !== null && tmp_2_0 !== undefined ? tmp_2_0 : widget_r2.id), \" \");\n  }\n}\nfunction DashboardComponent_div_12_div_1_vdr_dashboard_widget_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_12_div_1_vdr_dashboard_widget_1_button_11_Template_button_click_0_listener() {\n      const width_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const widget_r8 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.setWidgetWidth(widget_r8, width_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const width_r7 = ctx.$implicit;\n    const widget_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"disabled\", width_r7 === widget_r8.width);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 2, \"dashboard.widget-width\", i0.ɵɵpureFunction1(5, _c3, width_r7)), \" \");\n  }\n}\nfunction DashboardComponent_div_12_div_1_vdr_dashboard_widget_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vdr-dashboard-widget\", 13)(1, \"div\", 14)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"clr-icon\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"vdr-dropdown\")(5, \"button\", 17);\n    i0.ɵɵelement(6, \"clr-icon\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"vdr-dropdown-menu\", 4)(8, \"h4\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardComponent_div_12_div_1_vdr_dashboard_widget_1_button_11_Template, 3, 7, \"button\", 19);\n    i0.ɵɵelement(12, \"div\", 20);\n    i0.ɵɵelementStart(13, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_12_div_1_vdr_dashboard_widget_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const widget_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeWidget(widget_r8));\n    });\n    i0.ɵɵelement(14, \"clr-icon\", 21);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const widget_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"widgetConfig\", widget_r8.config);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 4, \"dashboard.widget-resize\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getSupportedWidths(widget_r8.config));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 6, \"dashboard.remove-widget\"), \" \");\n  }\n}\nfunction DashboardComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, DashboardComponent_div_12_div_1_vdr_dashboard_widget_1_Template, 17, 8, \"vdr-dashboard-widget\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const widget_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getClassForWidth(widget_r8.width))(\"cdkDragData\", widget_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"vdrIfPermissions\", widget_r8.config.requiresPermissions || null);\n  }\n}\nfunction DashboardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function DashboardComponent_div_12_Template_div_cdkDropListDropped_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.drop($event));\n    });\n    i0.ɵɵtemplate(1, DashboardComponent_div_12_div_1_Template, 2, 3, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r9 = ctx.$implicit;\n    const rowIndex_r10 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkDropListData\", i0.ɵɵpureFunction1(3, _c2, rowIndex_r10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", row_r9)(\"ngForTrackBy\", ctx_r2.trackRowItem);\n  }\n}\nconst _c4 = a0 => [\"/orders/\", a0];\nfunction LatestOrdersWidgetComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"clr-icon\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const order_r1 = ctx.item;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c4, order_r1.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(order_r1.code);\n  }\n}\nfunction LatestOrdersWidgetComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-order-state-label\", 8);\n  }\n  if (rf & 2) {\n    const order_r2 = ctx.item;\n    i0.ɵɵproperty(\"state\", order_r2.state);\n  }\n}\nfunction LatestOrdersWidgetComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"vdr-customer-label\", 9);\n  }\n  if (rf & 2) {\n    const order_r3 = ctx.item;\n    i0.ɵɵproperty(\"customer\", order_r3.customer);\n  }\n}\nfunction LatestOrdersWidgetComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"localeCurrency\");\n  }\n  if (rf & 2) {\n    const order_r4 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(1, 1, order_r4.totalWithTax, order_r4.currencyCode), \" \");\n  }\n}\nfunction LatestOrdersWidgetComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"timeAgo\");\n  }\n  if (rf & 2) {\n    const order_r5 = ctx.item;\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(1, 1, order_r5.orderPlacedAt), \" \");\n  }\n}\nfunction OrderChartWidgetComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function OrderChartWidgetComponent_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.metricType$.next(ctx_r1.MetricType.OrderTotal));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function OrderChartWidgetComponent_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.metricType$.next(ctx_r1.MetricType.OrderCount));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function OrderChartWidgetComponent_div_2_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.metricType$.next(ctx_r1.MetricType.AverageOrderValue));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"div\", 5);\n    i0.ɵɵelementStart(11, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function OrderChartWidgetComponent_div_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refresh());\n    });\n    i0.ɵɵelement(12, \"clr-icon\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const activeMetricType_r3 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", activeMetricType_r3 === ctx_r1.MetricType.OrderTotal);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 9, \"dashboard.metric-order-total-value\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", activeMetricType_r3 === ctx_r1.MetricType.OrderCount);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 11, \"dashboard.metric-number-of-orders\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", activeMetricType_r3 === ctx_r1.MetricType.AverageOrderValue);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 13, \"dashboard.metric-average-order-value\"), \" \");\n  }\n}\nfunction OrderSummaryWidgetComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function OrderSummaryWidgetComponent_div_18_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selection$.next({\n        timeframe: \"day\",\n        date: ctx_r1.today\n      }));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function OrderSummaryWidgetComponent_div_18_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selection$.next({\n        timeframe: \"day\",\n        date: ctx_r1.yesterday\n      }));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function OrderSummaryWidgetComponent_div_18_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selection$.next({\n        timeframe: \"week\"\n      }));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function OrderSummaryWidgetComponent_div_18_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selection$.next({\n        timeframe: \"month\"\n      }));\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const selection_r3 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", selection_r3.date === ctx_r1.today);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 12, \"dashboard.today\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", selection_r3.date === ctx_r1.yesterday);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 14, \"dashboard.yesterday\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", selection_r3.timeframe === \"week\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 16, \"dashboard.thisWeek\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", selection_r3.timeframe === \"month\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 18, \"dashboard.thisMonth\"), \" \");\n  }\n}\nfunction OrderSummaryWidgetComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"localeDate\");\n    i0.ɵɵpipe(3, \"localeDate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const range_r4 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, range_r4.start), \" - \", i0.ɵɵpipeBind1(3, 4, range_r4.end), \" \");\n  }\n}\nfunction WelcomeWidgetComponent_div_0_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.hideVendureBranding ? \"\" : \"Vendure\", \" \", ctx_r0.hideVersion ? \"\" : \"Admin UI v\" + ctx_r0.version, \" \");\n  }\n}\nfunction WelcomeWidgetComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h4\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"small\", 4);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"timeAgo\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, WelcomeWidgetComponent_div_0_p_7_Template, 2, 2, \"p\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const administrator_r2 = ctx.ngIf;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Welcome, \", administrator_r2.firstName, \" \", administrator_r2.lastName, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Last login: \", i0.ɵɵpipeBind1(6, 4, administrator_r2.user.lastLogin), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.hideVendureBranding || !ctx_r0.hideVersion);\n  }\n}\nclass DashboardWidgetComponent {\n  ngAfterViewInit() {\n    this.loadWidget();\n  }\n  async loadWidget() {\n    const loadComponentResult = this.widgetConfig.loadComponent();\n    const componentType = loadComponentResult instanceof Promise ? await loadComponentResult : loadComponentResult;\n    this.componentRef = this.portal.createComponent(componentType);\n    this.componentRef.changeDetectorRef.detectChanges();\n  }\n  ngOnDestroy() {\n    if (this.componentRef) {\n      this.componentRef.destroy();\n    }\n  }\n  static {\n    this.ɵfac = function DashboardWidgetComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardWidgetComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DashboardWidgetComponent,\n      selectors: [[\"vdr-dashboard-widget\"]],\n      viewQuery: function DashboardWidgetComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, ViewContainerRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.portal = _t.first);\n        }\n      },\n      inputs: {\n        widgetConfig: \"widgetConfig\"\n      },\n      standalone: false,\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 3,\n      consts: [[\"portal\", \"\"], [3, \"title\"], [\"vdrCardControls\", \"\"]],\n      template: function DashboardWidgetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"vdr-card\", 1);\n          i0.ɵɵpipe(1, \"translate\");\n          i0.ɵɵtemplate(2, DashboardWidgetComponent_ng_template_2_Template, 1, 0, \"ng-template\", 2)(3, DashboardWidgetComponent_ng_template_3_Template, 0, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(1, 1, (tmp_1_0 = ctx.widgetConfig.title) !== null && tmp_1_0 !== undefined ? tmp_1_0 : \"\"));\n        }\n      },\n      dependencies: [i1.CardComponent, i1.CardControlsDirective, i2.TranslatePipe],\n      styles: [\"[_nghost-%COMP%]{display:block}.card[_ngcontent-%COMP%]{margin-top:0;min-height:200px}.card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DashboardWidgetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-dashboard-widget',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-card [title]=\\\"widgetConfig.title ?? '' | translate\\\">\\n    <ng-template vdrCardControls>\\n        <ng-content></ng-content>\\n    </ng-template>\\n    <ng-template #portal></ng-template>\\n</vdr-card>\\n\",\n      styles: [\":host{display:block}.card{margin-top:0;min-height:200px}.card-header{display:flex;justify-content:space-between}\\n\"]\n    }]\n  }], null, {\n    widgetConfig: [{\n      type: Input\n    }],\n    portal: [{\n      type: ViewChild,\n      args: ['portal', {\n        read: ViewContainerRef\n      }]\n    }]\n  });\n})();\nclass DashboardComponent {\n  constructor(dashboardWidgetService, localStorageService, changedDetectorRef, dataService) {\n    this.dashboardWidgetService = dashboardWidgetService;\n    this.localStorageService = localStorageService;\n    this.changedDetectorRef = changedDetectorRef;\n    this.dataService = dataService;\n    this.deletionMarker = '__delete__';\n    this.setTitle = titleSetter();\n  }\n  ngOnInit() {\n    this.availableWidgets$ = this.dataService.client.userStatus().stream$.pipe(map(({\n      userStatus\n    }) => userStatus.permissions), map(permissions => this.dashboardWidgetService.getAvailableWidgets(permissions)), tap(widgets => this.widgetLayout = this.initLayout(widgets.map(w => w.id))));\n    this.setTitle('breadcrumb.dashboard');\n  }\n  getClassForWidth(width) {\n    switch (width) {\n      case 3:\n        return `clr-col-12 clr-col-sm-6 clr-col-lg-3`;\n      case 4:\n        return `clr-col-12 clr-col-sm-6 clr-col-lg-4`;\n      case 6:\n        return `clr-col-12 clr-col-lg-6`;\n      case 8:\n        return `clr-col-12 clr-col-lg-8`;\n      case 12:\n        return `clr-col-12`;\n      default:\n        assertNever(width);\n    }\n  }\n  getSupportedWidths(config) {\n    return config.supportedWidths || [3, 4, 6, 8, 12];\n  }\n  setWidgetWidth(widget, width) {\n    widget.width = width;\n    this.recalculateLayout();\n  }\n  trackRow(index, row) {\n    const id = row.map(item => `${item.id}:${item.width}`).join('|');\n    return id;\n  }\n  trackRowItem(index, item) {\n    return item.config;\n  }\n  addWidget(id) {\n    const config = this.dashboardWidgetService.getWidgetById(id);\n    if (config) {\n      const width = this.getSupportedWidths(config)[0];\n      const widget = {\n        id,\n        config,\n        width\n      };\n      let targetRow;\n      if (this.widgetLayout && this.widgetLayout.length) {\n        targetRow = this.widgetLayout[this.widgetLayout.length - 1];\n      } else {\n        targetRow = [];\n        this.widgetLayout?.push(targetRow);\n      }\n      targetRow.push(widget);\n      this.recalculateLayout();\n    }\n  }\n  removeWidget(widget) {\n    widget.id = this.deletionMarker;\n    this.recalculateLayout();\n  }\n  drop(event) {\n    const {\n      currentIndex,\n      previousIndex,\n      previousContainer,\n      container\n    } = event;\n    if (previousIndex === currentIndex && previousContainer.data.index === container.data.index) {\n      // Nothing changed\n      return;\n    }\n    if (this.widgetLayout) {\n      const previousLayoutRow = this.widgetLayout[previousContainer.data.index];\n      const newLayoutRow = this.widgetLayout[container.data.index];\n      previousLayoutRow.splice(previousIndex, 1);\n      newLayoutRow.splice(currentIndex, 0, event.item.data);\n      this.recalculateLayout();\n    }\n  }\n  initLayout(availableIds) {\n    const savedLayoutDef = this.localStorageService.get('dashboardWidgetLayout');\n    let layoutDef;\n    if (savedLayoutDef) {\n      // validate all the IDs from the saved layout are still available\n      layoutDef = savedLayoutDef.filter(item => availableIds.includes(item.id));\n    }\n    return this.dashboardWidgetService.getWidgetLayout(layoutDef);\n  }\n  recalculateLayout() {\n    if (this.widgetLayout) {\n      const flattened = this.widgetLayout.reduce((flat, row) => [...flat, ...row], []).filter(item => item.id !== this.deletionMarker);\n      const newLayoutDef = flattened.map(item => ({\n        id: item.id,\n        width: item.width\n      }));\n      this.widgetLayout = this.dashboardWidgetService.getWidgetLayout(newLayoutDef);\n      this.localStorageService.set('dashboardWidgetLayout', newLayoutDef);\n      setTimeout(() => this.changedDetectorRef.markForCheck());\n    }\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.DashboardWidgetService), i0.ɵɵdirectiveInject(i1.LocalStorageService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"vdr-dashboard\"]],\n      standalone: false,\n      decls: 13,\n      vars: 8,\n      consts: [[1, \"widget-header\", \"mb-1\"], [\"vdrDropdownTrigger\", \"\", 1, \"btn\", \"btn-secondary\", \"btn-sm\"], [\"shape\", \"plus\"], [\"shape\", \"ellipsis-vertical\"], [\"vdrPosition\", \"bottom-right\"], [\"vdrDropdownItem\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"cdkDropListGroup\", \"\"], [\"class\", \"clr-row dashboard-row\", \"cdkDropList\", \"\", \"cdkDropListOrientation\", \"horizontal\", 3, \"cdkDropListData\", \"cdkDropListDropped\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"vdrDropdownItem\", \"\", 3, \"click\"], [\"cdkDropList\", \"\", \"cdkDropListOrientation\", \"horizontal\", 1, \"clr-row\", \"dashboard-row\", 3, \"cdkDropListDropped\", \"cdkDropListData\"], [\"class\", \"dashboard-item\", \"cdkDrag\", \"\", 3, \"ngClass\", \"cdkDragData\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"cdkDrag\", \"\", 1, \"dashboard-item\", 3, \"ngClass\", \"cdkDragData\"], [3, \"widgetConfig\", 4, \"vdrIfPermissions\"], [3, \"widgetConfig\"], [1, \"flex\"], [\"cdkDragHandle\", \"\", 1, \"drag-handle\"], [\"shape\", \"drag-handle\", \"size\", \"24\"], [\"vdrDropdownTrigger\", \"\", 1, \"icon-button\"], [1, \"dropdown-header\"], [\"vdrDropdownItem\", \"\", 3, \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"separator\", 1, \"dropdown-divider\"], [\"shape\", \"trash\", 1, \"is-danger\"], [\"vdrDropdownItem\", \"\", 3, \"click\", \"disabled\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-page-block\")(1, \"div\", 0)(2, \"vdr-dropdown\")(3, \"button\", 1);\n          i0.ɵɵelement(4, \"clr-icon\", 2);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵelement(7, \"clr-icon\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"vdr-dropdown-menu\", 4);\n          i0.ɵɵtemplate(9, DashboardComponent_button_9_Template, 3, 3, \"button\", 5);\n          i0.ɵɵpipe(10, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 6);\n          i0.ɵɵtemplate(12, DashboardComponent_div_12_Template, 2, 5, \"div\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"dashboard.add-widget\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(10, 6, ctx.availableWidgets$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.widgetLayout)(\"ngForTrackBy\", ctx.trackRow);\n        }\n      },\n      dependencies: [i2$1.ClrIconCustomTag, i3.NgClass, i3.NgForOf, i4.CdkDropList, i4.CdkDropListGroup, i4.CdkDrag, i4.CdkDragHandle, i1.DropdownComponent, i1.DropdownMenuComponent, i1.DropdownTriggerDirective, i1.DropdownItemDirective, i1.IfPermissionsDirective, i1.PageBlockComponent, DashboardWidgetComponent, i3.AsyncPipe, i2.TranslatePipe],\n      styles: [\".widget-header[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.placeholder[_ngcontent-%COMP%]{color:var(--color-grey-300);text-align:center}.placeholder[_ngcontent-%COMP%]   .version[_ngcontent-%COMP%]{font-size:3em;margin:24px;line-height:1em}.placeholder[_ngcontent-%COMP%]     .clr-i-outline{fill:var(--color-grey-200)}vdr-dashboard-widget[_ngcontent-%COMP%]{margin-bottom:24px}.drag-handle[_ngcontent-%COMP%]{cursor:move}.cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.dashboard-row[_ngcontent-%COMP%]{padding:0;border-width:1;margin-bottom:6px;transition:padding .2s,margin .2s}.dashboard-row.cdk-drop-list-dragging[_ngcontent-%COMP%], .dashboard-row.cdk-drop-list-receiving[_ngcontent-%COMP%]{border:2px dashed var(--color-component-border-200);border-radius:var(--border-radius);padding:6px}.dashboard-row.cdk-drop-list-dragging[_ngcontent-%COMP%]   .dashboard-item[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DashboardComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-dashboard',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-page-block>\\n    <div class=\\\"widget-header mb-1\\\">\\n        <vdr-dropdown>\\n            <button class=\\\"btn btn-secondary btn-sm\\\" vdrDropdownTrigger>\\n                <clr-icon shape=\\\"plus\\\"></clr-icon>\\n                {{ 'dashboard.add-widget' | translate }}\\n                <clr-icon shape=\\\"ellipsis-vertical\\\"></clr-icon>\\n            </button>\\n            <vdr-dropdown-menu vdrPosition=\\\"bottom-right\\\">\\n                <button\\n                    vdrDropdownItem\\n                    *ngFor=\\\"let widget of availableWidgets$ | async\\\"\\n                    (click)=\\\"addWidget(widget.id)\\\"\\n                >\\n                    {{ (widget.config.title ?? widget.id) | translate }}\\n                </button>\\n            </vdr-dropdown-menu>\\n        </vdr-dropdown>\\n    </div>\\n    <div cdkDropListGroup>\\n        <div\\n            class=\\\"clr-row dashboard-row\\\"\\n            *ngFor=\\\"let row of widgetLayout; index as rowIndex; trackBy: trackRow\\\"\\n            cdkDropList\\n            (cdkDropListDropped)=\\\"drop($event)\\\"\\n            cdkDropListOrientation=\\\"horizontal\\\"\\n            [cdkDropListData]=\\\"{ index: rowIndex }\\\"\\n        >\\n            <div\\n                *ngFor=\\\"let widget of row; trackBy: trackRowItem\\\"\\n                class=\\\"dashboard-item\\\"\\n                [ngClass]=\\\"getClassForWidth(widget.width)\\\"\\n                cdkDrag\\n                [cdkDragData]=\\\"widget\\\"\\n            >\\n                <vdr-dashboard-widget\\n                    *vdrIfPermissions=\\\"widget.config.requiresPermissions || null\\\"\\n                    [widgetConfig]=\\\"widget.config\\\"\\n                >\\n                    <div class=\\\"flex\\\">\\n                        <div class=\\\"drag-handle\\\" cdkDragHandle>\\n                            <clr-icon shape=\\\"drag-handle\\\" size=\\\"24\\\"></clr-icon>\\n                        </div>\\n                        <vdr-dropdown>\\n                            <button class=\\\"icon-button\\\" vdrDropdownTrigger>\\n                                <clr-icon shape=\\\"ellipsis-vertical\\\"></clr-icon>\\n                            </button>\\n                            <vdr-dropdown-menu vdrPosition=\\\"bottom-right\\\">\\n                                <h4 class=\\\"dropdown-header\\\">{{ 'dashboard.widget-resize' | translate }}</h4>\\n                                <button\\n                                    vdrDropdownItem\\n                                    [disabled]=\\\"width === widget.width\\\"\\n                                    *ngFor=\\\"let width of getSupportedWidths(widget.config)\\\"\\n                                    (click)=\\\"setWidgetWidth(widget, width)\\\"\\n                                >\\n                                    {{ 'dashboard.widget-width' | translate : { width: width } }}\\n                                </button>\\n                                <div class=\\\"dropdown-divider\\\" role=\\\"separator\\\"></div>\\n                                <button vdrDropdownItem (click)=\\\"removeWidget(widget)\\\">\\n                                    <clr-icon shape=\\\"trash\\\" class=\\\"is-danger\\\"></clr-icon>\\n                                    {{ 'dashboard.remove-widget' | translate }}\\n                                </button>\\n                            </vdr-dropdown-menu>\\n                        </vdr-dropdown>\\n                    </div>\\n                </vdr-dashboard-widget>\\n            </div>\\n        </div>\\n    </div>\\n</vdr-page-block>\\n\",\n      styles: [\".widget-header{display:flex;justify-content:flex-end}.placeholder{color:var(--color-grey-300);text-align:center}.placeholder .version{font-size:3em;margin:24px;line-height:1em}.placeholder ::ng-deep .clr-i-outline{fill:var(--color-grey-200)}vdr-dashboard-widget{margin-bottom:24px}.drag-handle{cursor:move}.cdk-drag-preview{box-sizing:border-box;border-radius:4px}.cdk-drag-placeholder{opacity:0}.cdk-drag-animating{transition:transform .25s cubic-bezier(0,0,.2,1)}.dashboard-row{padding:0;border-width:1;margin-bottom:6px;transition:padding .2s,margin .2s}.dashboard-row.cdk-drop-list-dragging,.dashboard-row.cdk-drop-list-receiving{border:2px dashed var(--color-component-border-200);border-radius:var(--border-radius);padding:6px}.dashboard-row.cdk-drop-list-dragging .dashboard-item:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DashboardWidgetService\n  }, {\n    type: i1.LocalStorageService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.DataService\n  }], null);\n})();\nconst dashboardRoutes = [{\n  path: '',\n  component: DashboardComponent,\n  pathMatch: 'full'\n}];\nconst GET_LATEST_ORDERS = gql`\n    query GetLatestOrders($options: OrderListOptions) {\n        orders(options: $options) {\n            items {\n                id\n                createdAt\n                updatedAt\n                type\n                orderPlacedAt\n                code\n                state\n                total\n                totalWithTax\n                currencyCode\n                customer {\n                    id\n                    firstName\n                    lastName\n                }\n            }\n        }\n    }\n`;\nclass LatestOrdersWidgetComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n  }\n  ngOnInit() {\n    this.latestOrders$ = this.dataService.query(GetLatestOrdersDocument, {\n      options: {\n        take: 10,\n        filter: {\n          active: {\n            eq: false\n          },\n          state: {\n            notIn: ['Cancelled', 'Draft']\n          }\n        },\n        sort: {\n          orderPlacedAt: SortOrder.DESC\n        }\n      }\n    }).refetchOnChannelChange().mapStream(data => data.orders.items);\n  }\n  static {\n    this.ɵfac = function LatestOrdersWidgetComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LatestOrdersWidgetComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: LatestOrdersWidgetComponent,\n      selectors: [[\"vdr-latest-orders-widget\"]],\n      standalone: false,\n      decls: 17,\n      vars: 20,\n      consts: [[\"id\", \"latest-orders-widget-list\", 3, \"items\"], [\"id\", \"code\", 3, \"heading\"], [\"id\", \"state\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"customer\", 3, \"heading\", \"hiddenByDefault\"], [\"id\", \"total\", 3, \"heading\"], [\"id\", \"placed-at\", 3, \"heading\"], [1, \"button-ghost\", 3, \"routerLink\"], [\"shape\", \"arrow right\"], [3, \"state\"], [3, \"customer\"]],\n      template: function LatestOrdersWidgetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"vdr-data-table-2\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"vdr-dt2-column\", 1);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵtemplate(4, LatestOrdersWidgetComponent_ng_template_4_Template, 4, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"vdr-dt2-column\", 2);\n          i0.ɵɵpipe(6, \"translate\");\n          i0.ɵɵtemplate(7, LatestOrdersWidgetComponent_ng_template_7_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"vdr-dt2-column\", 3);\n          i0.ɵɵpipe(9, \"translate\");\n          i0.ɵɵtemplate(10, LatestOrdersWidgetComponent_ng_template_10_Template, 1, 1, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"vdr-dt2-column\", 4);\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵtemplate(13, LatestOrdersWidgetComponent_ng_template_13_Template, 2, 4, \"ng-template\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"vdr-dt2-column\", 5);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵtemplate(16, LatestOrdersWidgetComponent_ng_template_16_Template, 2, 3, \"ng-template\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(1, 8, ctx.latestOrders$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(3, 10, \"common.code\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(6, 12, \"order.state\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(9, 14, \"customer.customer\"))(\"hiddenByDefault\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(12, 16, \"order.total\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"heading\", i0.ɵɵpipeBind1(15, 18, \"order.placed-at\"));\n        }\n      },\n      dependencies: [i2$1.ClrIconCustomTag, i3$1.RouterLink, i1.CustomerLabelComponent, i1.OrderStateLabelComponent, i1.DataTable2Component, i1.DataTable2ColumnComponent, i3.AsyncPipe, i2.TranslatePipe, i1.TimeAgoPipe, i1.LocaleCurrencyPipe],\n      styles: [\"vdr-data-table[_ngcontent-%COMP%]     table{margin-top:0}vdr-order-state-label[_ngcontent-%COMP%]{display:inline-block;margin-top:2px}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LatestOrdersWidgetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-latest-orders-widget',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-data-table-2 [items]=\\\"latestOrders$ | async\\\" id=\\\"latest-orders-widget-list\\\">\\n    <vdr-dt2-column [heading]=\\\"'common.code' | translate\\\" id=\\\"code\\\">\\n        <ng-template let-order=\\\"item\\\">\\n            <a class=\\\"button-ghost\\\" [routerLink]=\\\"['/orders/', order.id]\\\"\\n                ><span>{{ order.code }}</span>\\n                <clr-icon shape=\\\"arrow right\\\"></clr-icon>\\n            </a>\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'order.state' | translate\\\" id=\\\"state\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-order=\\\"item\\\">\\n            <vdr-order-state-label [state]=\\\"order.state\\\"></vdr-order-state-label>\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'customer.customer' | translate\\\" id=\\\"customer\\\" [hiddenByDefault]=\\\"true\\\">\\n        <ng-template let-order=\\\"item\\\">\\n            <vdr-customer-label [customer]=\\\"order.customer\\\"></vdr-customer-label>\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'order.total' | translate\\\" id=\\\"total\\\">\\n        <ng-template let-order=\\\"item\\\">\\n            {{ order.totalWithTax | localeCurrency : order.currencyCode }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n    <vdr-dt2-column [heading]=\\\"'order.placed-at' | translate\\\" id=\\\"placed-at\\\">\\n        <ng-template let-order=\\\"item\\\">\\n            {{ order.orderPlacedAt | timeAgo }}\\n        </ng-template>\\n    </vdr-dt2-column>\\n</vdr-data-table-2>\\n\",\n      styles: [\"vdr-data-table ::ng-deep table{margin-top:0}vdr-order-state-label{display:inline-block;margin-top:2px}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DataService\n  }], null);\n})();\nclass LatestOrdersWidgetModule {\n  static {\n    this.ɵfac = function LatestOrdersWidgetModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LatestOrdersWidgetModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LatestOrdersWidgetModule,\n      declarations: [LatestOrdersWidgetComponent],\n      imports: [CoreModule, SharedModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule, SharedModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LatestOrdersWidgetModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule, SharedModule],\n      declarations: [LatestOrdersWidgetComponent]\n    }]\n  }], null, null);\n})();\nconst GET_ORDER_CHART_DATA = gql`\n    query GetOrderChartData($refresh: Boolean, $types: [MetricType!]!) {\n        metricSummary(input: { interval: Daily, types: $types, refresh: $refresh }) {\n            interval\n            type\n            entries {\n                label\n                value\n            }\n        }\n    }\n`;\nclass OrderChartWidgetComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.refresh$ = new Subject();\n    this.metricType$ = new BehaviorSubject(MetricType.OrderTotal);\n    this.MetricType = MetricType;\n  }\n  ngOnInit() {\n    const currencyCode$ = this.dataService.settings.getActiveChannel().refetchOnChannelChange().mapStream(data => data.activeChannel.defaultCurrencyCode || undefined);\n    const uiState$ = this.dataService.client.uiState().mapStream(data => data.uiState);\n    const metricType$ = this.metricType$.pipe(distinctUntilChanged());\n    this.metrics$ = combineLatest(metricType$, currencyCode$, uiState$).pipe(switchMap(([metricType, currencyCode, uiState]) => this.refresh$.pipe(startWith(false), switchMap(refresh => this.dataService.query(GetOrderChartDataDocument, {\n      types: [metricType],\n      refresh\n    }).mapSingle(data => data.metricSummary).pipe(map(metrics => {\n      const formatValueAs = metricType === MetricType.OrderCount ? 'number' : 'currency';\n      const locale = `${uiState.language}-${uiState.locale}`;\n      const formatOptions = {\n        formatValueAs,\n        currencyCode,\n        locale\n      };\n      return metrics.find(m => m.type === metricType)?.entries.map(entry => ({\n        ...entry,\n        formatOptions\n      })) ?? [];\n    }))))));\n  }\n  refresh() {\n    this.refresh$.next(true);\n  }\n  static {\n    this.ɵfac = function OrderChartWidgetComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OrderChartWidgetComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OrderChartWidgetComponent,\n      selectors: [[\"vdr-order-chart-widget\"]],\n      standalone: false,\n      decls: 4,\n      vars: 6,\n      consts: [[3, \"entries\"], [\"class\", \"flex\", 4, \"ngIf\"], [1, \"flex\"], [1, \"button-small\", 3, \"click\"], [1, \"ml-1\", \"button-small\", 3, \"click\"], [1, \"flex-spacer\"], [\"shape\", \"refresh\"]],\n      template: function OrderChartWidgetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"vdr-chart\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵtemplate(2, OrderChartWidgetComponent_div_2_Template, 13, 15, \"div\", 1);\n          i0.ɵɵpipe(3, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"entries\", i0.ɵɵpipeBind1(1, 2, ctx.metrics$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 4, ctx.metricType$));\n        }\n      },\n      dependencies: [i2$1.ClrIconCustomTag, i3.NgIf, i1.ChartComponent, i3.AsyncPipe, i2.TranslatePipe],\n      styles: [\".button-small.active[_ngcontent-%COMP%]{background-color:var(--color-primary-200);color:var(--color-primary-900)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OrderChartWidgetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-order-chart-widget',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<vdr-chart [entries]=\\\"metrics$ | async\\\" />\\n<div class=\\\"flex\\\" *ngIf=\\\"metricType$ | async as activeMetricType\\\">\\n    <button\\n        class=\\\"button-small\\\"\\n        (click)=\\\"metricType$.next(MetricType.OrderTotal)\\\"\\n        [class.active]=\\\"activeMetricType === MetricType.OrderTotal\\\"\\n    >\\n        {{ 'dashboard.metric-order-total-value' | translate }}\\n    </button>\\n    <button\\n        class=\\\"ml-1 button-small\\\"\\n        (click)=\\\"metricType$.next(MetricType.OrderCount)\\\"\\n        [class.active]=\\\"activeMetricType === MetricType.OrderCount\\\"\\n    >\\n        {{ 'dashboard.metric-number-of-orders' | translate }}\\n    </button>\\n    <button\\n        class=\\\"ml-1 button-small\\\"\\n        (click)=\\\"metricType$.next(MetricType.AverageOrderValue)\\\"\\n        [class.active]=\\\"activeMetricType === MetricType.AverageOrderValue\\\"\\n    >\\n        {{ 'dashboard.metric-average-order-value' | translate }}\\n    </button>\\n    <div class=\\\"flex-spacer\\\"></div>\\n    <button class=\\\"button-small\\\" (click)=\\\"refresh()\\\">\\n        <clr-icon shape=\\\"refresh\\\"></clr-icon>\\n    </button>\\n</div>\\n\",\n      styles: [\".button-small.active{background-color:var(--color-primary-200);color:var(--color-primary-900)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DataService\n  }], null);\n})();\nconst GET_ORDER_SUMMARY = gql`\n    query GetOrderSummary($start: DateTime!, $end: DateTime!) {\n        orders(options: { filter: { orderPlacedAt: { between: { start: $start, end: $end } } } }) {\n            totalItems\n            items {\n                id\n                totalWithTax\n                currencyCode\n            }\n        }\n    }\n`;\nclass OrderSummaryWidgetComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.today = new Date();\n    this.yesterday = new Date(new Date().setDate(this.today.getDate() - 1));\n    this.selection$ = new BehaviorSubject({\n      timeframe: 'day',\n      date: this.today\n    });\n  }\n  ngOnInit() {\n    this.dateRange$ = this.selection$.pipe(distinctUntilChanged(), map(selection => ({\n      start: dayjs(selection.date).startOf(selection.timeframe).toDate(),\n      end: dayjs(selection.date).endOf(selection.timeframe).toDate()\n    })), shareReplay(1));\n    const orderSummary$ = this.dateRange$.pipe(switchMap$1(({\n      start,\n      end\n    }) => this.dataService.query(GetOrderSummaryDocument, {\n      start: start.toISOString(),\n      end: end.toISOString()\n    }).refetchOnChannelChange().mapStream(data => data.orders)), shareReplay(1));\n    this.totalOrderCount$ = orderSummary$.pipe(map(res => res.totalItems));\n    this.totalOrderValue$ = orderSummary$.pipe(map(res => res.items.reduce((total, order) => total + order.totalWithTax, 0)));\n    this.currencyCode$ = this.dataService.settings.getActiveChannel().refetchOnChannelChange().mapStream(data => data.activeChannel.defaultCurrencyCode || undefined);\n  }\n  static {\n    this.ɵfac = function OrderSummaryWidgetComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OrderSummaryWidgetComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OrderSummaryWidgetComponent,\n      selectors: [[\"vdr-order-summary-widget\"]],\n      standalone: false,\n      decls: 22,\n      vars: 23,\n      consts: [[1, \"stats\"], [1, \"stat\"], [1, \"stat-figure\"], [1, \"stat-label\"], [1, \"footer\"], [\"class\", \"flex\", 4, \"ngIf\"], [\"class\", \"date-range\", 4, \"ngIf\"], [1, \"flex\"], [1, \"button-small\", 3, \"click\"], [1, \"ml-1\", \"button-small\", 3, \"click\"], [1, \"date-range\"]],\n      template: function OrderSummaryWidgetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵpipe(4, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 1)(9, \"div\", 2);\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"async\");\n          i0.ɵɵpipe(12, \"async\");\n          i0.ɵɵpipe(13, \"localeCurrency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 3);\n          i0.ɵɵtext(15);\n          i0.ɵɵpipe(16, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 4);\n          i0.ɵɵtemplate(18, OrderSummaryWidgetComponent_div_18_Template, 13, 20, \"div\", 5);\n          i0.ɵɵpipe(19, \"async\");\n          i0.ɵɵtemplate(20, OrderSummaryWidgetComponent_div_20_Template, 4, 6, \"div\", 6);\n          i0.ɵɵpipe(21, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, ctx.totalOrderCount$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 8, \"dashboard.total-orders\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 14, i0.ɵɵpipeBind1(11, 10, ctx.totalOrderValue$), i0.ɵɵpipeBind1(12, 12, ctx.currencyCode$) || undefined), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 17, \"dashboard.total-order-value\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(19, 19, ctx.selection$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(21, 21, ctx.dateRange$));\n        }\n      },\n      dependencies: [i3.NgIf, i3.AsyncPipe, i2.TranslatePipe, i1.LocaleDatePipe, i1.LocaleCurrencyPipe],\n      styles: [\".stats[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly}.stat[_ngcontent-%COMP%]{text-align:center}.stat-figure[_ngcontent-%COMP%]{font-size:2rem;line-height:3rem}.stat-label[_ngcontent-%COMP%]{text-transform:uppercase}.date-range[_ngcontent-%COMP%]{margin-top:calc(var(--space-unit) * 3);font-size:var(--font-size-xs)}.footer[_ngcontent-%COMP%]{margin-top:24px;display:flex;flex-direction:column;justify-content:space-between}.button-small.active[_ngcontent-%COMP%]{background-color:var(--color-primary-200);color:var(--color-primary-900)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OrderSummaryWidgetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-order-summary-widget',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<div class=\\\"stats\\\">\\n    <div class=\\\"stat\\\">\\n        <div class=\\\"stat-figure\\\">{{ totalOrderCount$ | async }}</div>\\n        <div class=\\\"stat-label\\\">{{ 'dashboard.total-orders' | translate }}</div>\\n    </div>\\n    <div class=\\\"stat\\\">\\n        <div class=\\\"stat-figure\\\">\\n            {{ totalOrderValue$ | async | localeCurrency: (currencyCode$ | async) || undefined }}\\n        </div>\\n        <div class=\\\"stat-label\\\">{{ 'dashboard.total-order-value' | translate }}</div>\\n    </div>\\n</div>\\n<div class=\\\"footer\\\">\\n    <div class=\\\"flex\\\" *ngIf=\\\"selection$ | async as selection\\\">\\n        <button class=\\\"button-small\\\" [class.active]=\\\"selection.date === today\\\" (click)=\\\"selection$.next({timeframe: 'day', date: today})\\\">\\n            {{ 'dashboard.today' | translate }}\\n        </button>\\n        <button class=\\\"ml-1 button-small\\\" [class.active]=\\\"selection.date === yesterday\\\" (click)=\\\"selection$.next({timeframe: 'day', date: yesterday})\\\">\\n            {{ 'dashboard.yesterday' | translate }}\\n        </button>\\n        <button class=\\\"ml-1 button-small\\\" [class.active]=\\\"selection.timeframe === 'week'\\\" (click)=\\\"selection$.next({timeframe: 'week'})\\\">\\n            {{ 'dashboard.thisWeek' | translate }}\\n        </button>\\n        <button class=\\\"ml-1 button-small\\\" [class.active]=\\\"selection.timeframe === 'month'\\\" (click)=\\\"selection$.next({timeframe: 'month'})\\\">\\n            {{ 'dashboard.thisMonth' | translate }}\\n        </button>\\n    </div>\\n\\n    <div class=\\\"date-range\\\" *ngIf=\\\"dateRange$ | async as range\\\">\\n        {{ range.start | localeDate }} - {{ range.end | localeDate }}\\n    </div>\\n</div>\\n\",\n      styles: [\".stats{display:flex;justify-content:space-evenly}.stat{text-align:center}.stat-figure{font-size:2rem;line-height:3rem}.stat-label{text-transform:uppercase}.date-range{margin-top:calc(var(--space-unit) * 3);font-size:var(--font-size-xs)}.footer{margin-top:24px;display:flex;flex-direction:column;justify-content:space-between}.button-small.active{background-color:var(--color-primary-200);color:var(--color-primary-900)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DataService\n  }], null);\n})();\nclass OrderSummaryWidgetModule {\n  static {\n    this.ɵfac = function OrderSummaryWidgetModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OrderSummaryWidgetModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OrderSummaryWidgetModule,\n      declarations: [OrderSummaryWidgetComponent],\n      imports: [CoreModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OrderSummaryWidgetModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule],\n      declarations: [OrderSummaryWidgetComponent]\n    }]\n  }], null, null);\n})();\nconst DEFAULT_DASHBOARD_WIDGET_LAYOUT = [{\n  id: 'metrics',\n  width: 12\n}, {\n  id: 'orderSummary',\n  width: 6\n}, {\n  id: 'latestOrders',\n  width: 6\n}];\nconst DEFAULT_WIDGETS = {\n  metrics: {\n    title: marker('dashboard.metrics'),\n    supportedWidths: [6, 8, 12],\n    loadComponent: () => OrderChartWidgetComponent,\n    requiresPermissions: [Permission.ReadOrder]\n  },\n  orderSummary: {\n    title: marker('dashboard.orders-summary'),\n    loadComponent: () => OrderSummaryWidgetComponent,\n    supportedWidths: [4, 6, 8, 12],\n    requiresPermissions: [Permission.ReadOrder]\n  },\n  latestOrders: {\n    title: marker('dashboard.latest-orders'),\n    loadComponent: () => LatestOrdersWidgetComponent,\n    supportedWidths: [6, 8, 12],\n    requiresPermissions: [Permission.ReadOrder]\n  }\n};\nclass DashboardModule {\n  constructor(dashboardWidgetService) {\n    Object.entries(DEFAULT_WIDGETS).map(([id, config]) => {\n      if (!dashboardWidgetService.getWidgetById(id)) {\n        dashboardWidgetService.registerWidget(id, config);\n      }\n    });\n    if (dashboardWidgetService.getDefaultLayout().length === 0) {\n      dashboardWidgetService.setDefaultLayout(DEFAULT_DASHBOARD_WIDGET_LAYOUT);\n    }\n  }\n  static {\n    this.ɵfac = function DashboardModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardModule)(i0.ɵɵinject(i1.DashboardWidgetService));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DashboardModule,\n      declarations: [DashboardComponent, DashboardWidgetComponent, OrderChartWidgetComponent],\n      imports: [SharedModule, i3$1.RouterModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(dashboardRoutes)]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DashboardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [SharedModule, RouterModule.forChild(dashboardRoutes)],\n      declarations: [DashboardComponent, DashboardWidgetComponent, OrderChartWidgetComponent]\n    }]\n  }], () => [{\n    type: i1.DashboardWidgetService\n  }], null);\n})();\nclass TestWidgetComponent {\n  static {\n    this.ɵfac = function TestWidgetComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TestWidgetComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TestWidgetComponent,\n      selectors: [[\"vdr-test-widget\"]],\n      standalone: false,\n      decls: 2,\n      vars: 0,\n      template: function TestWidgetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"This is a test widget!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestWidgetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-test-widget',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<p>This is a test widget!</p>\\n\"\n    }]\n  }], null, null);\n})();\nclass TestWidgetModule {\n  static {\n    this.ɵfac = function TestWidgetModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TestWidgetModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TestWidgetModule,\n      declarations: [TestWidgetComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestWidgetModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TestWidgetComponent]\n    }]\n  }], null, null);\n})();\nclass WelcomeWidgetComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.version = ADMIN_UI_VERSION;\n    this.brand = getAppConfig().brand;\n    this.hideVendureBranding = getAppConfig().hideVendureBranding;\n    this.hideVersion = getAppConfig().hideVersion;\n  }\n  ngOnInit() {\n    this.administrator$ = this.dataService.administrator.getActiveAdministrator().mapStream(data => data.activeAdministrator || null);\n  }\n  static {\n    this.ɵfac = function WelcomeWidgetComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WelcomeWidgetComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: WelcomeWidgetComponent,\n      selectors: [[\"vdr-welcome-widget\"]],\n      standalone: false,\n      decls: 4,\n      vars: 3,\n      consts: [[4, \"ngIf\"], [1, \"placeholder\"], [\"shape\", \"line-chart\", \"size\", \"128\"], [1, \"\"], [1, \"p5\"], [\"class\", \"p5\", 4, \"ngIf\"]],\n      template: function WelcomeWidgetComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, WelcomeWidgetComponent_div_0_Template, 8, 6, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"clr-icon\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.administrator$));\n        }\n      },\n      dependencies: [i2$1.ClrIconCustomTag, i3.NgIf, i3.AsyncPipe, i1.TimeAgoPipe],\n      styles: [\"[_nghost-%COMP%]{display:flex;justify-content:space-between}.placeholder[_ngcontent-%COMP%]{color:var(--color-grey-200)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(WelcomeWidgetComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-welcome-widget',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: false,\n      template: \"<div *ngIf=\\\"administrator$ | async as administrator\\\">\\n    <h4 class=\\\"\\\">\\n        Welcome, {{ administrator.firstName }} {{ administrator.lastName }}<br />\\n        <small class=\\\"p5\\\">Last login: {{ administrator.user.lastLogin | timeAgo }}</small>\\n    </h4>\\n\\n    <p class=\\\"p5\\\" *ngIf=\\\"!hideVendureBranding || !hideVersion\\\">\\n        {{ hideVendureBranding ? '' : 'Vendure' }} {{ hideVersion ? '' : ('Admin UI v' + version) }}\\n    </p>\\n</div>\\n<div class=\\\"placeholder\\\">\\n    <clr-icon shape=\\\"line-chart\\\" size=\\\"128\\\"></clr-icon>\\n</div>\\n\",\n      styles: [\":host{display:flex;justify-content:space-between}.placeholder{color:var(--color-grey-200)}\\n\"]\n    }]\n  }], () => [{\n    type: i1.DataService\n  }], null);\n})();\nclass WelcomeWidgetModule {\n  static {\n    this.ɵfac = function WelcomeWidgetModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WelcomeWidgetModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: WelcomeWidgetModule,\n      declarations: [WelcomeWidgetComponent],\n      imports: [CoreModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(WelcomeWidgetModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule],\n      declarations: [WelcomeWidgetComponent]\n    }]\n  }], null, null);\n})();\n\n// This file was generated by the build-public-api.ts script\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_DASHBOARD_WIDGET_LAYOUT, DEFAULT_WIDGETS, DashboardComponent, DashboardModule, DashboardWidgetComponent, GET_ORDER_CHART_DATA, GET_ORDER_SUMMARY, LatestOrdersWidgetComponent, LatestOrdersWidgetModule, OrderChartWidgetComponent, OrderSummaryWidgetComponent, OrderSummaryWidgetModule, TestWidgetComponent, TestWidgetModule, WelcomeWidgetComponent, WelcomeWidgetModule, dashboardRoutes };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,0BAA4B;AAW5B,mBAAkB;AAClB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAAC;AACnE,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AACT;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,+DAA+D;AAC7F,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,UAAU,EAAE,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,YAAY,IAAI;AACtB,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,UAAU,UAAU,OAAO,WAAW,QAAQ,YAAY,SAAY,UAAU,UAAU,EAAE,GAAG,GAAG;AAAA,EACrJ;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,oGAAoG;AAClI,YAAM,WAAc,cAAc,GAAG,EAAE;AACvC,YAAM,YAAe,cAAc,CAAC,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,eAAe,WAAW,QAAQ,CAAC;AAAA,IAClE,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,YAAe,cAAc,CAAC,EAAE;AACtC,IAAG,WAAW,YAAY,aAAa,UAAU,KAAK;AACtD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,0BAA6B,gBAAgB,GAAG,KAAK,QAAQ,CAAC,GAAG,GAAG;AAAA,EACtH;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,wBAAwB,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC3E,IAAG,UAAU,GAAG,YAAY,EAAE;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE;AACpD,IAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,qBAAqB,CAAC,EAAE,GAAG,MAAM,EAAE;AACxD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa;AAChB,IAAG,WAAW,IAAI,2EAA2E,GAAG,GAAG,UAAU,EAAE;AAC/G,IAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,IAAG,eAAe,IAAI,UAAU,CAAC;AACjC,IAAG,WAAW,SAAS,SAAS,2FAA2F;AACzH,MAAG,cAAc,GAAG;AACpB,YAAM,YAAe,cAAc,EAAE;AACrC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,SAAS,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,UAAU,IAAI,YAAY,EAAE;AAC/B,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa,EAAE,EAAE,EAAE,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,EAAE;AACrC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,gBAAgB,UAAU,MAAM;AAC9C,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,GAAG,yBAAyB,CAAC;AACrE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,mBAAmB,UAAU,MAAM,CAAC;AACpE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,IAAI,GAAG,yBAAyB,GAAG,GAAG;AAAA,EAClF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,iEAAiE,IAAI,GAAG,wBAAwB,EAAE;AACnH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,iBAAiB,UAAU,KAAK,CAAC,EAAE,eAAe,SAAS;AAC3F,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,UAAU,OAAO,uBAAuB,IAAI;AAAA,EAChF;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,sBAAsB,SAAS,qEAAqE,QAAQ;AACxH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,KAAK,MAAM,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,OAAO,EAAE;AAC1E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAS,IAAI;AACnB,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,mBAAsB,gBAAgB,GAAG,KAAK,YAAY,CAAC;AACzE,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,MAAM,EAAE,gBAAgB,OAAO,YAAY;AAAA,EACtE;AACF;AACA,IAAM,MAAM,QAAM,CAAC,YAAY,EAAE;AACjC,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC,EAAE,GAAG,MAAM;AACtC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,SAAS,EAAE,CAAC;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,SAAS,IAAI;AAAA,EACpC;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,CAAC;AAAA,EAC5C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,SAAS,SAAS,KAAK;AAAA,EACvC;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,sBAAsB,CAAC;AAAA,EACzC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,YAAY,SAAS,QAAQ;AAAA,EAC7C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,gBAAgB;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,SAAS,cAAc,SAAS,YAAY,GAAG,GAAG;AAAA,EACpG;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,SAAS;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,SAAS,aAAa,GAAG,GAAG;AAAA,EAC9E;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,UAAU,CAAC;AAC7C,IAAG,WAAW,SAAS,SAAS,mEAAmE;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,KAAK,OAAO,WAAW,UAAU,CAAC;AAAA,IAC7E,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,mEAAmE;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,KAAK,OAAO,WAAW,UAAU,CAAC;AAAA,IAC7E,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,mEAAmE;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,KAAK,OAAO,WAAW,iBAAiB,CAAC;AAAA,IACpF,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,UAAU,IAAI,OAAO,CAAC;AACzB,IAAG,eAAe,IAAI,UAAU,CAAC;AACjC,IAAG,WAAW,SAAS,SAAS,oEAAoE;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,IAAG,UAAU,IAAI,YAAY,CAAC;AAC9B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,sBAAsB,IAAI;AAChC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,YAAY,UAAU,wBAAwB,OAAO,WAAW,UAAU;AAC7E,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,oCAAoC,GAAG,GAAG;AAC1F,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,UAAU,wBAAwB,OAAO,WAAW,UAAU;AAC7E,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,mCAAmC,GAAG,GAAG;AAC1F,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,UAAU,wBAAwB,OAAO,WAAW,iBAAiB;AACpF,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,sCAAsC,GAAG,GAAG;AAAA,EAC/F;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,UAAU,CAAC;AAC7C,IAAG,WAAW,SAAS,SAAS,sEAAsE;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK;AAAA,QAC3C,WAAW;AAAA,QACX,MAAM,OAAO;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,sEAAsE;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK;AAAA,QAC3C,WAAW;AAAA,QACX,MAAM,OAAO;AAAA,MACf,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,sEAAsE;AACpG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK;AAAA,QAC3C,WAAW;AAAA,MACb,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,WAAW;AACxB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,CAAC;AACjC,IAAG,WAAW,SAAS,SAAS,uEAAuE;AACrG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,KAAK;AAAA,QAC3C,WAAW;AAAA,MACb,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,YAAY,UAAU,aAAa,SAAS,OAAO,KAAK;AAC3D,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,iBAAiB,GAAG,GAAG;AACxE,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,UAAU,aAAa,SAAS,OAAO,SAAS;AAC/D,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,qBAAqB,GAAG,GAAG;AAC5E,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,UAAU,aAAa,cAAc,MAAM;AAC1D,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,oBAAoB,GAAG,GAAG;AAC3E,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,UAAU,aAAa,cAAc,OAAO;AAC3D,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,qBAAqB,GAAG,GAAG;AAAA,EAC/E;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,YAAY;AACzB,IAAG,OAAO,GAAG,YAAY;AACzB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,SAAS,KAAK,GAAG,OAAU,YAAY,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG;AAAA,EACjH;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,sBAAsB,KAAK,WAAW,KAAK,OAAO,cAAc,KAAK,eAAe,OAAO,SAAS,GAAG;AAAA,EAC3I;AACF;AACA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC;AACtC,IAAG,OAAO,CAAC;AACX,IAAG,UAAU,GAAG,IAAI;AACpB,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,SAAS;AACtB,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,KAAK,CAAC;AACxE,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAmB,IAAI;AAC7B,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,cAAc,iBAAiB,WAAW,KAAK,iBAAiB,UAAU,EAAE;AAClG,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,gBAAmB,YAAY,GAAG,GAAG,iBAAiB,KAAK,SAAS,GAAG,EAAE;AAC/F,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,uBAAuB,CAAC,OAAO,WAAW;AAAA,EAC1E;AACF;AACA,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,kBAAkB;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA,EACM,aAAa;AAAA;AACjB,YAAM,sBAAsB,KAAK,aAAa,cAAc;AAC5D,YAAM,gBAAgB,+BAA+B,UAAU,MAAM,sBAAsB;AAC3F,WAAK,eAAe,KAAK,OAAO,gBAAgB,aAAa;AAC7D,WAAK,aAAa,kBAAkB,cAAc;AAAA,IACpD;AAAA;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,QAAQ;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,GAAG,gBAAgB;AAAA,QACzC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,MAC9D,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,YAAY,CAAC;AAClC,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACrM,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,WAAW,SAAY,YAAY,GAAG,IAAI,UAAU,IAAI,aAAa,WAAW,QAAQ,YAAY,SAAY,UAAU,EAAE,CAAC;AAAA,QAClI;AAAA,MACF;AAAA,MACA,cAAc,CAAI,eAAkB,uBAA0B,aAAa;AAAA,MAC3E,QAAQ,CAAC,mKAAmK;AAAA,MAC5K,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,oHAAoH;AAAA,IAC/H,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,wBAAwB,qBAAqB,oBAAoB,aAAa;AACxF,SAAK,yBAAyB;AAC9B,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,WAAW,YAAY;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,oBAAoB,KAAK,YAAY,OAAO,WAAW,EAAE,QAAQ,KAAK,IAAI,CAAC;AAAA,MAC9E;AAAA,IACF,MAAM,WAAW,WAAW,GAAG,IAAI,iBAAe,KAAK,uBAAuB,oBAAoB,WAAW,CAAC,GAAG,IAAI,aAAW,KAAK,eAAe,KAAK,WAAW,QAAQ,IAAI,OAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5L,SAAK,SAAS,sBAAsB;AAAA,EACtC;AAAA,EACA,iBAAiB,OAAO;AACtB,YAAQ,OAAO;AAAA,MACb,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,6CAAY,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,mBAAmB,QAAQ;AACzB,WAAO,OAAO,mBAAmB,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE;AAAA,EAClD;AAAA,EACA,eAAe,QAAQ,OAAO;AAC5B,WAAO,QAAQ;AACf,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,SAAS,OAAO,KAAK;AACnB,UAAM,KAAK,IAAI,IAAI,UAAQ,GAAG,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE,KAAK,GAAG;AAC/D,WAAO;AAAA,EACT;AAAA,EACA,aAAa,OAAO,MAAM;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU,IAAI;AACZ,UAAM,SAAS,KAAK,uBAAuB,cAAc,EAAE;AAC3D,QAAI,QAAQ;AACV,YAAM,QAAQ,KAAK,mBAAmB,MAAM,EAAE,CAAC;AAC/C,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,UAAI;AACJ,UAAI,KAAK,gBAAgB,KAAK,aAAa,QAAQ;AACjD,oBAAY,KAAK,aAAa,KAAK,aAAa,SAAS,CAAC;AAAA,MAC5D,OAAO;AACL,oBAAY,CAAC;AACb,aAAK,cAAc,KAAK,SAAS;AAAA,MACnC;AACA,gBAAU,KAAK,MAAM;AACrB,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,KAAK,KAAK;AACjB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,KAAK,OAAO;AACV,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,kBAAkB,gBAAgB,kBAAkB,KAAK,UAAU,UAAU,KAAK,OAAO;AAE3F;AAAA,IACF;AACA,QAAI,KAAK,cAAc;AACrB,YAAM,oBAAoB,KAAK,aAAa,kBAAkB,KAAK,KAAK;AACxE,YAAM,eAAe,KAAK,aAAa,UAAU,KAAK,KAAK;AAC3D,wBAAkB,OAAO,eAAe,CAAC;AACzC,mBAAa,OAAO,cAAc,GAAG,MAAM,KAAK,IAAI;AACpD,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,WAAW,cAAc;AACvB,UAAM,iBAAiB,KAAK,oBAAoB,IAAI,uBAAuB;AAC3E,QAAI;AACJ,QAAI,gBAAgB;AAElB,kBAAY,eAAe,OAAO,UAAQ,aAAa,SAAS,KAAK,EAAE,CAAC;AAAA,IAC1E;AACA,WAAO,KAAK,uBAAuB,gBAAgB,SAAS;AAAA,EAC9D;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,cAAc;AACrB,YAAM,YAAY,KAAK,aAAa,OAAO,CAAC,MAAM,QAAQ,CAAC,GAAG,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO,UAAQ,KAAK,OAAO,KAAK,cAAc;AAC/H,YAAM,eAAe,UAAU,IAAI,WAAS;AAAA,QAC1C,IAAI,KAAK;AAAA,QACT,OAAO,KAAK;AAAA,MACd,EAAE;AACF,WAAK,eAAe,KAAK,uBAAuB,gBAAgB,YAAY;AAC5E,WAAK,oBAAoB,IAAI,yBAAyB,YAAY;AAClE,iBAAW,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAqB,sBAAsB,GAAM,kBAAqB,mBAAmB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,WAAW,CAAC;AAAA,IACtO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,MAAM,GAAG,CAAC,sBAAsB,IAAI,GAAG,OAAO,iBAAiB,QAAQ,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,SAAS,mBAAmB,GAAG,CAAC,eAAe,cAAc,GAAG,CAAC,mBAAmB,IAAI,GAAG,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,SAAS,yBAAyB,eAAe,IAAI,0BAA0B,cAAc,GAAG,mBAAmB,sBAAsB,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,mBAAmB,IAAI,GAAG,OAAO,GAAG,CAAC,eAAe,IAAI,0BAA0B,cAAc,GAAG,WAAW,iBAAiB,GAAG,sBAAsB,iBAAiB,GAAG,CAAC,SAAS,kBAAkB,WAAW,IAAI,GAAG,WAAW,eAAe,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,WAAW,IAAI,GAAG,kBAAkB,GAAG,WAAW,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,kBAAkB,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,iBAAiB,IAAI,GAAG,aAAa,GAAG,CAAC,SAAS,eAAe,QAAQ,IAAI,GAAG,CAAC,sBAAsB,IAAI,GAAG,aAAa,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC,mBAAmB,IAAI,GAAG,YAAY,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,QAAQ,aAAa,GAAG,kBAAkB,GAAG,CAAC,SAAS,SAAS,GAAG,WAAW,GAAG,CAAC,mBAAmB,IAAI,GAAG,SAAS,UAAU,CAAC;AAAA,MACltC,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,cAAc,EAAE,GAAG,UAAU,CAAC;AACrF,UAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,qBAAqB,CAAC;AAC3C,UAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,UAAU,CAAC;AACxE,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,UAAG,WAAW,IAAI,oCAAoC,GAAG,GAAG,OAAO,CAAC;AACpE,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,sBAAsB,GAAG,GAAG;AAC5E,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,GAAG,IAAI,iBAAiB,CAAC;AACrE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAW,IAAI,YAAY,EAAE,gBAAgB,IAAI,QAAQ;AAAA,QACzE;AAAA,MACF;AAAA,MACA,cAAc,CAAM,kBAAqB,SAAY,SAAY,aAAgB,kBAAqB,SAAY,eAAkB,mBAAsB,uBAA0B,0BAA6B,uBAA0B,wBAA2B,oBAAoB,0BAA6B,WAAc,aAAa;AAAA,MAClV,QAAQ,CAAC,6nCAA6nC;AAAA,MACtoC,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,m2BAAm2B;AAAA,IAC92B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kBAAkB,CAAC;AAAA,EACvB,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AACb,CAAC;AACD,IAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuB1B,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,KAAK,YAAY,MAAM,yBAAyB;AAAA,MACnE,SAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,QAAQ;AAAA,YACN,IAAI;AAAA,UACN;AAAA,UACA,OAAO;AAAA,YACL,OAAO,CAAC,aAAa,OAAO;AAAA,UAC9B;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ,eAAe,UAAU;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,CAAC,EAAE,uBAAuB,EAAE,UAAU,UAAQ,KAAK,OAAO,KAAK;AAAA,EACjE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,WAAW,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,0BAA0B,CAAC;AAAA,MACxC,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,MAAM,6BAA6B,GAAG,OAAO,GAAG,CAAC,MAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,MAAM,SAAS,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,YAAY,GAAG,WAAW,iBAAiB,GAAG,CAAC,MAAM,SAAS,GAAG,SAAS,GAAG,CAAC,MAAM,aAAa,GAAG,SAAS,GAAG,CAAC,GAAG,gBAAgB,GAAG,YAAY,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,UAAU,CAAC;AAAA,MAC9V,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,oBAAoB,CAAC;AAC1C,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,aAAa;AACxF,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,aAAa;AACxF,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,kBAAkB,CAAC;AACxC,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,WAAW,IAAI,qDAAqD,GAAG,GAAG,aAAa;AAC1F,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,qDAAqD,GAAG,GAAG,aAAa;AAC1F,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,kBAAkB,CAAC;AACzC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,WAAW,IAAI,qDAAqD,GAAG,GAAG,aAAa;AAC1F,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAY,YAAY,GAAG,GAAG,IAAI,aAAa,CAAC;AAC9D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,GAAG,IAAI,aAAa,CAAC;AAC7D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,GAAG,IAAI,aAAa,CAAC,EAAE,mBAAmB,IAAI;AACtF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,GAAG,IAAI,mBAAmB,CAAC,EAAE,mBAAmB,IAAI;AAC5F,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,aAAa,CAAC;AAC9D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAc,YAAY,IAAI,IAAI,iBAAiB,CAAC;AAAA,QACpE;AAAA,MACF;AAAA,MACA,cAAc,CAAM,kBAAuB,YAAe,wBAA2B,0BAA6B,qBAAwB,2BAA8B,WAAc,eAAkB,aAAgB,kBAAkB;AAAA,MAC1O,QAAQ,CAAC,wIAAwI;AAAA,MACjJ,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,0GAA0G;AAAA,IACrH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,2BAA2B;AAAA,MAC1C,SAAS,CAAC,YAAY,YAAY;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY,YAAY;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,YAAY;AAAA,MAClC,cAAc,CAAC,2BAA2B;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAY7B,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,cAAc,IAAI,gBAAgB,WAAW,UAAU;AAC5D,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,UAAM,gBAAgB,KAAK,YAAY,SAAS,iBAAiB,EAAE,uBAAuB,EAAE,UAAU,UAAQ,KAAK,cAAc,uBAAuB,MAAS;AACjK,UAAM,WAAW,KAAK,YAAY,OAAO,QAAQ,EAAE,UAAU,UAAQ,KAAK,OAAO;AACjF,UAAM,cAAc,KAAK,YAAY,KAAK,qBAAqB,CAAC;AAChE,SAAK,WAAW,cAAc,aAAa,eAAe,QAAQ,EAAE,KAAK,UAAU,CAAC,CAAC,YAAY,cAAc,OAAO,MAAM,KAAK,SAAS,KAAK,UAAU,KAAK,GAAG,UAAU,aAAW,KAAK,YAAY,MAAM,2BAA2B;AAAA,MACtO,OAAO,CAAC,UAAU;AAAA,MAClB;AAAA,IACF,CAAC,EAAE,UAAU,UAAQ,KAAK,aAAa,EAAE,KAAK,IAAI,aAAW;AAC3D,YAAM,gBAAgB,eAAe,WAAW,aAAa,WAAW;AACxE,YAAM,SAAS,GAAG,QAAQ,QAAQ,IAAI,QAAQ,MAAM;AACpD,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,QAAQ,KAAK,OAAK,EAAE,SAAS,UAAU,GAAG,QAAQ,IAAI,WAAU,iCAClE,QADkE;AAAA,QAErE;AAAA,MACF,EAAE,KAAK,CAAC;AAAA,IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,EACR;AAAA,EACA,UAAU;AACR,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,WAAW,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,MACtC,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,gBAAgB,GAAG,OAAO,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,SAAS,SAAS,CAAC;AAAA,MACtL,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,aAAa,CAAC;AAC9B,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,GAAG,0CAA0C,IAAI,IAAI,OAAO,CAAC;AAC3E,UAAG,OAAO,GAAG,OAAO;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAc,YAAY,GAAG,GAAG,IAAI,QAAQ,CAAC;AAC3D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,GAAG,GAAG,IAAI,WAAW,CAAC;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,cAAc,CAAM,kBAAqB,MAAS,gBAAmB,WAAc,aAAa;AAAA,MAChG,QAAQ,CAAC,mHAAmH;AAAA,MAC5H,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,kGAAkG;AAAA,IAC7G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAY1B,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,QAAQ,oBAAI,KAAK;AACtB,SAAK,YAAY,IAAI,MAAK,oBAAI,KAAK,GAAE,QAAQ,KAAK,MAAM,QAAQ,IAAI,CAAC,CAAC;AACtE,SAAK,aAAa,IAAI,gBAAgB;AAAA,MACpC,WAAW;AAAA,MACX,MAAM,KAAK;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,SAAK,aAAa,KAAK,WAAW,KAAK,qBAAqB,GAAG,IAAI,gBAAc;AAAA,MAC/E,WAAO,aAAAA,SAAM,UAAU,IAAI,EAAE,QAAQ,UAAU,SAAS,EAAE,OAAO;AAAA,MACjE,SAAK,aAAAA,SAAM,UAAU,IAAI,EAAE,MAAM,UAAU,SAAS,EAAE,OAAO;AAAA,IAC/D,EAAE,GAAG,YAAY,CAAC,CAAC;AACnB,UAAM,gBAAgB,KAAK,WAAW,KAAK,UAAY,CAAC;AAAA,MACtD;AAAA,MACA;AAAA,IACF,MAAM,KAAK,YAAY,MAAM,yBAAyB;AAAA,MACpD,OAAO,MAAM,YAAY;AAAA,MACzB,KAAK,IAAI,YAAY;AAAA,IACvB,CAAC,EAAE,uBAAuB,EAAE,UAAU,UAAQ,KAAK,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC;AAC3E,SAAK,mBAAmB,cAAc,KAAK,IAAI,SAAO,IAAI,UAAU,CAAC;AACrE,SAAK,mBAAmB,cAAc,KAAK,IAAI,SAAO,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,QAAQ,MAAM,cAAc,CAAC,CAAC,CAAC;AACxH,SAAK,gBAAgB,KAAK,YAAY,SAAS,iBAAiB,EAAE,uBAAuB,EAAE,UAAU,UAAQ,KAAK,cAAc,uBAAuB,MAAS;AAAA,EAClK;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,WAAW,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,0BAA0B,CAAC;AAAA,MACxC,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,SAAS,QAAQ,GAAG,MAAM,GAAG,CAAC,SAAS,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,gBAAgB,GAAG,OAAO,GAAG,CAAC,GAAG,YAAY,CAAC;AAAA,MACpQ,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,OAAO,IAAI,gBAAgB;AAC9B,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,UAAG,WAAW,IAAI,6CAA6C,IAAI,IAAI,OAAO,CAAC;AAC/E,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,WAAW,IAAI,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAC7E,UAAG,OAAO,IAAI,OAAO;AACrB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,GAAG,GAAG,IAAI,gBAAgB,CAAC;AAC/D,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,GAAG,GAAG,wBAAwB,CAAC;AACnE,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAO,YAAY,IAAI,IAAI,IAAI,gBAAgB,GAAM,YAAY,IAAI,IAAI,IAAI,aAAa,KAAK,MAAS,GAAG,GAAG;AAC5J,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,6BAA6B,CAAC;AAC1E,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,UAAU,CAAC;AAC5D,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,IAAI,IAAI,IAAI,UAAU,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,cAAc,CAAI,MAAS,WAAc,eAAkB,gBAAmB,kBAAkB;AAAA,MAChG,QAAQ,CAAC,0iBAA0iB;AAAA,MACnjB,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,uaAAua;AAAA,IAClb,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,2BAA2B;AAAA,MAC1C,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,cAAc,CAAC,2BAA2B;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kCAAkC,CAAC;AAAA,EACvC,IAAI;AAAA,EACJ,OAAO;AACT,GAAG;AAAA,EACD,IAAI;AAAA,EACJ,OAAO;AACT,GAAG;AAAA,EACD,IAAI;AAAA,EACJ,OAAO;AACT,CAAC;AACD,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,IACP,OAAO,OAAO,mBAAmB;AAAA,IACjC,iBAAiB,CAAC,GAAG,GAAG,EAAE;AAAA,IAC1B,eAAe,MAAM;AAAA,IACrB,qBAAqB,CAAC,WAAW,SAAS;AAAA,EAC5C;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,OAAO,0BAA0B;AAAA,IACxC,eAAe,MAAM;AAAA,IACrB,iBAAiB,CAAC,GAAG,GAAG,GAAG,EAAE;AAAA,IAC7B,qBAAqB,CAAC,WAAW,SAAS;AAAA,EAC5C;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,OAAO,yBAAyB;AAAA,IACvC,eAAe,MAAM;AAAA,IACrB,iBAAiB,CAAC,GAAG,GAAG,EAAE;AAAA,IAC1B,qBAAqB,CAAC,WAAW,SAAS;AAAA,EAC5C;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,wBAAwB;AAClC,WAAO,QAAQ,eAAe,EAAE,IAAI,CAAC,CAAC,IAAI,MAAM,MAAM;AACpD,UAAI,CAAC,uBAAuB,cAAc,EAAE,GAAG;AAC7C,+BAAuB,eAAe,IAAI,MAAM;AAAA,MAClD;AAAA,IACF,CAAC;AACD,QAAI,uBAAuB,iBAAiB,EAAE,WAAW,GAAG;AAC1D,6BAAuB,iBAAiB,+BAA+B;AAAA,IACzE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAY,sBAAsB,CAAC;AAAA,IAC1F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,oBAAoB,0BAA0B,yBAAyB;AAAA,MACtF,SAAS,CAAC,cAAmB,YAAY;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,cAAc,aAAa,SAAS,eAAe,CAAC;AAAA,IAChE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,aAAa,SAAS,eAAe,CAAC;AAAA,MAC9D,cAAc,CAAC,oBAAoB,0BAA0B,yBAAyB;AAAA,IACxF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,GAAG;AACxB,UAAG,OAAO,GAAG,wBAAwB;AACrC,UAAG,aAAa;AAAA,QAClB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,mBAAmB;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,mBAAmB;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,QAAQ,aAAa,EAAE;AAC5B,SAAK,sBAAsB,aAAa,EAAE;AAC1C,SAAK,cAAc,aAAa,EAAE;AAAA,EACpC;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB,KAAK,YAAY,cAAc,uBAAuB,EAAE,UAAU,UAAQ,KAAK,uBAAuB,IAAI;AAAA,EAClI;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,WAAW,CAAC;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,SAAS,cAAc,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,MAAM,GAAG,MAAM,CAAC;AAAA,MAChI,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,OAAO,CAAC;AACtE,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,YAAY,CAAC;AAC7B,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAW,YAAY,GAAG,GAAG,IAAI,cAAc,CAAC;AAAA,QAChE;AAAA,MACF;AAAA,MACA,cAAc,CAAM,kBAAqB,MAAS,WAAc,WAAW;AAAA,MAC3E,QAAQ,CAAC,0HAA0H;AAAA,MACnI,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,8FAA8F;AAAA,IACzG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,sBAAsB;AAAA,MACrC,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,cAAc,CAAC,sBAAsB;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["dayjs"]}