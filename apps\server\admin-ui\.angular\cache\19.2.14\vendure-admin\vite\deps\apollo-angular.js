import {
  APOLL<PERSON>_FLAGS,
  APOLLO_NAMED_OPTIONS,
  APOLLO_OPTIONS,
  Apollo,
  ApolloBase,
  Mutation,
  Query,
  QueryRef,
  Subscription,
  gql,
  provideApollo,
  provideNamedApollo
} from "./chunk-4A7K6JDB.js";
import "./chunk-JNMUMDNO.js";
import "./chunk-OKFFUXD2.js";
import "./chunk-755OWGIU.js";
import "./chunk-WKNUSL3B.js";
import "./chunk-TXDUYLVM.js";
export {
  APOLLO_FLAGS,
  APOLLO_NAMED_OPTIONS,
  APOLLO_OPTIONS,
  Apollo,
  ApolloBase,
  Mutation,
  Query,
  QueryRef,
  Subscription,
  gql,
  provideApollo,
  provideNamedApollo
};
